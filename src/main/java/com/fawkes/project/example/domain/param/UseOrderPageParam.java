package com.fawkes.project.example.domain.param;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;

/**
 * 设备领用订单-分页查询参数
 *
 * <AUTHOR>
 */
@Data
@ToString
@NoArgsConstructor
public class UseOrderPageParam extends PageParam {
    @ApiModelProperty(value = "订单编号")
    private String orderNo;

    @ApiModelProperty(value = "订单名称")
    private String orderName;

    @ApiModelProperty(value = "订单责任人")
    @JsonSerialize(using = ToStringSerializer.class)
    private Long dutyUserId;
}
