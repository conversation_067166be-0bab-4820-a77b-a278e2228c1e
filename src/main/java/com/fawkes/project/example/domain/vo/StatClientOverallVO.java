package com.fawkes.project.example.domain.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;

import java.io.Serializable;
import java.util.List;

/**
 * 客户关系看板-客户分布VO
 *
 * <AUTHOR>
 */
@Data
@ToString
@NoArgsConstructor
public class StatClientOverallVO implements Serializable {
    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "订单金额占比")
    List<LabelValueExtraVO> orderAmountPercentList;

    @ApiModelProperty(value = "订单数量占比")
    List<LabelValueExtraVO> orderQuantityPercentList;

    @ApiModelProperty(value = "客户分布详情")
    List<ClientVO> clientList;

    @ApiModelProperty(value = "销售金额排名")
    List<ClientVO> clientSaleAmountTop5;
}
