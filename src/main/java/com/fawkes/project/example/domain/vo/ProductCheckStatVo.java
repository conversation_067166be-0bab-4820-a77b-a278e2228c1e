package com.fawkes.project.example.domain.vo;

import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;

/**
 * 产品检验统计Vo类
 *
 * <AUTHOR>
 */
@Data
@ToString
@NoArgsConstructor
public class ProductCheckStatVo {

    /**
     * 已检验产品数量
     */
    private Long checkedObjNum;

    /**
     * 未检验产品数量
     */
    private Long nonCheckedObjNum;


    /**
     * 已检验订单数
     */
    private Long checkedOrderNum;

    /**
     * 检验中订单数
     */
    private Long checkingOrderNum;

    /**
     * 未检验订单数
     */
    private Long nonCheckedOrderNum;

}
