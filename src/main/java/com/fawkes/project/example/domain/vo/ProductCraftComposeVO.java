package com.fawkes.project.example.domain.vo;

import com.fawkes.project.example.common.model.ProductCraftCompose;
import lombok.Data;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 */
@Data
public class ProductCraftComposeVO extends ProductCraftCompose {

    /**
     * 配件编号
     */
    private String accessoryCode;

    /**
     * 配件名称
     */
    private String accessoryName;

    /**
     * 配件当前单价
     */
    private BigDecimal curPrice;
}
