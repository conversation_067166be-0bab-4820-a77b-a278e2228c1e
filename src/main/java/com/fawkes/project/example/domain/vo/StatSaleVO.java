package com.fawkes.project.example.domain.vo;

import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;

import java.util.List;

/**
 * 统计销售数量
 *
 * <AUTHOR>
 */
@Data
@ToString
@NoArgsConstructor
public class StatSaleVO<T> {
    private String clientType;

    private String clientTypeName;

    private T totalNum;

    private List<StatSaleDetailVO<T>> children;

    public StatSaleVO(String clientType, String clientTypeName, T totalNum) {
        this.clientType = clientType;
        this.clientTypeName = clientTypeName;
        this.totalNum = totalNum;
    }
}
