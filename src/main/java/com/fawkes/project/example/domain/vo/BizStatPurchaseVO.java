package com.fawkes.project.example.domain.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.ToString;

import java.math.BigDecimal;

/**
 * 收款统计
 *
 * <AUTHOR>
 */
@Data
@ToString
public class BizStatPurchaseVO {
    @ApiModelProperty(value = "累计采购总总额")
    private BigDecimal orderAmount;

    @ApiModelProperty(value = "采购订单签订数量")
    private Integer orderQuantity;

    @ApiModelProperty(value = "供应商数量")
    private Integer supplierQuantity;

    public BizStatPurchaseVO() {
        this.orderAmount = new BigDecimal("0");
        this.orderQuantity = 0;
        this.supplierQuantity = 0;
    }
}
