package com.fawkes.project.example.domain.vo;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;

/**
 * 配件使用情况Vo类
 *
 * <AUTHOR>
 */
@Data
@ToString
@NoArgsConstructor
public class AccessoryUseInfoVo {

    /**
     * 配件ID
     */
    @ApiModelProperty(value = "配件ID")
    @JsonSerialize(using = ToStringSerializer.class)
    private Long accessoryId;

    /**
     * 配件编号
     */
    @ApiModelProperty(value = "配件编号")
    private String accessoryNo;

    /**
     * 配件名称
     */
    @ApiModelProperty(value = "配件名称")
    private String accessoryName;

    /**
     * 规格型号
     */
    @ApiModelProperty(value = "规格型号")
    private String accessorySpec;

    /**
     * 配件类型
     */
    @ApiModelProperty(value = "配件类型")
    private String accessoryType;

    /**
     * 配件数量
     */
    @ApiModelProperty(value = "配件数量")
    private Integer quantity;

    @ApiModelProperty(value = "库存数量")
    private Integer inventoryQuantity;

    @ApiModelProperty(value = "损耗责任人")
    private String lossUserName;

    public AccessoryUseInfoVo(Long accessoryId, String accessoryNo, String accessoryName, String accessorySpec, String accessoryType, Integer quantity) {
        this.accessoryId = accessoryId;
        this.accessoryNo = accessoryNo;
        this.accessoryName = accessoryName;
        this.accessorySpec = accessorySpec;
        this.accessoryType = accessoryType;
        this.quantity = quantity;
    }

    public AccessoryUseInfoVo(Long accessoryId, String accessoryNo, String accessoryName, String accessorySpec, String accessoryType, Integer quantity, Integer inventoryQuantity) {
        this.accessoryId = accessoryId;
        this.accessoryNo = accessoryNo;
        this.accessoryName = accessoryName;
        this.accessorySpec = accessorySpec;
        this.accessoryType = accessoryType;
        this.quantity = quantity;
        this.inventoryQuantity = inventoryQuantity;
    }
}
