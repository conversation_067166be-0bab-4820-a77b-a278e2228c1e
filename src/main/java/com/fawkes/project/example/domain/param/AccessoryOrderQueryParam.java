package com.fawkes.project.example.domain.param;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDate;

/**
 * <AUTHOR>
 */
@Data
public class AccessoryOrderQueryParam extends PageParam {
    /*********** 配件明细 - 配件入库订单 条件搜索 ***********/
    @ApiModelProperty(value = "支付状态： 0 - 不区分；1 - 未完全支付；2 - 完全支付")
    private Integer paidStatus;

    @ApiModelProperty(value = "订单最早日期：yyyy-MM-dd")
    private LocalDate startDate;

    @ApiModelProperty(value = "订单最晚日期：yyyy-MM-dd")
    private LocalDate endDate;

    /*********** 经营统计 - 付款统计 - 订单统计 条件搜索  ***********/
    @ApiModelProperty(value = "供应商名称")
    private String accessorySupplier;

    @ApiModelProperty(value = "责任人id")
    @JsonSerialize(using = ToStringSerializer.class)
    private Long orderPersonLiableId;

    @ApiModelProperty(value = "订单编号")
    private String orderNo;

    @ApiModelProperty(value = "订单名称")
    private String orderName;

    @ApiModelProperty(value = "采购总金额-最低")
    private BigDecimal totalPurchasePriceMin;

    @ApiModelProperty(value = "采购总金额-最高")
    private BigDecimal totalPurchasePriceMax;

    @ApiModelProperty(value = "订单日期-最早")
    private LocalDate orderDateStart;

    @ApiModelProperty(value = "订单日期-最晚")
    private LocalDate orderDateEnd;

    @ApiModelProperty(value = "交付日期-最早")
    private LocalDate orderDeliveryDateStart;

    @ApiModelProperty(value = "交付日期-最晚")
    private LocalDate orderDeliveryDateEnd;
}
