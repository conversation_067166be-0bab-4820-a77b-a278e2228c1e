package com.fawkes.project.example.domain.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import com.fawkes.project.example.common.model.CheckCalib;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;

import java.util.Date;
import java.util.List;

/**
 * 检验明细Vo类
 *
 * <AUTHOR>
 */
@Data
@ToString
@NoArgsConstructor
public class CheckDetailVo {

    /**
     * 产品ID
     */
    @JsonSerialize(using = ToStringSerializer.class)
    private Long objectId;

    /**
     * 产品编号
     */
    private String objectNo;

    /**
     * 产品ID
     */
    @JsonSerialize(using = ToStringSerializer.class)
    private Long productId;

    /**
     * 产品型号
     */
    private String productModel;

    /**
     * 产品名称
     */
    private String productName;

    /**
     * 状态
     */
    private Integer status;

    /**
     * 工艺版本号
     */
    private String craftCode;

    /**
     * 预计完成时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date expectCheckFinishTime;

    /**
     * 实际完成时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date actualFinishTime;

    /**
     * 不合格原因
     */
    private String unqualifiedReason;

    /**
     * 不合格图片
     */
    private String unqualifiedPic;

    /**
     * 附件
     */
    private String attach;

    /**
     * 订单编号
     */
    private String orderNo;

    private List<CheckCalib> checkCalibs;

}
