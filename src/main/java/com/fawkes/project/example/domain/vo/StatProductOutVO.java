package com.fawkes.project.example.domain.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fawkes.project.example.common.constants.CommonConstants;
import com.fawkes.project.example.common.model.UseOrder;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;

import java.time.ZoneId;
import java.util.Date;

/**
 * 经营统计 - 产品出入库明细 - 产品出库
 *
 * <AUTHOR>
 */
@Data
@ToString
@NoArgsConstructor
public class StatProductOutVO {
    @ApiModelProperty(value = "订单日期")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date orderTime;

    @ApiModelProperty(value = "订单编号")
    private String orderNo;

    @ApiModelProperty(value = "合同名称")
    private String orderName;

    @ApiModelProperty(value = "出库形式")
    private String outType;

    @ApiModelProperty(value = "计划销售（领用）数量")
    private Long planNum;

    @ApiModelProperty(value = "退货数量")
    private Long rejectNum;

    @ApiModelProperty(value = "换货数量")
    private Long exchangeNum;

    @ApiModelProperty(value = "订单负责人")
    private String dutyUserName;

    public StatProductOutVO(UseOrder useOrder) {
        this.orderTime = useOrder.getCreateDate();
        this.orderNo = useOrder.getOrderNo();
        this.orderName = useOrder.getOrderName();
        this.dutyUserName = useOrder.getDutyUserName();
        this.outType = CommonConstants.PRODUCT_ORDER_TYPE_OUT_USE;
    }

    public StatProductOutVO(SaleOrderVO saleOrderVO) {
        this.orderTime = Date.from(saleOrderVO.getSignDate().atStartOfDay(ZoneId.systemDefault()).toInstant());
        this.orderNo = saleOrderVO.getOrderNo();
        this.orderName = saleOrderVO.getOrderName();
        this.dutyUserName = saleOrderVO.getLiableUserName();
        this.planNum = saleOrderVO.getSaleQuantity();
        this.outType = CommonConstants.PRODUCT_ORDER_TYPE_OUT_SALE;
    }
}
