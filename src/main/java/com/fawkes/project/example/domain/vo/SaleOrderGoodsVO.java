package com.fawkes.project.example.domain.vo;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import com.fawkes.project.example.common.model.SaleOrderGoods;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;

import java.util.List;


/**
 * 销售订单-销售明细
 *
 * <AUTHOR>
 */
@Data
@ToString
@NoArgsConstructor
public class SaleOrderGoodsVO extends SaleOrderGoods {

    @ApiModelProperty(value = "产品id")
    @JsonSerialize(using = ToStringSerializer.class)
    private Long productId;

    @ApiModelProperty(value = "产品名称")
    private String productName;

    @ApiModelProperty(value = "产品型号")
    private String productModel;

    @ApiModelProperty(value = "产品参数名称")
    private String paramName;

    @ApiModelProperty(value = "工艺版本号")
    private String craftCode;

    @ApiModelProperty(value = "应用行业")
    private String industry;

    @ApiModelProperty(value = "所含工艺")
    private List<SaleOrderGoodsVO> children;

    public String getDetail() {
        return "{产品名称='" + productName + '\'' +
                ", 产品参数='" + paramName + '\'' +
                ", 工艺版本号='" + craftCode + '\'' +
                ", 销售数量='" + getSaleQuantity() + '\'' +
                ", 含税售价=" + getSalePrice() + "（元）" +
                '}';
    }
}
