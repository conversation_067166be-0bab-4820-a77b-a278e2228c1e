package com.fawkes.project.example.domain.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fawkes.project.example.common.constants.CommonConstants;
import com.fawkes.project.example.common.model.ProductOrder;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;

import java.util.Date;

/**
 * 经营统计 - 产品出入库明细 - 产品入库
 *
 * <AUTHOR>
 */
@Data
@ToString
@NoArgsConstructor
public class StatProductInVO {
    @ApiModelProperty(value = "订单日期")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date orderTime;

    @ApiModelProperty(value = "订单编号")
    private String orderNo;

    @ApiModelProperty(value = "生产责任人名称")
    private String produceUserName;

    @ApiModelProperty(value = "入库形式")
    private String inType = CommonConstants.PRODUCT_ORDER_TYPE_IN_PRODUCE;

    @ApiModelProperty(value = "合格品数量")
    private Long qualifiedNum;

    @ApiModelProperty(value = "次品数量")
    private Long defectiveNum;

    @ApiModelProperty(value = "废品数量")
    private Long wasteNum;

    public StatProductInVO(ProductOrder order) {
        this.orderTime = order.getOrderTime();
        this.orderNo = order.getOrderNo();
        this.produceUserName = order.getProduceUserName();
    }
}
