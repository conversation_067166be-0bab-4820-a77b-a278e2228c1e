package com.fawkes.project.example.domain.param;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;

/**
 * 产品工艺数量查询类
 *
 * <AUTHOR>
 */
@Data
@ToString
@NoArgsConstructor
public class ProductCraftQo {

    /**
     * 工艺ID
     */
    @ApiModelProperty(value = "工艺ID")
    @JsonSerialize(using = ToStringSerializer.class)
    private Long craftId;

    /**
     * 数量
     */
    private Integer num;

    @ApiModelProperty(value = "产品名称")
    private String productName;

    @ApiModelProperty(value = "产品型号")
    private String productModel;

    @ApiModelProperty(value = "工艺版本号")
    private String craftCode;

}
