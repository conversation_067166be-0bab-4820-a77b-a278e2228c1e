package com.fawkes.project.example.domain.vo;

import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;

/**
 * 产品订单统计Vo类
 *
 * <AUTHOR>
 */
@Data
@ToString
@NoArgsConstructor
public class ProductOrderProductStatVo {

    /**
     * 产品名称
     */
    private String productName;

    /**
     * 生产数量
     */
    private Integer num;

    public ProductOrderProductStatVo(String productName, Integer num) {
        this.productName = productName;
        this.num = num;
    }
}
