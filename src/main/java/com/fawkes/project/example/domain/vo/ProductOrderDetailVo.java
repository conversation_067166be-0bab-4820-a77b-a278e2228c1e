package com.fawkes.project.example.domain.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fawkes.project.example.common.model.ProductObject;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;

import java.util.Date;
import java.util.List;

/**
 * 产品订单详情Vo类
 *
 * <AUTHOR>
 */
@Data
@ToString
@NoArgsConstructor
public class ProductOrderDetailVo {

    /**
     * 订单编号
     */
    private String orderNo;

    /**
     * 订单日期
     */
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date orderTime;

    /**
     * 生产年份
     */
    @JsonFormat(pattern = "yyyy", timezone = "GMT+8")
    private Integer orderYear;

    /**
     * 生产批次
     */
    private String orderBatch;

    /**
     * 生产责任人名称
     */
    private String produceUserName;

    /**
     * 检验责任人名称
     */
    private String checkUserName;

    /**
     * 预计检验完成时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date expectCheckFinishTime;

    /**
     * 产品明细
     */
    private List<ProductObject> products;

    /**
     * 配件使用情况
     */
    private List<AccessoryUseInfoVo> accessoryUseInfoVos;

    /**
     * 配件损耗情况
     */
    private List<AccessoryUseInfoVo> losses;

}
