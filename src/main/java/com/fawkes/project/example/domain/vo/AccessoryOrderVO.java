package com.fawkes.project.example.domain.vo;

import com.fawkes.project.example.common.constants.CommonConstants;
import com.fawkes.project.example.common.model.AccessoryOrder;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;

/**
 * <AUTHOR>
 */
@Data
@ToString
@NoArgsConstructor
public class AccessoryOrderVO extends AccessoryOrder implements Serializable {
    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "订单类型：采购入库、产品生产")
    private String orderType = CommonConstants.ORDER_TYPE_CGRK;

    @ApiModelProperty(value = "总数量")
    private Integer totalPurchaseQuantity;

    @ApiModelProperty(value = "累计到货数量")
    private Integer totalArrivalQuantity;

    @ApiModelProperty(value = "累计总金额")
    private BigDecimal totalPurchasePrice;

    @ApiModelProperty(value = "本次支付金额")
    private BigDecimal curPayAmount;

    @ApiModelProperty(value = "配件明细")
    private List<AccessoryOrderGoodsVO> goodsList;
}
