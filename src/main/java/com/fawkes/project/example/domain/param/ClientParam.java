package com.fawkes.project.example.domain.param;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 客户管理条件搜索
 *
 * <AUTHOR>
 */
@Data
public class ClientParam extends PageParam {

    @ApiModelProperty(value = "客户名称")
    private String clientName;

    @ApiModelProperty(value = "客户编号")
    private String clientNo;

    @ApiModelProperty(value = "对接负责人名称")
    private String userInCharge;

    @ApiModelProperty(value = "客户区域：省")
    private String province;

    @ApiModelProperty(value = "客户区域：市")
    private String city;

    @ApiModelProperty(value = "客户区域：区")
    private String region;

    @ApiModelProperty(value = "客户类型：YNKH、JTKH、SCKH、CPDLS")
    private String clientType;

    @ApiModelProperty(value = "行业类型：海上风电等")
    private String industry;

    @ApiModelProperty(value = "首次合作时间最小")
    private String firstDealDateStart;

    @ApiModelProperty(value = "首次合作时间最大")
    private String firstDealDateEnd;

    @ApiModelProperty(value = "最近合作时间范围最小")
    private String recentDealDateStart;

    @ApiModelProperty(value = "最近合作时间范围最大")
    private String recentDealDateEnd;

    public static ClientParam getQueryAllParam() {
        ClientParam param = new ClientParam();
        param.setPageNum(1);
        param.setPageSize(Integer.MAX_VALUE);
        return param;
    }
}
