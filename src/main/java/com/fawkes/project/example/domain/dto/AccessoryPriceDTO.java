package com.fawkes.project.example.domain.dto;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * 配件价格
 *
 * <AUTHOR>
 */
@Data
public class AccessoryPriceDTO implements Serializable {
    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "配件ID")
    @JsonSerialize(using = ToStringSerializer.class)
    private Long accessoryId;

    @ApiModelProperty(value = "总数")
    private Integer totalQuantity;

    @ApiModelProperty(value = "总价")
    private BigDecimal totalPrice;

    @ApiModelProperty(value = "单价")
    private BigDecimal curPrice;
}
