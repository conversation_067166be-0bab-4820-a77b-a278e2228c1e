package com.fawkes.project.example.domain.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;

import java.math.BigDecimal;

/**
 * 逐月统计收款量、销售额
 *
 * <AUTHOR>
 */
@Data
@ToString
@NoArgsConstructor
public class StatSaleOrderVO {

    @ApiModelProperty(value = "统计月份")
    private Integer month;

    @ApiModelProperty(value = "收款量")
    private BigDecimal paymentAmount;

    @ApiModelProperty(value = "销售额")
    private BigDecimal saleAmount;
}
