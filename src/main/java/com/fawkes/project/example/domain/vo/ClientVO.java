package com.fawkes.project.example.domain.vo;

import com.fawkes.project.example.common.model.Client;
import com.fawkes.project.example.common.model.ClientContacts;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.List;

/**
 * <AUTHOR>
 */
@Data
public class ClientVO extends Client {

    @ApiModelProperty(value = "首次合作时间")
    private LocalDate firstDealDate;

    @ApiModelProperty(value = "最近合作时间")
    private LocalDate recentDealDate;

    @ApiModelProperty(value = "销售次数")
    private Integer orderQuantity;

    @ApiModelProperty(value = "累计销售金额")
    private BigDecimal saleAmount;

    @ApiModelProperty(value = "销售金额占比")
    private String saleAmountPercent;

    @ApiModelProperty(value = "对接负责人列表")
    private List<ClientContacts> userInChargeList;

    @ApiModelProperty(value = "联系人列表")
    private List<ClientContacts> contactsList;

    @ApiModelProperty(value = "涉及的行业类型")
    private List<String> industryList;

    public ClientVO() {
        this.orderQuantity = 0;
        this.saleAmount = new BigDecimal("0");
    }

    public ClientVO(String clientName, String clientType, BigDecimal saleAmount, LocalDate recentDealDate) {
        super.setClientName(clientName);
        super.setClientType(clientType);
        this.saleAmount = saleAmount;
        this.recentDealDate = recentDealDate;
    }
}
