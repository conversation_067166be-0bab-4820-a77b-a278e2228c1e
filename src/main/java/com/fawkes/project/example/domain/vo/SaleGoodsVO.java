package com.fawkes.project.example.domain.vo;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;

/**
 * 产品交付-树表
 *
 * <AUTHOR>
 */
@Data
@ToString
@NoArgsConstructor
public class SaleGoodsVO implements Serializable {
    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "产品名称")
    private String productName;

    @ApiModelProperty(value = "产品型号")
    private String productModel;

    @ApiModelProperty(value = "工艺id")
    @JsonSerialize(using = ToStringSerializer.class)
    private Long craftId;

    @ApiModelProperty(value = "工艺版本号")
    private String craftCode;

    @ApiModelProperty(value = "计划销售数量")
    private Integer saleQuantity;

    @ApiModelProperty(value = "已交付数量")
    private Integer outQuantity;

    @ApiModelProperty(value = "未交付数量")
    private Integer unOutQuantity;

    @ApiModelProperty(value = "售价（单价）")
    private BigDecimal unitSalePrice;

    @ApiModelProperty(value = "退换货数量")
    private Integer returnQuantity;

    @ApiModelProperty(value = "退货数量")
    private Integer rejectQuantity;

    @ApiModelProperty(value = "换货数量")
    private Integer exchangeQuantity;

    @ApiModelProperty(value = "实体对象列表")
    private List<SaleProductObjVO> objList;
}
