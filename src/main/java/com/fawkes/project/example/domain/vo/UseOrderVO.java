package com.fawkes.project.example.domain.vo;

import com.fawkes.project.example.common.model.UseOrder;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 */
@Data
@ToString
@NoArgsConstructor
public class UseOrderVO extends UseOrder implements Serializable {
    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "设备领用明细")
    List<UseOrderGoodsVO> goodsList;
}
