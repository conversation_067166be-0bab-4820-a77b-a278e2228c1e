package com.fawkes.project.example.domain.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;

import java.math.BigDecimal;

/**
 * 逐月统计设备成本、设备数量
 *
 * <AUTHOR>
 */
@Data
@ToString
@NoArgsConstructor
public class StatUseOrderVO {
    @ApiModelProperty(value = "统计月份")
    private Integer month;

    @ApiModelProperty(value = "设备成本")
    private BigDecimal useCost;

    @ApiModelProperty(value = "设备数量")
    private Integer useQuantity;

    public StatUseOrderVO(Integer month) {
        this.month = month;
        this.useCost = new BigDecimal("0");
        this.useQuantity = 0;
    }
}
