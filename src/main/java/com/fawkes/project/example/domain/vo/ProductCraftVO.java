package com.fawkes.project.example.domain.vo;

import com.fawkes.project.example.common.model.ProductCraft;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.util.List;

/**
 * <AUTHOR>
 */
@Data
public class ProductCraftVO extends ProductCraft {

    @ApiModelProperty(value = "产品名称")
    private String productName;

    @ApiModelProperty(value = "产品型号")
    private String productMode;

    @ApiModelProperty(value = "指导成本")
    private BigDecimal guideCost;

    @ApiModelProperty(value = "工艺明细")
    private List<ProductCraftComposeVO> composeList;
}
