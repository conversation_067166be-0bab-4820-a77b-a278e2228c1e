package com.fawkes.project.example.domain.param;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;

import java.util.List;

/**
 * 销售退货参数
 *
 * <AUTHOR>
 */
@Data
@ToString
@NoArgsConstructor
public class SaleReturnParam {

    @ApiModelProperty(value = "退换货列表")
    List<SaleReturnObj> objList;
    @ApiModelProperty(value = "销售订单id")
    @JsonSerialize(using = ToStringSerializer.class)
    private Long orderId;
    @ApiModelProperty(value = "退换类型: 退货、换货")
    private String returnType;
}
