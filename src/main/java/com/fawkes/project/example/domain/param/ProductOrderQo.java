package com.fawkes.project.example.domain.param;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;

/**
 * 产品订单查询类
 *
 * <AUTHOR>
 */
@Data
@ToString
@NoArgsConstructor
public class ProductOrderQo extends PageParam {

    /**
     * 订单编号
     */
    private String orderNo;

    /**
     * 生产年份（产品检验：实际完成时间）
     */
    private String orderYear;

    /**
     * 生产批次
     */
    private String orderBatch;

    /**
     * 生产责任人名称
     */
    @ApiModelProperty(value = "检验责任人名称")
    private String produceUserName;

    @ApiModelProperty(value = "检验责任人id")
    @JsonSerialize(using = ToStringSerializer.class)
    private Long checkUserId;

    /**
     * 生产责任人名称
     */
    @ApiModelProperty(value = "检验责任人名称")
    private String checkUserName;

    @ApiModelProperty(value = "订单检验状态: 0 - 未启动，1 - 检验中， 2 - 已完成")
    private Integer checkStatus;

    @ApiModelProperty(value = "产品编号")
    private String objectNo;

    /**
     * 订单开始时间
     */
    private String orderStartTime;

    /**
     * 订单结束时间
     */
    private String orderEndTime;

}
