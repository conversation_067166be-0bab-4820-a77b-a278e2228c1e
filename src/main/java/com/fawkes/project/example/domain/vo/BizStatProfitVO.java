package com.fawkes.project.example.domain.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.ToString;

import java.math.BigDecimal;

/**
 * 收款利润统计
 *
 * <AUTHOR>
 */
@Data
@ToString
public class BizStatProfitVO {

    @ApiModelProperty(value = "累计销售总额")
    private BigDecimal saleAmount;

    @ApiModelProperty(value = "已收款总额")
    private BigDecimal paymentAmount;

    /**
     * TODO 如何计算累计利润？
     */
    @ApiModelProperty(value = "累计利润")
    private BigDecimal profit;

    @ApiModelProperty(value = "已获取利润")
    private BigDecimal earnedProfit;

    @ApiModelProperty(value = "已完成合同数")
    private Long finishedOrder;

    @ApiModelProperty(value = "未完成合同数")
    private Long unfinishedOrder;

    @ApiModelProperty(value = "已结算客户数")
    private Long finishedClient;

    @ApiModelProperty(value = "未结算客户数")
    private Long unfinishedClient;

    public BizStatProfitVO() {
        this.saleAmount = new BigDecimal("0");
        this.paymentAmount = new BigDecimal("0");
        this.profit = new BigDecimal("0");
        this.earnedProfit = new BigDecimal("0");
        this.finishedOrder = 0L;
        this.unfinishedOrder = 0L;
        this.finishedClient = 0L;
        this.unfinishedClient = 0L;
    }

}
