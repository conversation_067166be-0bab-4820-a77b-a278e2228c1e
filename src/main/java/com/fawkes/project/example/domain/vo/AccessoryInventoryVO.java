package com.fawkes.project.example.domain.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 配件库存
 *
 * <AUTHOR>
 */
@Data
public class AccessoryInventoryVO extends AccessoryVO {

    /**
     * 库存数量：累计已到货数量
     */
    private Integer totalArrivalQuantity;

    /**
     * 库存数量：累计未到货数量
     */
    private Integer totalNotArrivalQuantity;

    /**
     * 库存数量：总使用数量
     */
    private Integer totalUseQuantity;

    @ApiModelProperty(value = "库存数量：总损耗数量")
    private Integer totalLossQuantity;

    /**
     * 库存数量
     */
    private Integer inventoryQuantity;

    /**
     * 零件种类
     */
    private String categoryCode;

    /**
     * 零件种类名称
     */
    private String categoryName;

    /**
     * 零件编号
     */
    private String refCode;
}
