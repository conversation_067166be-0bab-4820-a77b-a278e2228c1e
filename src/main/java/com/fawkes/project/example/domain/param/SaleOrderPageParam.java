package com.fawkes.project.example.domain.param;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.Year;

/**
 * 销售订单-分页查询参数
 *
 * <AUTHOR>
 */
@Data
@ToString
@NoArgsConstructor
public class SaleOrderPageParam extends PageParam {

    @ApiModelProperty(value = "订单编号")
    private String orderNo;

    @ApiModelProperty(value = "订单名称")
    private String orderName;

    @ApiModelProperty(value = "客户名称")
    private String clientName;

    @ApiModelProperty(value = "客户id")
    @JsonSerialize(using = ToStringSerializer.class)
    private Long clientId;

    @ApiModelProperty(value = "订单负责人id")
    @JsonSerialize(using = ToStringSerializer.class)
    private Long liableUserId;

    @ApiModelProperty(value = "合同状态：进行中、已完成")
    private String status;

    /*********** 收款利润统计 ***********/
    @ApiModelProperty(value = "签订日期-开始")
    private LocalDate signDateStart;

    @ApiModelProperty(value = "签订日期-结束")
    private LocalDate signDateEnd;

    @ApiModelProperty(value = "销售金额-最小")
    private BigDecimal salePriceMin;

    @ApiModelProperty(value = "销售金额-最大")
    private BigDecimal salePriceMax;

    private Year searchYear;

    public static SaleOrderPageParam getQueryAllParam() {
        SaleOrderPageParam param = new SaleOrderPageParam();
        param.setPageNum(1);
        param.setPageSize(Integer.MAX_VALUE);
        return param;
    }
}
