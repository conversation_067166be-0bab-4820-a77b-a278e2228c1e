package com.fawkes.project.example.domain.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.ToString;

import java.math.BigDecimal;

/**
 * 收款统计
 *
 * <AUTHOR>
 */
@Data
@ToString
public class BizStatPurchaseMonthVO {

    @ApiModelProperty(value = "月份")
    private String month;

    @ApiModelProperty(value = "采购金额")
    private BigDecimal purchaseAmount;

    @ApiModelProperty(value = "支付金额")
    private BigDecimal paymentAmount;

    public BizStatPurchaseMonthVO() {
        this.purchaseAmount = new BigDecimal("0");
        this.paymentAmount = new BigDecimal("0");
    }
}
