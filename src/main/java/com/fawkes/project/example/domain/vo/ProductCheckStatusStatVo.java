package com.fawkes.project.example.domain.vo;

import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;

/**
 * 产品检验状态统计Vo类
 *
 * <AUTHOR>
 */
@Data
@ToString
@NoArgsConstructor
public class ProductCheckStatusStatVo {

    /**
     * 产品名称
     */
    private String productName;

    /**
     * 未检验数量
     */
    private Long nonCheckNum;

    /**
     * 合格品数量
     */
    private Long qualifiedNum;

    /**
     * 次品数量
     */
    private Long DefectiveNum;

    /**
     * 废品数量
     */
    private Long wasteNum;

    public ProductCheckStatusStatVo(String productName, Long nonCheckNum, Long qualifiedNum, Long defectiveNum, Long wasteNum) {
        this.nonCheckNum = nonCheckNum;
        this.productName = productName;
        this.qualifiedNum = qualifiedNum;
        DefectiveNum = defectiveNum;
        this.wasteNum = wasteNum;
    }
}
