package com.fawkes.project.example.domain.vo;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.ToString;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.List;

/**
 * <AUTHOR>
 */
@Data
@ToString
public class SaleOrderVO implements Serializable {
    private static final long serialVersionUID = 1L;
    @ApiModelProperty(value = "销售明细统计")
    List<SaleOrderGoodsWithStatVO> goodsList;
    @ApiModelProperty(value = "ID")
    @JsonSerialize(using = ToStringSerializer.class)
    private Long id;
    @ApiModelProperty(value = "订单编号")
    private String orderNo;
    @ApiModelProperty(value = "订单名称")
    private String orderName;
    @ApiModelProperty(value = "订单金额")
    private BigDecimal salePrice;
    @ApiModelProperty(value = "计划销售数量")
    private Long saleQuantity;
    @ApiModelProperty(value = "已交付数量")
    private Long outQuantity;
    @ApiModelProperty(value = "已退货数量")
    private Long rejectQuantity;
    @ApiModelProperty(value = "换货数量")
    private Long exchangeQuantity;
    @ApiModelProperty(value = "预计利润")
    private BigDecimal expectedProfit;
    @ApiModelProperty(value = "实际销售金额")
    private BigDecimal actualSalesAmount;
    @ApiModelProperty(value = "客户名称")
    private String clientName;
    @ApiModelProperty(value = "实际利润合计")
    private BigDecimal actualProfit;
    @ApiModelProperty(value = "累计收款金额")
    private BigDecimal paymentAmount;
    /**
     * 已获取利润 = 累计收款金额 - 交付数量 * 成本
     */
    @ApiModelProperty(value = "已获取利润")
    private BigDecimal earnedProfit;
    @ApiModelProperty(value = "计划销售成本")
    private BigDecimal saleCost;
    @ApiModelProperty(value = "已交付成本")
    private BigDecimal outCost;
    @ApiModelProperty(value = "订单签订日期")
    private LocalDate signDate;
    @ApiModelProperty(value = "计划交付日期")
    private LocalDate plannedDeliveryDate;
    @ApiModelProperty(value = "实际交付日期")
    private LocalDate actualDeliveryDate;
    @ApiModelProperty(value = "责任人名称")
    private String liableUserName;
    @ApiModelProperty(value = "责任人id")
    @JsonSerialize(using = ToStringSerializer.class)
    private Long liableUserId;
    @ApiModelProperty(value = "行业类型")
    private String industry;
    @ApiModelProperty(value = "项目类型：院内项目、市场项目")
    private String projectType;
    @ApiModelProperty(value = "合同状态")
    private String status;
    @ApiModelProperty(value = "客户类型")
    private String clientType;

    @ApiModelProperty(value = "客户地址-省")
    private String clientProvince;

    @ApiModelProperty(value = "客户名称")
    @JsonSerialize(using = ToStringSerializer.class)
    private Long clientId;

    @ApiModelProperty(value = "合同附件")
    private String orderEnclosure;

    public SaleOrderVO() {
        this.saleQuantity = 0L;
        this.outQuantity = 0L;
        this.rejectQuantity = 0L;
        this.exchangeQuantity = 0L;
        this.salePrice = BigDecimal.ZERO;
        this.expectedProfit = BigDecimal.ZERO;
        this.actualSalesAmount = BigDecimal.ZERO;
        this.paymentAmount = BigDecimal.ZERO;
        this.actualProfit = BigDecimal.ZERO;
        this.earnedProfit = BigDecimal.ZERO;
        this.outCost = BigDecimal.ZERO;
        this.saleCost = BigDecimal.ZERO;
    }
}
