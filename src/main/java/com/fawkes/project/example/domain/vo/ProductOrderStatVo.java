package com.fawkes.project.example.domain.vo;

import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;

/**
 * 产品订单统计Vo类
 *
 * <AUTHOR>
 */
@Data
@ToString
@NoArgsConstructor
public class ProductOrderStatVo {

    /**
     * 总生产批次数量
     */
    private Integer totalBatchNum;

    /**
     * 当年生产批次数量
     */
    private Integer curYearBatchNum;

    /**
     * 总生产产品数量
     */
    private Integer totalProductNum;

    /**
     * 当年生产产品数量
     */
    private Integer curYearProductNum;

    public ProductOrderStatVo(Integer totalBatchNum, Integer curYearBatchNum, Integer totalProductNum, Integer curYearProductNum) {
        this.totalBatchNum = totalBatchNum;
        this.curYearBatchNum = curYearBatchNum;
        this.totalProductNum = totalProductNum;
        this.curYearProductNum = curYearProductNum;
    }
}
