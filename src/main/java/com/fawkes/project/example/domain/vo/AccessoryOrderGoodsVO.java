package com.fawkes.project.example.domain.vo;

import com.fawkes.project.example.common.model.AccessoryOrderGoods;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;

import java.math.BigDecimal;
import java.sql.Timestamp;
import java.time.LocalDate;

/**
 * 配件订单明细
 *
 * <AUTHOR>
 */
@Data
@ToString
@NoArgsConstructor
public class AccessoryOrderGoodsVO extends AccessoryOrderGoods {

    @ApiModelProperty(value = "配件编号")
    private String accessoryCode;

    @ApiModelProperty(value = "配件名称")
    private String accessoryName;

    @ApiModelProperty(value = "配件类型：耗材、原材料")
    private String accessoryType;

    @ApiModelProperty(value = "单位")
    private String accessoryUnit;

    @ApiModelProperty(value = "规格型号")
    private String accessorySpec;

    @ApiModelProperty(value = "配件图片")
    private String accessoryPic;

    @ApiModelProperty(value = "厂商名称")
    private String accessorySupplier;

    @ApiModelProperty(value = "当前单价")
    private BigDecimal accessoryCurPrice;

    @ApiModelProperty(value = "到货日期")
    private Timestamp arrivalDate;

    @ApiModelProperty(value = "订单编号")
    private String orderNo;

    @ApiModelProperty(value = "订单名称")
    private String orderName;

    @ApiModelProperty(value = "订单日期")
    private LocalDate orderDate;
}
