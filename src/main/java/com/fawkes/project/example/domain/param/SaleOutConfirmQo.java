package com.fawkes.project.example.domain.param;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;

import java.time.LocalDate;

/**
 * 产品出库入参
 *
 * <AUTHOR>
 */
@Data
@ToString
@NoArgsConstructor
public class SaleOutConfirmQo {

    @ApiModelProperty(value = "销售订单id")
    @JsonSerialize(using = ToStringSerializer.class)
    private Long orderId;

    @ApiModelProperty(value = "出库时间")
    private LocalDate outDate;

    @ApiModelProperty(value = "出库员id")
    @JsonSerialize(using = ToStringSerializer.class)
    private String outUserId;

    @ApiModelProperty(value = "出库员姓名")
    private String outUserName;

    @ApiModelProperty(value = "出库员备注")
    private String outRemark;

    @ApiModelProperty(value = "出库图片")
    private String outPic;

    @ApiModelProperty(value = "产品id列表")
    private String objIds;

    public String getLogDesc() {
        return "销售订单出库";
    }
}
