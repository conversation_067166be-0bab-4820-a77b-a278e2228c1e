package com.fawkes.project.example.domain.param;

import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;

/**
 * 产品检验查询类
 *
 * <AUTHOR>
 */
@Data
@ToString
@NoArgsConstructor
public class CheckQo {

    /**
     * 检验ID数组
     */
    private Long[] checkIds;

    /**
     * 检验状态
     */
    private Integer status;

    /**
     * 不合格原因
     */
    private String unqualifiedReason;

    /**
     * 不合格图片
     */
    private String unqualifiedPic;

    /**
     * 附件
     */
    private String attach;
}
