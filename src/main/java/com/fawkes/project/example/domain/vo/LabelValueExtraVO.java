package com.fawkes.project.example.domain.vo;

import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;

/**
 * <AUTHOR>
 */
@Data
@ToString
@NoArgsConstructor
public class LabelValueExtraVO {

    private String label;

    private Object value;

    private Object extra;

    public LabelValueExtraVO(String label) {
        this.label = label;
    }

    public LabelValueExtraVO(String label, Object value, Object extra) {
        this.label = label;
        this.value = value;
        this.extra = extra;
    }
}
