package com.fawkes.project.example.domain.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.ToString;

import java.math.BigDecimal;

/**
 * 销售订单统计-总览
 *
 * <AUTHOR>
 */
@Data
@ToString
public class StatUseOrderOverallVO {

    @ApiModelProperty(value = "设备领用订单数量")
    private Integer orderQuantity;

    @ApiModelProperty(value = "领用设备累计总数量")
    private Integer useQuantity;

    @ApiModelProperty(value = "领用设备累计总成本")
    private BigDecimal useCost;

    public StatUseOrderOverallVO() {
        this.orderQuantity = 0;
        this.useQuantity = 0;
        this.useCost = new BigDecimal("0");
    }
}
