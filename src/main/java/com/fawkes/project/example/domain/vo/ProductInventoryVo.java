package com.fawkes.project.example.domain.vo;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;

/**
 * 产品库存统计Vo类
 *
 * <AUTHOR>
 */
@Data
@ToString
@NoArgsConstructor
public class ProductInventoryVo {

    @ApiModelProperty(value = "ID")
    @JsonSerialize(using = ToStringSerializer.class)
    private Long productId;

    /**
     * 产品型号
     */
    private String productModel;

    /**
     * 产品名称
     */
    private String productName;

    /**
     * 废品数量
     */
    private Long wasteNum;

    /**
     * 次品数量
     */
    private Long defectiveNum;

    /**
     * 未检验数量
     */
    private Long nonCheckNum;

    /**
     * 可用数量
     */
    private Long availNum;

    /**
     * 再库数量
     */
    private Long inNum;

    /**
     * 待出库数量
     */
    private Long toOutNum;

    public ProductInventoryVo(String productName, String productModel, Long productId) {
        this.productName = productName;
        this.productModel = productModel;
        this.productId = productId;
    }
}
