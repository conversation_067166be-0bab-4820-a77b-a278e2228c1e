package com.fawkes.project.example.domain.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * 订单支付情况
 *
 * <AUTHOR>
 */
@Data
public class AccessoryOrderStatPaidVO implements Serializable {

    @ApiModelProperty(value = "已完全支付订单的数量")
    private Integer notFullyPaidCount;

    @ApiModelProperty(value = "已完全支付订单的数量")
    private Integer fullyPaidCount;

}
