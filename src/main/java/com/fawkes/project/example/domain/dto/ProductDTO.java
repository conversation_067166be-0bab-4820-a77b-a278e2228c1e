package com.fawkes.project.example.domain.dto;

import com.fawkes.project.example.common.model.Product;
import com.fawkes.project.example.common.model.ProductCalib;
import com.fawkes.project.example.domain.vo.ProductCraftVO;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;

import java.util.List;

/**
 * <AUTHOR>
 */
@Data
@ToString
@NoArgsConstructor
public class ProductDTO extends Product {

    @ApiModelProperty(value = "率定参数")
    List<ProductCalib> calibList;

    @ApiModelProperty(value = "工艺")
    List<ProductCraftVO> craftList;
}
