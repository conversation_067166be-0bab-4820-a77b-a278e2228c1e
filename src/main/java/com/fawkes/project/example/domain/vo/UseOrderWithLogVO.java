package com.fawkes.project.example.domain.vo;

import com.fawkes.project.example.common.model.UseOrder;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 */
@Data
@ToString
@NoArgsConstructor
public class UseOrderWithLogVO extends UseOrder implements Serializable {
    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "领用产品明细")
    List<UseOrderGoodsVO> goodsList;
}
