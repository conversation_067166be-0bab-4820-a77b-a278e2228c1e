package com.fawkes.project.example.domain.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import com.fawkes.project.example.common.constants.CommonConstants;
import io.swagger.annotations.ApiModelProperty;
import io.swagger.annotations.Extension;
import io.swagger.annotations.ExtensionProperty;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;

import java.io.Serializable;
import java.sql.Timestamp;
import java.util.Date;


/**
 * <AUTHOR>
 */
@Data
@ToString
@NoArgsConstructor
public class AccessoryOutVO implements Cloneable, Serializable {

    @JsonSerialize(using = ToStringSerializer.class)
    @ApiModelProperty(
            value = "ID",
            extensions = @Extension(name = "rap2", properties = {
                    @ExtensionProperty(name = "type", value = "String"),
                    @ExtensionProperty(name = "value", value = "@id"),
                    @ExtensionProperty(name = "rule", value = "")
            })
    )
    private Long id;

    @ApiModelProperty(value = "订单编号")
    private String orderNo;

    @ApiModelProperty(value = "订单日期")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date orderTime;

    @ApiModelProperty(value = "产品总数量")
    private Integer totalProductQuantity;

    @ApiModelProperty(value = "配件需求量")
    private Integer accessoryQuantity;

    @ApiModelProperty(value = "已出库的配件数量配件")
    private Integer accessoryQuantityWithLoss;

    @ApiModelProperty(value = "责任人")
    private String produceUserName;

    @ApiModelProperty(value = "订单类型：采购入库、产品生产")
    private String orderType = CommonConstants.ORDER_TYPE_CPSC;

    @ApiModelProperty(
            value = "更新时间",
            extensions = @Extension(name = "rap2", properties = {
                    @ExtensionProperty(name = "type", value = "String"),
                    @ExtensionProperty(name = "value", value = "@datetime"),
                    @ExtensionProperty(name = "rule", value = "")
            }))
    private Timestamp updateDate;
}
