package com.fawkes.project.example.domain.param;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;

import java.time.LocalDate;

/**
 * 产品出库收货入参
 *
 * <AUTHOR>
 */
@Data
@ToString
@NoArgsConstructor
public class SaleOutRecevQo {
    @ApiModelProperty(value = "出库收货时间")
    private LocalDate outRecevDate;

    @ApiModelProperty(value = "出库收货员id")
    private Long outReceiverId;

    @ApiModelProperty(value = "出库收货员姓名")
    private String outReceiverName;

    @ApiModelProperty(value = "出库收货备注")
    private String outRecevRemark;

    @ApiModelProperty(value = "出库产品对象ids")
    private String objIds;
}
