package com.fawkes.project.example.domain.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;

import java.io.Serializable;

/**
 * 产品对象的使用状态
 *
 * <AUTHOR>
 */
@Data
@NoArgsConstructor
@ToString
public class ProductStatusVO implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 产品编号
     */
    @ApiModelProperty(value = "产品编号")
    private String objectNo;

    /**
     * 产品名称
     */
    @ApiModelProperty(value = "产品名称")
    private String productName;

    /**
     * 产品型号
     */
    @ApiModelProperty(value = "产品型号")
    private String productModel;

    /**
     * 应用现状：未使用，已使用
     */
    @ApiModelProperty(value = "应用现状：未使用，已使用")
    private String useStatus;

    /**
     * 检验状态：0 - 未检验；1 - 合格品 ；2 - 次品；3 - 废品
     */
    @ApiModelProperty(value = "检验状态：0 - 未检验；1 - 合格品 ；2 - 次品；3 - 废品 ")
    private Integer status;
}
