package com.fawkes.project.example.domain.param;

import com.fawkes.project.example.common.constants.CommonConstants;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * 分页参数
 *
 * <AUTHOR>
 */
@Data
public class PageParam implements Serializable {

    @ApiModelProperty(value = "页码")
    private Integer pageNum = CommonConstants.PAGE_NUM;

    @ApiModelProperty(value = "每页条数")
    private Integer pageSize = CommonConstants.PAGE_SIZE;
}
