package com.fawkes.project.example.domain.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;

import java.io.Serializable;
import java.sql.Timestamp;

/**
 * 产品出库-可用库存
 *
 * <AUTHOR>
 */
@Data
@ToString
@NoArgsConstructor
public class SaleProductObjVO implements Serializable {
    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "产品实体id")
    @JsonSerialize(using = ToStringSerializer.class)
    private Long id;

    @ApiModelProperty(value = "产品编号")
    private String objectNo;

    @ApiModelProperty(value = "工艺版本号id")
    @JsonSerialize(using = ToStringSerializer.class)
    private Long craftId;

    @ApiModelProperty(value = "工艺版本号")
    private String craftCode;

    @ApiModelProperty(value = "产品状态：0 - 未检验；1 - 合格品 ；2 - 次品；3 - 废品")
    private Integer status;


    /******************************* 订单信息 *******************************/

    @ApiModelProperty(value = "物品领用订单id")
    @JsonSerialize(using = ToStringSerializer.class)
    private Long useOrderId;

    @ApiModelProperty(value = "销售订单id")
    @JsonSerialize(using = ToStringSerializer.class)
    private Long saleOrderId;

    /******************************* 产品出库 *******************************/
    @ApiModelProperty(value = "出库员id")
    @JsonSerialize(using = ToStringSerializer.class)
    private Long outUserId;

    @ApiModelProperty(value = "出库员姓名")
    private String outUserName;

    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    @ApiModelProperty(value = "出库时间")
    private Timestamp outDate;

    @ApiModelProperty(value = "出库备注")
    private String outRemark;

    @ApiModelProperty(value = "出库图片")
    private String outPic;

    /******************************* 产品退换货 *******************************/
    @ApiModelProperty(value = "退换货记录id")
    @JsonSerialize(using = ToStringSerializer.class)
    private Long saleOrderReturnId;

    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    @ApiModelProperty(value = "退换货时间")
    private Timestamp returnDate;

    @ApiModelProperty(value = "退换货类型")
    private String returnType;

    @ApiModelProperty(value = "退换货产品状态")
    private String returnStatus;

    @ApiModelProperty(value = "退换货产品状态")
    private String returnReason;

    @ApiModelProperty(value = "退换货产品状态")
    private String returnPic;

    @ApiModelProperty(value = "新产品id")
    @JsonSerialize(using = ToStringSerializer.class)
    private Long returnNewObjId;

    @ApiModelProperty(value = "新产品编号")
    private String returnNewObjNo;
}
