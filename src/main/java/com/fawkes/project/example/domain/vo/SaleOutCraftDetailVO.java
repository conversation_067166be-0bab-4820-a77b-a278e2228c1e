package com.fawkes.project.example.domain.vo;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;

import java.io.Serializable;
import java.util.List;

/**
 * 产品出库-工艺可用库存详情
 *
 * <AUTHOR>
 */
@Data
@ToString
@NoArgsConstructor
public class SaleOutCraftDetailVO implements Serializable {
    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "工艺id")
    @JsonSerialize(using = ToStringSerializer.class)
    private Long craftId;

    @ApiModelProperty(value = "产品名称")
    private String productName;

    @ApiModelProperty(value = "产品型号")
    private String productModel;

    @ApiModelProperty(value = "当前订单需交付的数量(仍需交付的数量)")
    private Integer requiredQuantity;

    @ApiModelProperty(value = "可用库存列表")
    private List<SaleProductObjVO> objList;

}
