package com.fawkes.project.example.domain.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.ToString;

import java.math.BigDecimal;

/**
 * 销售订单-统计总金额、客户数量、总单数
 *
 * <AUTHOR>
 */
@Data
@ToString
public class StatSaleOverallVO {

    @ApiModelProperty(value = "销售订单签署数量")
    private Integer orderQuantity;

    @ApiModelProperty(value = "签单客户数量")
    private Integer clientQuantity;

    @ApiModelProperty(value = "销售订单总金额")
    private BigDecimal orderAmount;

    public StatSaleOverallVO() {
        this.orderQuantity = 0;
        this.clientQuantity = 0;
        this.orderAmount = new BigDecimal("0");
    }
}
