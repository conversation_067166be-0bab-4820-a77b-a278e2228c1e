package com.fawkes.project.example.domain.dto;

import com.fawkes.project.example.common.model.SaleOrder;
import com.fawkes.project.example.domain.vo.SaleOrderGoodsVO;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;

import java.util.List;

/**
 * 销售订单（带销售明细）
 *
 * <AUTHOR>
 */
@Data
@ToString
@NoArgsConstructor
public class SaleOrderDTO extends SaleOrder {

    @ApiModelProperty(value = "销售产品明细")
    List<SaleOrderGoodsVO> goodsList;
    @ApiModelProperty(value = "客户名称")
    private String clientName;
}
