package com.fawkes.project.example.domain.param;

import com.fawkes.project.example.common.constants.CommonConstants;
import com.fawkes.project.example.domain.vo.ProductStatusVO;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 */
@Data
public class ProductStatusParam extends ProductStatusVO {
    @ApiModelProperty(value = "页码")
    private Integer pageNum = CommonConstants.PAGE_NUM;

    @ApiModelProperty(value = "每页条数")
    private Integer pageSize = CommonConstants.PAGE_SIZE;
}
