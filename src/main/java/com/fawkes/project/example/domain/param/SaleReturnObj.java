package com.fawkes.project.example.domain.param;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;

/**
 * 销售退货参数
 *
 * <AUTHOR>
 */
@Data
@ToString
@NoArgsConstructor
public class SaleReturnObj {
    @ApiModelProperty(value = "产品对象id")
    @JsonSerialize(using = ToStringSerializer.class)
    private Long objectId;

    @ApiModelProperty(value = "退换货理由")
    private String returnReason;

    @ApiModelProperty(value = "退换货产品状态")
    private String returnStatus;

    @ApiModelProperty(value = "退换货图片")
    private String returnPic;

    @ApiModelProperty(value = "新产品对象id")
    @JsonSerialize(using = ToStringSerializer.class)
    private Long newObjectId;
}
