package com.fawkes.project.example.domain.param;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.time.LocalDate;

/**
 * 配件出库订单
 *
 * <AUTHOR>
 */
@Data
public class AccessoryOutOrderQueryParam extends PageParam {

    @ApiModelProperty(value = "订单编号（生产）")
    private String orderNo;

    @ApiModelProperty(value = "订单最早日期：yyyy-MM-dd")
    private LocalDate startDate;

    @ApiModelProperty(value = "订单最晚日期：yyyy-MM-dd")
    private LocalDate endDate;
}
