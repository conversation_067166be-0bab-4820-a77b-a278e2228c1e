package com.fawkes.project.example.domain.vo;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import com.fawkes.project.example.common.exception.BizDmException;
import com.fawkes.project.example.common.utils.OrderTool;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.ToString;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Objects;

/**
 * <AUTHOR>
 */
@Data
@ToString
public class SaleOrderGoodsWithStatVO implements Serializable {
    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "ID")
    @JsonSerialize(using = ToStringSerializer.class)
    private Long id;

    @ApiModelProperty(value = "订单ID")
    @JsonSerialize(using = ToStringSerializer.class)
    private Long orderId;

    @ApiModelProperty(value = "产品名称")
    private String productName;

    @ApiModelProperty(value = "产品型号")
    private String productModel;

    @ApiModelProperty(value = "工艺版本号id")
    @JsonSerialize(using = ToStringSerializer.class)
    private Long craftId;

    @ApiModelProperty(value = "工艺版本号")
    private String craftCode;

    @ApiModelProperty(value = "指导成本")
    private BigDecimal guidCost;

    @ApiModelProperty(value = "销售数量（计划销售数量）")
    private Integer saleQuantity;

    @ApiModelProperty(value = "售价(含税)")
    private BigDecimal salePrice;

    @ApiModelProperty(value = "销售单价")
    private BigDecimal saleUnitPrice;

    @ApiModelProperty(value = "已出库数量")
    private Long outQuantity;

    @ApiModelProperty(value = "已退货数量")
    private Long rejectQuantity;

    @ApiModelProperty(value = "已换货数量")
    private Long exchangeQuantity;

    @ApiModelProperty(value = "未交付数量")
    private Long unOutQuantity;

    /**
     * 实际销售金额 = 销售售价 - 退货数量 * 销售单价
     */
    @ApiModelProperty(value = "实际售价合计(实际销售金额)")
    private BigDecimal actualSalesAmount;

    /**
     * 实际利润合计 = 预计利润 - 退货数量 * （销售单价 - 成本）
     */
    @ApiModelProperty(value = "实际利润合计")
    private BigDecimal actualProfit;

    /**
     * 预计利润 = 售价(含税) - 计划销售数量 * 成本
     */
    private BigDecimal expectedProfit;

    /**
     * 毛利润 = 销售额 - 交付数量 * 成本
     */
    @ApiModelProperty(value = "毛利润")
    private BigDecimal grossProfit;

    public SaleOrderGoodsWithStatVO() {
        this.guidCost = BigDecimal.ZERO;
        this.salePrice = BigDecimal.ZERO;
        this.saleUnitPrice = BigDecimal.ZERO;
        this.actualSalesAmount = BigDecimal.ZERO;
        this.actualProfit = BigDecimal.ZERO;
        this.expectedProfit = BigDecimal.ZERO;
        this.grossProfit = BigDecimal.ZERO;

        this.saleQuantity = 0;
        this.outQuantity = 0L;
        this.rejectQuantity = 0L;
        this.exchangeQuantity = 0L;
        this.unOutQuantity = 0L;
    }

    /**
     * 获取销售单价
     *
     * @return
     */
    public BigDecimal calcUnitSalePrice() {
        if (Objects.isNull(salePrice)) {
            throw new BizDmException("未填写售价");
        }
        if (Objects.isNull(saleQuantity) || saleQuantity <= 0) {
            throw new BizDmException("未填写销售数量");
        }
        return OrderTool.bdDivide(salePrice, BigDecimal.valueOf(saleQuantity));
    }

    /**
     * 更新售价和利润
     */
    public void updateAmountAndProfit() {
        // 1. 计算实际销售金额
        this.actualSalesAmount = OrderTool.bdSub(salePrice, OrderTool.bdMul(saleUnitPrice, BigDecimal.valueOf(rejectQuantity)));
        // 2. 计算预计利润
        this.expectedProfit = OrderTool.bdSub(salePrice, OrderTool.bdMul(guidCost, BigDecimal.valueOf(saleQuantity)));
        // 3. 计算实际利润
        this.actualProfit = OrderTool.bdSub(expectedProfit, OrderTool.bdMul(BigDecimal.valueOf(rejectQuantity), OrderTool.bdSub(saleUnitPrice, guidCost)));
        // 4. 计算毛利润
        this.grossProfit = OrderTool.bdSub(salePrice, OrderTool.bdMul(BigDecimal.valueOf(outQuantity), guidCost));
    }
}
