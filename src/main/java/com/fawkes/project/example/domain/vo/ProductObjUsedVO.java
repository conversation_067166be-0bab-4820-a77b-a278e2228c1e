package com.fawkes.project.example.domain.vo;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 产品对象使用状态
 *
 * <AUTHOR>
 */
@Data
public class ProductObjUsedVO {

    @ApiModelProperty(value = "产品对象")
    @JsonSerialize(using = ToStringSerializer.class)
    private Long id;

    @ApiModelProperty(value = "产品对象编号")
    private String objectNo;

    @ApiModelProperty(value = "使用订单类型")
    private String orderType;

    @ApiModelProperty(value = "使用订单名称")
    private String orderName;
}
