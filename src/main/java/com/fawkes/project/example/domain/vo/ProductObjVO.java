package com.fawkes.project.example.domain.vo;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import com.fawkes.project.example.common.model.ProductObject;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;

import java.util.Date;


/**
 * 产品对象VO
 *
 * <AUTHOR>
 */
@Data
@ToString
@NoArgsConstructor
public class ProductObjVO extends ProductObject {

//    @ApiModelProperty(value = "产品名称")
//    private String productName;
//
//    @ApiModelProperty(value = "产品型号")
//    private String productModel;

    @ApiModelProperty(value = "应用行业")
    private String industry;

    @ApiModelProperty(value = "销售订单id")
    @JsonSerialize(using = ToStringSerializer.class)
    private Long saleOrderId;

    @ApiModelProperty(value = "物品领用订单id")
    @JsonSerialize(using = ToStringSerializer.class)
    private Long useOrderId;

    @ApiModelProperty(value = "物品领用时间")
    private Date useDate;

    @ApiModelProperty(value = "退换货类型：退货、换货")
    private String returnType;

    @ApiModelProperty(value = "归还状态：合格、不合格")
    private String returnStatus;

    @ApiModelProperty(value = "不合格理由")
    private String returnReason;

    @ApiModelProperty(value = "不合格图片")
    private String returnPic;

    public ProductObjVO(Long id, Integer status, String returnReason, String returnPic) {
        this.setId(id);
        this.setStatus(status);
        this.returnReason = returnReason;
        this.returnPic = returnPic;
    }

    @Override
    public String toString() {
        return "ProductObjVO{" +
                "id='" + this.getId() + '\'' +
                "status='" + this.getStatus() + '\'' +
                "returnStatus='" + returnStatus + '\'' +
                ", returnReason='" + returnReason + '\'' +
                ", returnPic='" + returnPic + '\'' +
                '}';
    }
}
