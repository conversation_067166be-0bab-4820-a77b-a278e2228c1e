package com.fawkes.project.example.domain.vo;

import com.fawkes.project.example.common.model.UseOrderGoods;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Objects;

/**
 * 领用明细
 *
 * <AUTHOR>
 */
@Data
public class UseOrderGoodsVO extends UseOrderGoods {

    @ApiModelProperty(value = "产品编号")
    private String objectNo;

    @ApiModelProperty(value = "产品型号")
    private String productModel;

    @ApiModelProperty(value = "产品名称")
    private String productName;

    @ApiModelProperty(value = "工艺版本号")
    private String craftCode;

    @ApiModelProperty(value = "应用行业")
    private String industry;

    @ApiModelProperty(value = "领用状态")
    private String useStatus;

    public String getUseStatus() {
        return Objects.isNull(this.getUseReturnTime()) ? "领用" : "归还";
    }
}
