package com.fawkes.project.example.domain.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.ToString;

import java.math.BigDecimal;
import java.util.List;

/**
 * 产品退换货-退货数量、销售总额
 *
 * <AUTHOR>
 */
@Data
@ToString
public class SaleReturnDetailVO {

    @ApiModelProperty(value = "退换货产品树表")
    List<SaleGoodsVO> returnList;
    @ApiModelProperty(value = "退货产品销售总额（含税）")
    private Integer rejectQuantity;
    @ApiModelProperty(value = "退货产品销售总额（含税）")
    private BigDecimal rejectAmount;
    @ApiModelProperty(value = "换货产品销售总额（含税）")
    private Integer exchangeQuantity;
    @ApiModelProperty(value = "换货产品销售总额（含税）")
    private BigDecimal exchangeAmount;

    public SaleReturnDetailVO() {
        this.rejectQuantity = 0;
        this.rejectAmount = new BigDecimal("0");
        this.exchangeQuantity = 0;
        this.exchangeAmount = new BigDecimal("0");
    }
}
