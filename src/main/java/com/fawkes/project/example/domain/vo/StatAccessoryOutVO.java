package com.fawkes.project.example.domain.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;

/**
 * <AUTHOR>
 */
@Data
public class StatAccessoryOutVO {

    @ApiModelProperty(value = "订单id")
    @JsonSerialize(using = ToStringSerializer.class)
    private Long orderId;

    @ApiModelProperty(value = "订单编号")
    private String orderNo;

    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    @ApiModelProperty(value = "订单日期")
    private Date orderTime;

    @ApiModelProperty(value = "配件id")
    @JsonSerialize(using = ToStringSerializer.class)
    private Long accessoryId;

    @ApiModelProperty(value = "配件使用量")
    private Integer useAccessoryQuantity;

    @ApiModelProperty(value = "配件损耗量")
    private Integer lossAccessoryQuantity;
}
