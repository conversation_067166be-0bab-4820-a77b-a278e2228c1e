package com.fawkes.project.example.domain.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;

import java.math.BigDecimal;
import java.util.List;

/**
 * 产品出库-已出库产品、已出库产品销售总额（含税）
 *
 * <AUTHOR>
 */
@Data
@ToString
@NoArgsConstructor
public class SaleOutDetailVO {

    @ApiModelProperty(value = "已出库产品树表")
    List<SaleGoodsVO> outList;
    @ApiModelProperty(value = "已出库产品总数")
    private int outQuantity;
    @ApiModelProperty(value = "已出库产品销售总额（含税）")
    private BigDecimal outTotalSalePrice;
}
