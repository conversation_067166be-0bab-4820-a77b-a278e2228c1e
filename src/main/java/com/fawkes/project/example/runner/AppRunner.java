package com.fawkes.project.example.runner;

import lombok.extern.slf4j.Slf4j;
import org.apache.commons.io.FileUtils;
import org.apache.commons.io.IOUtils;
import org.springframework.boot.ApplicationArguments;
import org.springframework.boot.ApplicationRunner;
import org.springframework.stereotype.Component;

import java.io.File;
import java.io.IOException;
import java.io.InputStream;

@Slf4j
@Component
public class AppRunner implements ApplicationRunner {

    private static String createFtlFile(String ftlPath, String ftlName) {
        try {
            //获取当前项目所在的绝对路径
            String proFilePath = System.getProperty("user.dir");
            log.info("project run path：" + proFilePath);
            //获取模板下的路径　
            String newFilePath = proFilePath + File.separator + "src" + File.separator + "main" + File.separator + "resources" + File.separator + ftlPath;
            newFilePath = newFilePath.replace("/", File.separator);
            Cache.ftlPath = newFilePath;
//            log.info("newFilePath:" + newFilePath);
            //检查项目运行时的src下的对应路径
            File newFile = new File(newFilePath + ftlName);
            if (newFile.isFile() && newFile.exists()) {
                return newFilePath;
            }
            //当项目打成jar包会运行下面的代码，而且复制一份到src路径下（具体结构看下面图片）
            InputStream certStream = Thread.currentThread().getContextClassLoader().getResourceAsStream(ftlPath + ftlName);
            byte[] certData = IOUtils.toByteArray(certStream);
            FileUtils.writeByteArrayToFile(newFile, certData);
            return newFilePath;
        } catch (IOException e) {
            log.error("复制文件失败--> 异常信息：" + e);
        }
        return null;
    }

    @Override
    public void run(ApplicationArguments args) throws Exception {
        createFtlFile("ftls/", "template.ftl");
    }


}
