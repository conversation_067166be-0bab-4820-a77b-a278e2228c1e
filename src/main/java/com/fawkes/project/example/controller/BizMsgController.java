package com.fawkes.project.example.controller;

import com.fawkes.core.base.api.ApiResponseBody;
import com.fawkes.project.example.common.model.BizMsg;
import com.fawkes.project.example.service.impl.BizMsgService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 消息通知
 *
 * <AUTHOR>
 */
@Api(tags = {"消息通知"})
@RestController
@RequestMapping("/msg")
public class BizMsgController {

    @Resource
    private BizMsgService bizMsgService;

    @ApiOperation("查询消息")
    @GetMapping(produces = {"application/json;charset=UTF-8"})
    public ApiResponseBody<List<BizMsg>> list() {
        return bizMsgService.list();
    }

    @ApiOperation("全部已读")
    @PutMapping(produces = {"application/json;charset=UTF-8"})
    public ApiResponseBody readAll() {
        return bizMsgService.readAll();
    }

    @ApiOperation("测试消息通知")
    @ApiImplicitParam(name = "userIds", value = "用户id")
    @GetMapping(path = {"/notify"}, produces = {"application/json;charset=UTF-8"})
    public ApiResponseBody notifyRefreshMsg(@RequestParam(value = "userIds", defaultValue = "") String userIds) {
        bizMsgService.notifyRefreshMsg(Arrays.stream(userIds.split(",")).map(Long::parseLong).collect(Collectors.toList()));
        return ApiResponseBody.defaultSuccess();
    }
}
