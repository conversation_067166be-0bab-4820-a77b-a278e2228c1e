package com.fawkes.project.example.controller;

import com.fawkes.core.base.api.ApiResponseBody;
import com.fawkes.project.example.common.model.ProductParam;
import com.fawkes.project.example.domain.dto.ProductParamDTO;
import com.fawkes.project.example.service.impl.ProductParamService;
import com.github.pagehelper.PageInfo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 */
@Api(tags = {"产品参数配置"})
@RestController
@RequestMapping("/product/param")
public class ProductParamController {
    @Resource
    private ProductParamService productParamService;

    @ApiOperation("添加")
    @PostMapping(produces = {"application/json;charset=UTF-8"})
    public ApiResponseBody add(@RequestBody ProductParam productParam) {
        return productParamService.add(productParam);
    }

    @ApiOperation("详情")
    @ApiImplicitParam(name = "id", value = "产品参数id", required = true)
    @GetMapping(produces = {"application/json;charset=UTF-8"})
    public ApiResponseBody<ProductParamDTO> query(@RequestParam String id) {
        return productParamService.query(id);
    }

    @ApiOperation("删除")
    @ApiImplicitParam(name = "id", value = "fawkesId")
    @DeleteMapping(produces = {"application/json;charset=UTF-8"})
    public ApiResponseBody delete(@RequestParam(value = "id") String id) {
        return productParamService.delete(id);
    }

    @ApiOperation("修改")
    @PutMapping(produces = {"application/json;charset=UTF-8"})
    public ApiResponseBody update(@RequestBody ProductParam productParam) {
        return productParamService.update(productParam);
    }


    @ApiOperation("分页查询")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "pageNum", value = "页码", defaultValue = "1"),
            @ApiImplicitParam(name = "pageSize", value = "每页条数", defaultValue = "10"),
            @ApiImplicitParam(name = "name", value = "名称", defaultValue = "")})
    @GetMapping(path = "/list", produces = {"application/json;charset=UTF-8"})
    public ApiResponseBody<PageInfo<ProductParamDTO>> page(@RequestParam(value = "pageNum", defaultValue = "1") Integer pageNum,
                                                           @RequestParam(value = "pageSize", defaultValue = "100") Integer pageSize,
                                                           @RequestParam(value = "name", defaultValue = "") String name) {
        return productParamService.page(pageNum, pageSize, name);
    }
}
