package com.fawkes.project.example.controller;

import com.fawkes.core.base.api.ApiResponseBody;
import com.fawkes.project.example.domain.vo.ProductCraftVO;
import com.fawkes.project.example.service.impl.ProductService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 */
@Api(tags = {"产品信息维护"})
@RestController
@RequestMapping("/product")
public class ProductController {

    @Resource
    private ProductService productService;

    @ApiOperation("新增工艺")
    @PostMapping(path = {"/craft"}, produces = {"application/json;charset=UTF-8"})
    public ApiResponseBody add(@RequestBody ProductCraftVO productCraftVO) {
        return productService.add(productCraftVO);
    }

    @ApiOperation("查询工艺详情")
    @ApiImplicitParam(name = "id", value = "fawkesId", required = true)
    @GetMapping(produces = {"application/json;charset=UTF-8"})
    public ApiResponseBody query(@RequestParam String id) {
        return productService.queryCraft(id);
    }

    @ApiOperation("删除工艺")
    @ApiImplicitParam(name = "id", value = "fawkesId")
    @DeleteMapping(path = {"/craft"}, produces = {"application/json;charset=UTF-8"})
    public ApiResponseBody delete(@RequestParam(value = "id") String id) {
        return productService.deleteCraft(id);
    }

    @ApiOperation("批量删除工艺")
    @ApiImplicitParam(name = "productIds", value = "fawkesId")
    @DeleteMapping(path = {"/craft/batch"}, produces = {"application/json;charset=UTF-8"})
    public ApiResponseBody deleteBatchByProductIds(@RequestParam(value = "productIds") String productIds) {
        return productService.deleteCraftBatchByProductIds(productIds);
    }

    @ApiOperation("修改工艺")
    @PutMapping(path = {"/craft"}, produces = {"application/json;charset=UTF-8"})
    public ApiResponseBody update(@RequestBody ProductCraftVO productCraftVO) {
        return productService.update(productCraftVO);
    }

    @ApiOperation("批量启用/弃用产品")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "ids", value = "产品id，多个时逗号拼接", defaultValue = ""),
            @ApiImplicitParam(name = "applyStatus", value = "应用现状", defaultValue = "")
    })
    @GetMapping(path = "/product/applyStatus", produces = {"application/json;charset=UTF-8"})
    public ApiResponseBody changeProductApplyStatus(@RequestParam(value = "ids") String productIds, @RequestParam(value = "applyStatus") Boolean applyStatus) {
        return productService.changeProductApplyStatus(productIds, applyStatus);
    }

    @ApiOperation("启用/弃用工艺")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "craftId", value = "工艺id", defaultValue = ""),
            @ApiImplicitParam(name = "applyStatus", value = "应用现状", defaultValue = "")
    })
    @GetMapping(path = "/craft/applyStatus", produces = {"application/json;charset=UTF-8"})
    public ApiResponseBody changeCraftApplyStatus(@RequestParam(value = "craftId") String craftId, @RequestParam(value = "applyStatus") Boolean applyStatus) {
        return productService.changeCraftApplyStatus(craftId, applyStatus);
    }

    @ApiOperation("获取最新工艺版本号")
    @ApiImplicitParam(name = "productId", value = "产品id")
    @GetMapping(path = "/craft/latestCode", produces = {"application/json;charset=UTF-8"})
    public ApiResponseBody genLatestCraftCode(@RequestParam(value = "productId") String productId) {
        return productService.genLatestCraftCode(productId);
    }

    @ApiOperation("分页查询")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "pageNum", value = "页码", defaultValue = "1"),
            @ApiImplicitParam(name = "pageSize", value = "每页条数", defaultValue = "10"),
            @ApiImplicitParam(name = "model", value = "产品型号", defaultValue = ""),
            @ApiImplicitParam(name = "name", value = "产品名称", defaultValue = ""),
            @ApiImplicitParam(name = "applyStatus", value = "应用现状", defaultValue = "")
    })
    @GetMapping(path = "/list", produces = {"application/json;charset=UTF-8"})
    public ApiResponseBody page(@RequestParam(value = "pageNum", defaultValue = "1") Integer pageNum,
                                @RequestParam(value = "pageSize", defaultValue = "100") Integer pageSize,
                                @RequestParam(value = "model", defaultValue = "") String model,
                                @RequestParam(value = "name", defaultValue = "") String name,
                                @RequestParam(value = "applyStatus", defaultValue = "") Boolean applyStatus) {
        return productService.page(pageNum, pageSize, model, name, applyStatus);
    }
}
