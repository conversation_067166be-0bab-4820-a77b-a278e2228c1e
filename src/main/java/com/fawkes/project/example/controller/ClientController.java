package com.fawkes.project.example.controller;

import com.fawkes.core.base.api.ApiResponseBody;
import com.fawkes.project.example.domain.param.ClientParam;
import com.fawkes.project.example.domain.vo.ClientVO;
import com.fawkes.project.example.domain.vo.StatClientOverallVO;
import com.fawkes.project.example.service.impl.ClientService;
import com.github.pagehelper.PageInfo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.time.Year;

/**
 * <AUTHOR>
 */
@Api(tags = {"客户管理"})
@RestController
@RequestMapping("/client")
public class ClientController {

    @Resource
    ClientService clientService;

    @ApiOperation("添加")
    @PostMapping(produces = {"application/json;charset=UTF-8"})
    public ApiResponseBody add(@RequestBody ClientVO clientVO) {
        return clientService.add(clientVO);
    }

    @ApiOperation("详情")
    @ApiImplicitParam(name = "id", value = "客户id", required = true)
    @GetMapping(produces = {"application/json;charset=UTF-8"})
    public ApiResponseBody<ClientVO> query(@RequestParam String id) {
        return clientService.query(id);
    }

    @ApiOperation("批量删除")
    @ApiImplicitParam(name = "ids", value = "客户id，多个逗号拼接")
    @DeleteMapping(produces = {"application/json;charset=UTF-8"})
    public ApiResponseBody delete(@RequestParam(value = "ids") String ids) {
        return clientService.deleteBatch(ids);
    }

    @ApiOperation("修改")
    @PutMapping(produces = {"application/json;charset=UTF-8"})
    public ApiResponseBody update(@RequestBody ClientVO clientVO) {
        return clientService.update(clientVO);
    }

    @ApiOperation("分页查询")
    @PostMapping(path = "/list", produces = {"application/json;charset=UTF-8"})
    public ApiResponseBody<PageInfo<ClientVO>> page(@RequestBody ClientParam clientParam) {
        return clientService.page(clientParam);
    }

    @ApiOperation("统计-行业类型-客户类型")
    @GetMapping(path = {"/stat/client"}, produces = {"application/json;charset=UTF-8"})
    public ApiResponseBody statClient() {
        return clientService.statClient();
    }

    @ApiOperation("统计-销售金额-客户类型")
    @ApiImplicitParam(name = "searchYear", value = "年份")
    @GetMapping(path = {"/stat/saleAmount"}, produces = {"application/json;charset=UTF-8"})
    public ApiResponseBody statSaleAmount(@RequestParam Year searchYear) {
        return clientService.statSaleAmount(searchYear);
    }

    @ApiOperation("统计-销售金额-行业类型")
    @ApiImplicitParam(name = "searchYear", value = "年份")
    @GetMapping(path = {"/stat/saleAmount/industry"}, produces = {"application/json;charset=UTF-8"})
    public ApiResponseBody statSaleAmountByIndustry(@RequestParam Year searchYear) {
        return clientService.statSaleAmountByIndustry(searchYear);
    }

    @ApiOperation("统计-销售订单")
    @ApiImplicitParam(name = "searchYear", value = "年份")
    @GetMapping(path = {"/stat/saleQuantity"}, produces = {"application/json;charset=UTF-8"})
    public ApiResponseBody statSaleQuantity(@RequestParam Year searchYear) {
        return clientService.statSaleQuantity(searchYear);
    }

    @ApiOperation("统计-客户分布-省份订单数")
    @ApiImplicitParam(name = "searchYear", value = "年份")
    @GetMapping(path = {"/stat/clientMap"}, produces = {"application/json;charset=UTF-8"})
    public ApiResponseBody statClientByProvince(@RequestParam Year searchYear) {
        return clientService.statClientByProvince(searchYear);
    }

    @ApiOperation("统计-客户分布-详情")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "searchYear", value = "年份，不传返回全部", defaultValue = ""),
            @ApiImplicitParam(name = "province", value = "省份，不传返回全部", defaultValue = "")
    })
    @GetMapping(path = {"/stat/overall"}, produces = {"application/json;charset=UTF-8"})
    public ApiResponseBody<StatClientOverallVO> statOverall(@RequestParam(value = "searchYear", defaultValue = "") Year searchYear,
                                                            @RequestParam(value = "province", defaultValue = "") String province) {
        return clientService.statOverall(searchYear, province);
    }
}
