package com.fawkes.project.example.controller;

import com.fawkes.core.base.api.ApiResponseBody;
import com.fawkes.project.example.domain.dto.ProductDTO;
import com.fawkes.project.example.service.impl.ProductConfigService;
import com.github.pagehelper.PageInfo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 */
@Api(tags = {"产品类型配置"})
@RestController
@RequestMapping("/product/config")
public class ProductConfigController {
    @Resource
    ProductConfigService productConfigService;

    @ApiOperation("添加")
    @PostMapping(produces = {"application/json;charset=UTF-8"})
    public ApiResponseBody add(@RequestBody ProductDTO productDTO) {
        return productConfigService.add(productDTO);
    }

    @ApiOperation("查询产品")
    @ApiImplicitParam(name = "id", value = "fawkesId", required = true)
    @GetMapping(produces = {"application/json;charset=UTF-8"})
    public ApiResponseBody<ProductDTO> query(@RequestParam String id) {
        return productConfigService.query(id);
    }

    @ApiOperation("删除产品")
    @ApiImplicitParam(name = "id", value = "fawkesId")
    @DeleteMapping(produces = {"application/json;charset=UTF-8"})
    public ApiResponseBody delete(@RequestParam(value = "id") String id) {
        return productConfigService.delete(id);
    }

    @ApiOperation("修改产品")
    @PutMapping(produces = {"application/json;charset=UTF-8"})
    public ApiResponseBody update(@RequestBody ProductDTO productDTO) {
        return productConfigService.update(productDTO);
    }

    @ApiOperation("分页查询")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "pageNum", value = "页码", defaultValue = "1"),
            @ApiImplicitParam(name = "pageSize", value = "每页条数", defaultValue = "10"),
            @ApiImplicitParam(name = "name", value = "产品名称", defaultValue = "")
    })
    @GetMapping(path = "/list", produces = {"application/json;charset=UTF-8"})
    public ApiResponseBody<PageInfo<ProductDTO>> page(@RequestParam(value = "pageNum", defaultValue = "1") Integer pageNum,
                                                      @RequestParam(value = "pageSize", defaultValue = "100") Integer pageSize,
                                                      @RequestParam(value = "name", defaultValue = "") String name) {
        return productConfigService.page(pageNum, pageSize, name);
    }
}
