package com.fawkes.project.example.controller;

import com.fawkes.core.base.api.ApiResponseBody;
import com.fawkes.project.example.common.model.Accessory;
import com.fawkes.project.example.domain.vo.AccessoryInventoryVO;
import com.fawkes.project.example.domain.vo.AccessoryVO;
import com.fawkes.project.example.service.impl.AccessoryService;
import com.github.pagehelper.PageInfo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;

/**
 * 配件管理
 *
 * <AUTHOR>
 */
@Api(tags = {"配件信息管理"})
@RestController
@RequestMapping("/accessory")
public class AccessoryController {
    @Resource
    private AccessoryService accessoryService;

    @ApiOperation("添加配件")
    @PostMapping(produces = {"application/json;charset=UTF-8"})
    public ApiResponseBody add(@RequestBody Accessory accessory) {
        return accessoryService.add(accessory);
    }

    @ApiOperation("查询配件")
    @ApiImplicitParam(name = "id", value = "fawkesId", required = true)
    @GetMapping(produces = {"application/json;charset=UTF-8"})
    public ApiResponseBody<Accessory> query(@RequestParam String id) {
        return accessoryService.query(id);
    }

    @ApiOperation("删除配件")
    @ApiImplicitParam(name = "id", value = "fawkesId")
    @DeleteMapping(produces = {"application/json;charset=UTF-8"})
    public ApiResponseBody delete(@RequestParam(value = "id") String id) {
        return accessoryService.delete(id);
    }

    @ApiOperation("修改配件")
    @PutMapping(produces = {"application/json;charset=UTF-8"})
    public ApiResponseBody update(@RequestBody Accessory accessory) {
        return accessoryService.update(accessory);
    }

    @ApiOperation("配件分页查询")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "pageNum", value = "页码", defaultValue = "1"),
            @ApiImplicitParam(name = "pageSize", value = "每页条数", defaultValue = "10"),
            @ApiImplicitParam(name = "accessoryCode", value = "配件编号", defaultValue = ""),
            @ApiImplicitParam(name = "accessoryName", value = "配件名称", defaultValue = ""),
            @ApiImplicitParam(name = "accessoryType", value = "配件类型", defaultValue = "")
    })
    @GetMapping(path = "/list", produces = {"application/json;charset=UTF-8"})
    public ApiResponseBody<PageInfo<AccessoryVO>> page(@RequestParam(value = "pageNum", defaultValue = "1") Integer pageNum,
                                                       @RequestParam(value = "pageSize", defaultValue = "100") Integer pageSize,
                                                       @RequestParam(value = "accessoryCode", defaultValue = "") String accessoryCode,
                                                       @RequestParam(value = "accessoryName", defaultValue = "") String accessoryName,
                                                       @RequestParam(value = "accessoryType", defaultValue = "") String accessoryType) {
        return accessoryService.page(pageNum, pageSize, accessoryCode, accessoryName, accessoryType);
    }

    @ApiOperation("配件库存分页查询")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "pageNum", value = "页码", defaultValue = "1"),
            @ApiImplicitParam(name = "pageSize", value = "每页条数", defaultValue = "10"),
            @ApiImplicitParam(name = "accessoryCode", value = "配件编号", defaultValue = ""),
            @ApiImplicitParam(name = "accessoryName", value = "配件名称", defaultValue = ""),
            @ApiImplicitParam(name = "accessoryType", value = "配件类型", defaultValue = ""),
            @ApiImplicitParam(name = "accessorySupplier", value = "厂商名称", defaultValue = ""),
    })
    @GetMapping(path = "/inventory/list", produces = {"application/json;charset=UTF-8"})
    public ApiResponseBody<PageInfo<AccessoryInventoryVO>> inventoryPage(@RequestParam(value = "pageNum", defaultValue = "1") Integer pageNum,
                                                                         @RequestParam(value = "pageSize", defaultValue = "100") Integer pageSize,
                                                                         @RequestParam(value = "accessoryCode", defaultValue = "") String accessoryCode,
                                                                         @RequestParam(value = "accessoryName", defaultValue = "") String accessoryName,
                                                                         @RequestParam(value = "accessoryType", defaultValue = "") String accessoryType,
                                                                         @RequestParam(value = "accessorySupplier", defaultValue = "") String accessorySupplier) {
        return accessoryService.inventoryPage(pageNum, pageSize, accessoryCode, accessoryName, accessoryType, accessorySupplier);
    }

    @ApiOperation("统计：配件库存（大类小类）")
    @GetMapping(path = "/inventory/tree", produces = {"application/json;charset=UTF-8"})
    public ApiResponseBody inventoryTree() {
        return accessoryService.inventoryTree();
    }
}
