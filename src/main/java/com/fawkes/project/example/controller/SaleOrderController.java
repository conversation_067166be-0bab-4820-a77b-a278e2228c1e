package com.fawkes.project.example.controller;

import com.fawkes.core.base.api.ApiResponseBody;
import com.fawkes.project.example.domain.dto.SaleOrderDTO;
import com.fawkes.project.example.domain.dto.SaleOrderUpdateDTO;
import com.fawkes.project.example.domain.param.SaleOrderPageParam;
import com.fawkes.project.example.domain.param.SaleOutConfirmQo;
import com.fawkes.project.example.domain.param.SaleReturnParam;
import com.fawkes.project.example.domain.vo.*;
import com.fawkes.project.example.service.impl.SaleOrderServiceImpl;
import com.github.pagehelper.PageInfo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.time.Year;
import java.util.List;

/**
 * <AUTHOR>
 */
@Api(tags = {"销售订单管理"})
@RestController
@RequestMapping("/sale")
public class SaleOrderController {
    @Resource
    private SaleOrderServiceImpl saleOrderService;

    @ApiOperation("销售订单-获取最新的订单编号")
    @GetMapping(path = {"/latestNo"}, produces = {"application/json;charset=UTF-8"})
    public ApiResponseBody<String> getLatestOrderNo() {
        return saleOrderService.getLatestOrderNo();
    }


    /****************************************** 销售订单 **************************************************/
    @ApiOperation("销售订单-添加订单")
    @PostMapping(path = {"/order"}, produces = {"application/json;charset=UTF-8"})
    public ApiResponseBody add(@RequestBody SaleOrderDTO saleOrderDTO) {
        return saleOrderService.add(saleOrderDTO);
    }

    @ApiOperation("销售订单-查看订单详情")
    @ApiImplicitParam(name = "id", value = "订单id", required = true)
    @GetMapping(path = {"/order"}, produces = {"application/json;charset=UTF-8"})
    public ApiResponseBody query(@RequestParam String id) {
        return saleOrderService.query(id);
    }

    @ApiOperation("销售订单-批量删除订单")
    @ApiImplicitParam(name = "ids", value = "订单id")
    @DeleteMapping(path = {"/order"}, produces = {"application/json;charset=UTF-8"})
    public ApiResponseBody delete(@RequestParam(value = "ids") String ids) {
        return saleOrderService.deleteByIds(ids);
    }

    @ApiOperation("销售订单-修改订单")
    @PutMapping(path = {"/order"}, produces = {"application/json;charset=UTF-8"})
    public ApiResponseBody update(@RequestBody SaleOrderUpdateDTO orderUpdateDTO) {
        return saleOrderService.update(orderUpdateDTO);
    }

    @ApiOperation("销售订单-分页查询订单")
    @PostMapping(path = {"/order/list"}, produces = {"application/json;charset=UTF-8"})
    public ApiResponseBody<PageInfo<SaleOrderVO>> page(@RequestBody SaleOrderPageParam param) {
        return saleOrderService.page(param);
    }

    @ApiOperation("销售订单-统计金额/客户量/单数")
    @GetMapping(path = {"/stat/overall"}, produces = {"application/json;charset=UTF-8"})
    public ApiResponseBody<StatSaleOverallVO> statOverall() {
        return saleOrderService.statOverall();
    }

    @ApiOperation("销售订单-统计收款/销售")
    @ApiImplicitParam(name = "searchYear", value = "统计年份")
    @GetMapping(path = {"/stat/sale"}, produces = {"application/json;charset=UTF-8"})
    public ApiResponseBody<List<StatSaleOrderVO>> statSale(@RequestParam(value = "searchYear") Year searchYear) {
        return saleOrderService.statSale(searchYear);
    }


    /****************************************** 变更记录 **************************************************/
    @ApiOperation("销售订单-获取订单变更项")
    @PostMapping(path = {"/order/check"}, produces = {"application/json;charset=UTF-8"})
    public ApiResponseBody preCheck(@RequestBody SaleOrderDTO saleOrderDTO) {
        return saleOrderService.preCheck(saleOrderDTO);
    }

    @ApiOperation("销售订单-分页查询变更记录")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "pageNum", value = "页码", defaultValue = "1"),
            @ApiImplicitParam(name = "pageSize", value = "每页条数", defaultValue = "10"),
            @ApiImplicitParam(name = "orderId", value = "订单id", defaultValue = "", required = true)})
    @GetMapping(path = {"/alter/list"}, produces = {"application/json;charset=UTF-8"})
    public ApiResponseBody alterPage(@RequestParam(value = "pageNum", defaultValue = "1") Integer pageNum,
                                     @RequestParam(value = "pageSize", defaultValue = "10") Integer pageSize,
                                     @RequestParam(value = "orderId", defaultValue = "") String orderId) {
        return saleOrderService.alterPage(pageNum, pageSize, orderId);
    }

    @ApiOperation("销售订单-变更记录详情")
    @ApiImplicitParam(name = "alterId", value = "变更记录id", required = true)
    @GetMapping(path = {"/alter/detail"}, produces = {"application/json;charset=UTF-8"})
    public ApiResponseBody alterInfo(@RequestParam String alterId) {
        return saleOrderService.alterInfo(alterId);
    }

    /****************************************** 交付 **************************************************/


    /****************************************** 出库 **************************************************/

    @ApiOperation("产品出库-已出库列表")
    @ApiImplicitParam(name = "orderId", value = "订单id", required = true)
    @GetMapping(path = {"/out/list"}, produces = {"application/json;charset=UTF-8"})
    public ApiResponseBody<SaleOutDetailVO> outPage(@RequestParam String orderId) {
        return saleOrderService.outPage(orderId);
    }

    @ApiOperation("产品出库-工艺下拉选项")
    @ApiImplicitParam(name = "orderId", value = "订单id", required = true)
    @GetMapping(path = {"/out/craft/list"}, produces = {"application/json;charset=UTF-8"})
    public ApiResponseBody getCraftList(@RequestParam String orderId) {
        return saleOrderService.getCraftList(orderId);
    }

    @ApiOperation("产品出库-工艺可用详情")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "orderId", value = "销售订单id", defaultValue = ""),
            @ApiImplicitParam(name = "craftId", value = "工艺id", defaultValue = "")})
    @GetMapping(path = {"/out/craft"}, produces = {"application/json;charset=UTF-8"})
    public ApiResponseBody getAvailableCraftInfo(@RequestParam(value = "orderId", defaultValue = "") String orderId,
                                                 @RequestParam(value = "craftId", defaultValue = "") String craftId) {
        return saleOrderService.getAvailableCraftInfo(orderId, craftId);
    }

    @ApiOperation("产品出库-出库确认")
    @PostMapping(path = {"/out/submit"}, produces = {"application/json;charset=UTF-8"})
    public ApiResponseBody outConfirm(@RequestBody SaleOutConfirmQo saleOutConfirmQo) {
        return saleOrderService.outConfirm(saleOutConfirmQo);
    }

    @ApiOperation("产品出库-删除已出库产品")
    @ApiImplicitParam(name = "objId", value = "已出库产品id")
    @DeleteMapping(path = {"/out"}, produces = {"application/json;charset=UTF-8"})
    public ApiResponseBody deleteOutByObjId(@RequestParam(value = "objId", defaultValue = "") String objId) {
        return saleOrderService.deleteOutByObjId(objId);
    }

    @ApiOperation("产品出库-查询产品详情")
    @ApiImplicitParam(name = "objId", value = "产品对象的id", required = true)
    @GetMapping(path = {"/out/obj"}, produces = {"application/json;charset=UTF-8"})
    public ApiResponseBody<SaleProductObjVO> queryObj(@RequestParam String objId) {
        return saleOrderService.queryObj(objId);
    }

    @ApiOperation("产品出库-修改已出库产品")
    @PutMapping(path = {"/out/obj"}, produces = {"application/json;charset=UTF-8"})
    public ApiResponseBody updateOutObj(@RequestBody SaleProductObjVO objVO) {
        return saleOrderService.updateOutObj(objVO);
    }


    /****************************************** 退换货 **************************************************/

    @ApiOperation("产品退换货-退换货列表")
    @ApiImplicitParam(name = "orderId", value = "订单id", required = true)
    @GetMapping(path = {"/return/list"}, produces = {"application/json;charset=UTF-8"})
    public ApiResponseBody<SaleReturnDetailVO> returnPage(@RequestParam String orderId) {
        return saleOrderService.returnPage(orderId);
    }

    @ApiOperation("产品退换货-发起退换货")
    @PostMapping(path = {"/return"}, produces = {"application/json;charset=UTF-8"})
    public ApiResponseBody returnBatch(@RequestBody SaleReturnParam param) {
        return saleOrderService.returnBatch(param);
    }

    @ApiOperation("产品退换货-删除退换货")
    @ApiImplicitParam(name = "saleOrderReturnId", value = "退换货记录id", required = true)
    @DeleteMapping(path = {"/return"}, produces = {"application/json;charset=UTF-8"})
    public ApiResponseBody deleteReturn(@RequestParam(value = "saleOrderReturnId", defaultValue = "") String saleOrderReturnId) {
        return saleOrderService.deleteReturn(saleOrderReturnId);
    }

    @ApiOperation("产品退换货-修改退换货产品")
    @PutMapping(path = {"/return/obj"}, produces = {"application/json;charset=UTF-8"})
    public ApiResponseBody updateReturnObj(@RequestBody SaleProductObjVO objVO) {
        return saleOrderService.updateReturnObj(objVO);
    }

    @ApiOperation("产品退换货-查询产品详情")
    @ApiImplicitParam(name = "saleOrderReturnId", value = "退换货记录id", required = true)
    @GetMapping(path = {"/return/obj"}, produces = {"application/json;charset=UTF-8"})
    public ApiResponseBody<SaleProductObjVO> queryReturnObj(@RequestParam(value = "saleOrderReturnId", defaultValue = "") String saleOrderReturnId) {
        return saleOrderService.queryReturnObj(saleOrderReturnId);
    }
}
