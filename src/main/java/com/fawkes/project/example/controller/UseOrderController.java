package com.fawkes.project.example.controller;

import com.fawkes.core.base.api.ApiResponseBody;
import com.fawkes.project.example.domain.param.UseOrderPageParam;
import com.fawkes.project.example.domain.vo.StatUseOrderOverallVO;
import com.fawkes.project.example.domain.vo.StatUseOrderVO;
import com.fawkes.project.example.domain.vo.UseOrderVO;
import com.fawkes.project.example.service.impl.UseOrderService;
import com.github.pagehelper.PageInfo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.time.Year;
import java.util.List;

/**
 * <AUTHOR>
 */
@Api(tags = {"设备领用订单管理"})
@RestController
@RequestMapping("/use")
public class UseOrderController {

    @Resource
    UseOrderService useOrderService;

    @ApiOperation("获取最新的订单编号")
    @GetMapping(path = {"/latestNo"}, produces = {"application/json;charset=UTF-8"})
    public ApiResponseBody getLatestOrderNo() {
        return useOrderService.getLatestOrderNo();
    }

    /****************************************** 设备领用 **************************************************/
    @ApiOperation("获取工艺可用库存")
    @ApiImplicitParam(name = "craftId", value = "工艺id", required = true)
    @GetMapping(path = {"/obj/list"}, produces = {"application/json;charset=UTF-8"})
    public ApiResponseBody getObjList(@RequestParam String craftId) {
        return useOrderService.getObjList(craftId);
    }

    @ApiOperation("添加")
    @PostMapping(path = {"/order"}, produces = {"application/json;charset=UTF-8"})
    public ApiResponseBody add(@RequestBody UseOrderVO useOrderVO) {
        return useOrderService.add(useOrderVO);
    }

    @ApiOperation("详情")
    @ApiImplicitParam(name = "orderId", value = "领用订单id", required = true)
    @GetMapping(path = {"/order"}, produces = {"application/json;charset=UTF-8"})
    public ApiResponseBody<UseOrderVO> query(@RequestParam String orderId) {
        return useOrderService.query(orderId);
    }

    @ApiOperation("批量删除订单")
    @ApiImplicitParam(name = "ids", value = "订单id")
    @DeleteMapping(path = {"/order"}, produces = {"application/json;charset=UTF-8"})
    public ApiResponseBody delete(@RequestParam(value = "ids") String ids) {
        return useOrderService.deleteByIds(ids);
    }

    @ApiOperation("修改")
    @PutMapping(path = {"/order"}, produces = {"application/json;charset=UTF-8"})
    public ApiResponseBody update(@RequestBody UseOrderVO useOrderVO) {
        return useOrderService.update(useOrderVO);
    }

    @ApiOperation("分页查询")
    @PostMapping(path = {"/order/list"}, produces = {"application/json;charset=UTF-8"})
    public ApiResponseBody<PageInfo<UseOrderVO>> page(@RequestBody UseOrderPageParam param) {
        return useOrderService.page(param);
    }

    @ApiOperation("统计-总览")
    @GetMapping(path = {"/stat/overall"}, produces = {"application/json;charset=UTF-8"})
    public ApiResponseBody<StatUseOrderOverallVO> statOverall() {
        return useOrderService.statOverall();
    }

    @ApiOperation("统计-设备统计")
    @ApiImplicitParam(name = "searchYear", value = "年份", required = true)
    @GetMapping(path = {"/stat/obj"}, produces = {"application/json;charset=UTF-8"})
    public ApiResponseBody<List<StatUseOrderVO>> statUseObj(@RequestParam Year searchYear) {
        return useOrderService.statUseObj(searchYear);
    }
}
