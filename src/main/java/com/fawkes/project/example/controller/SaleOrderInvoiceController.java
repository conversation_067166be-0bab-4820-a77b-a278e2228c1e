package com.fawkes.project.example.controller;

import com.fawkes.core.base.api.ApiResponseBody;
import com.fawkes.project.example.common.model.SaleOrderInvoice;
import com.fawkes.project.example.service.impl.SaleOrderInvoiceServiceImpl;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 */
@Api(tags = {"销售订单-合同收款"})
@RestController
@RequestMapping("/sale/invoice")
public class SaleOrderInvoiceController {
    @Resource
    SaleOrderInvoiceServiceImpl saleOrderInvoiceService;

    /****************************************** 合同收款 **************************************************/
    @ApiOperation("添加")
    @PostMapping(produces = {"application/json;charset=UTF-8"})
    public ApiResponseBody add(@RequestBody SaleOrderInvoice orderInvoice) {
        return saleOrderInvoiceService.add(orderInvoice);
    }

    @ApiOperation("详情")
    @ApiImplicitParam(name = "id", value = "合同id", required = true)
    @GetMapping(produces = {"application/json;charset=UTF-8"})
    public ApiResponseBody query(@RequestParam String id) {
        return saleOrderInvoiceService.query(id);
    }

    @ApiOperation("删除")
    @ApiImplicitParam(name = "id", value = "合同id")
    @DeleteMapping(produces = {"application/json;charset=UTF-8"})
    public ApiResponseBody delete(@RequestParam(value = "id") String id) {
        return saleOrderInvoiceService.delete(id);
    }

    @ApiOperation("修改")
    @PutMapping(produces = {"application/json;charset=UTF-8"})
    public ApiResponseBody update(@RequestBody SaleOrderInvoice orderInvoice) {
        return saleOrderInvoiceService.update(orderInvoice);
    }

    @ApiOperation("列表查询")
    @ApiImplicitParam(name = "orderId", value = "订单id", required = true)
    @GetMapping(path = {"/list"}, produces = {"application/json;charset=UTF-8"})
    public ApiResponseBody page(@RequestParam String orderId) {
        return saleOrderInvoiceService.page(orderId);
    }
}
