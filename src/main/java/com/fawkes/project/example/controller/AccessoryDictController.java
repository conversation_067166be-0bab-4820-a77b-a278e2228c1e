package com.fawkes.project.example.controller;

import com.fawkes.core.base.api.ApiResponseBody;
import com.fawkes.project.example.common.model.AccessoryDict;
import com.fawkes.project.example.domain.dto.AccessoryDictDTO;
import com.fawkes.project.example.service.impl.AccessoryDictService;
import com.github.pagehelper.PageInfo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;


/**
 * <AUTHOR>
 */
@Api(tags = {"配件字典管理"})
@RestController
@RequestMapping("/accessory/dict")
public class AccessoryDictController {
    @Resource
    private AccessoryDictService accessoryDictService;

    @ApiOperation("添加")
    @PostMapping(produces = {"application/json;charset=UTF-8"})
    public ApiResponseBody add(@RequestBody AccessoryDict dict) {
        return accessoryDictService.add(dict);
    }

    @ApiOperation("删除")
    @ApiImplicitParam(name = "id", value = "字典id")
    @DeleteMapping(produces = {"application/json;charset=UTF-8"})
    public ApiResponseBody delete(@RequestParam(value = "id") String id) {
        return accessoryDictService.delete(id);
    }

    @ApiOperation("修改")
    @PutMapping(produces = {"application/json;charset=UTF-8"})
    public ApiResponseBody update(@RequestBody AccessoryDict accessoryDict) {
        return accessoryDictService.update(accessoryDict);
    }

    @ApiOperation("配件字典分页查询")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "typeName", value = "种类名称", defaultValue = ""),
            @ApiImplicitParam(name = "typeCode", value = "种类编码", defaultValue = ""),
            @ApiImplicitParam(name = "pageNum", value = "页码", defaultValue = "1"),
            @ApiImplicitParam(name = "pageSize", value = "每页条数", defaultValue = "10")})
    @GetMapping(path = "/list", produces = {"application/json;charset=UTF-8"})
    public ApiResponseBody<PageInfo<AccessoryDictDTO>> page(@RequestParam(value = "typeName", defaultValue = "") String typeName,
                                                            @RequestParam(value = "typeCode", defaultValue = "") String typeCode,
                                                            @RequestParam(value = "pageNum", defaultValue = "1") Integer pageNum,
                                                            @RequestParam(value = "pageSize", defaultValue = "10") Integer pageSize) {
        return accessoryDictService.page(typeName, typeCode, pageNum, pageSize);
    }
}
