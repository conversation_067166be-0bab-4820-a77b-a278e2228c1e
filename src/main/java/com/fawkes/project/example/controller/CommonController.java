//package com.fawkes.project.example.controller;
//
//
//import com.fawkes.core.base.api.ApiResponseBody;
//import com.fawkes.core.utils.EntityTool;
//import com.fawkes.project.example.common.mapper.AccessoryDictMapper;
//import com.fawkes.project.example.common.mapper.AccessoryMapper;
//import com.fawkes.project.example.common.model.Accessory;
//import com.fawkes.project.example.common.model.AccessoryDict;
//import com.fawkes.project.example.common.param.FormCommitParam;
//import com.fawkes.project.example.common.param.FormQueryParam;
//import com.fawkes.project.example.domain.dto.AccessoryDictDTO;
//import com.fawkes.project.example.domain.vo.AccessoryVO;
//import com.fawkes.project.example.service.ICommonService;
//import io.swagger.annotations.Api;
//import io.swagger.annotations.ApiOperation;
//import org.springframework.util.CollectionUtils;
//import org.springframework.web.bind.annotation.*;
//
//import javax.annotation.Resource;
//import java.sql.Timestamp;
//import java.util.ArrayList;
//import java.util.List;
//import java.util.Map;
//import java.util.stream.Collectors;
//import java.util.stream.IntStream;
//
///**
// * <AUTHOR>
// */
//@Api(tags = {"测试数据"})
//@RestController
//@RequestMapping("/test/init")
//public class CommonController {
//    @Resource
//    AccessoryDictMapper accessoryDictMapper;
//
//    @Resource
//    AccessoryMapper accessoryMapper;
//
//    @ApiOperation("配件字典")
//    @GetMapping(path = "/accessoryDict", produces = {"application/json;charset=UTF-8"})
//    public ApiResponseBody initAccessoryDict() {
//        List<AccessoryDictDTO> typeList = accessoryDictMapper.selectAccessoryDictType(null, null);
//        if (!CollectionUtils.isEmpty(typeList)) {
//            return ApiResponseBody.defaultSuccess("请手动清理数据");
//        }
//        List<String> alphabet = IntStream.rangeClosed('A', 'Z')
//                .mapToObj(i -> String.valueOf((char) i))
//                .collect(Collectors.toList());
//        List<AccessoryDict> list = new ArrayList<>();
//        alphabet.forEach(parentCode -> {
//            AccessoryDict parent = new AccessoryDict();
//            parent.setDictCode(parentCode);
//            parent.setDictName(parentCode);
//            parent.setDictRemark("初始化 -- " + parentCode);
//            EntityTool.insertEntity(parent);
//            list.add(parent);
//            IntStream.rangeClosed(1, 9).forEach(child -> {
//                AccessoryDict childDict = new AccessoryDict();
//                childDict.setDictCode(String.valueOf(child));
//                childDict.setDictName(String.valueOf(child));
//                childDict.setDictParent(parent.getId());
//                childDict.setDictRemark("初始化 -- " + child);
//                EntityTool.insertEntity(childDict);
//                list.add(childDict);
//            });
//        });
//        list.forEach(dict -> {
//            Timestamp date = new Timestamp(System.currentTimeMillis());
//            dict.setCreateDate(date);
//            dict.setUpdateDate(date);
//        });
//        accessoryDictMapper.insertBatch(list);
//        return ApiResponseBody.defaultSuccess("[OK] 初始化数据 -- 配件字典");
//    }
//
//    @ApiOperation("配件")
//    @GetMapping(path = "/accessory", produces = {"application/json;charset=UTF-8"})
//    public ApiResponseBody initAccessory() {
//        List<AccessoryVO> recordList = accessoryMapper.selectAccessoryList (null, null, null);
//        if (!CollectionUtils.isEmpty(recordList)) {
//            return ApiResponseBody.defaultSuccess("请手动清理数据");
//        }
//
//        List<Accessory> list = new ArrayList<>();
//
//        List<AccessoryDictDTO> typeList = accessoryDictMapper.selectAccessoryDictType(null, null);
//        List<Long> typeIds = typeList.stream().map(AccessoryDict::getId).collect(Collectors.toList());
//        Map<Long, List<AccessoryDict>> refMap = accessoryDictMapper.selectByDictParentIds(typeIds).stream().collect(Collectors.groupingBy(AccessoryDict::getDictParent));
//        typeList.forEach(parent -> {
//            refMap.get(parent.getId()).forEach(child -> {
//                Accessory accessory = new Accessory();
//                accessory.setCategoryId(parent.getId());
//                accessory.setRefId(child.getId());
//                accessory.setAccessoryUnit("台");
//                accessory.setAccessoryType("原材料");
//                accessory.setAccessorySupplier("初始化 -- 厂商名称");
//                accessory.setAccessorySpec("初始化 -- 规格");
//                accessory.setAccessoryStorage("初始化 -- 存放位置");
//                accessory.setAccessoryRemark("初始化 -- 用途说明");
//                list.add(accessory);
//            });
//        });
//        EntityTool.insertEntity(list);
//        accessoryMapper.insertBatch(list);
//
//        return ApiResponseBody.defaultSuccess("[OK] 初始化数据 -- 配件");
//    }
//
//}
