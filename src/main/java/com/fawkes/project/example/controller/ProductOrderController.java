package com.fawkes.project.example.controller;

import com.alibaba.fastjson.JSON;
import com.fawkes.core.base.api.ApiResponseBody;
import com.fawkes.core.constants.BaseConstants;
import com.fawkes.project.example.common.config.LocalCache;
import com.fawkes.project.example.common.model.CheckCalib;
import com.fawkes.project.example.common.model.ProductOrder;
import com.fawkes.project.example.common.utils.Utils;
import com.fawkes.project.example.domain.param.*;
import com.fawkes.project.example.domain.vo.*;
import com.fawkes.project.example.service.impl.ProductOrderService;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.ObjectUtils;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.List;
import java.util.UUID;
import java.util.concurrent.TimeUnit;

/**
 * 产品订单控制器
 *
 * <AUTHOR>
 */
@Api(tags = "产品订单")
@RestController
@Slf4j
@RequestMapping("/product/order")
public class ProductOrderController {

    @Autowired
    private ProductOrderService productOrderService;

    @Autowired
    private LocalCache localCache;

    /**
     * 产品订单及检验列表
     */
    @ApiOperation("产品订单列表")
    @PostMapping("list")
    public ApiResponseBody list(@RequestBody ProductOrderQo qo) {
        PageHelper.startPage(qo.getPageNum(), qo.getPageSize());
        List<ProductOrder> productOrders = productOrderService.list(qo);
        return ApiResponseBody.defaultSuccess(new PageInfo<>(productOrders));
    }

    /**
     * 获取某年下一个批次
     */
    @ApiOperation("获取某年下一个批次")
    @PostMapping("getNextBatch/{year}")
    public ApiResponseBody getNextBatch(@PathVariable Integer year) {
        Integer maxBatch = productOrderService.getMaxBatch(year);
        return ApiResponseBody.defaultSuccess(String.format("%03d", maxBatch + 1));
    }

    /**
     * 由工艺及数量获取配件使用情况
     */
    @ApiOperation("由工艺及数量获取配件使用情况")
    @PostMapping("accessoryUseInfo")
    public ApiResponseBody accessoryUseInfo(@RequestBody List<ProductCraftQo> qos) {
        List<AccessoryUseInfoVo> vos = productOrderService.accessoryUseInfo(qos);
        return ApiResponseBody.defaultSuccess(vos);
    }

    /**
     * 新增产品订单
     */
    @ApiOperation("新增产品订单")
    @PostMapping("add")
    public ApiResponseBody add(@RequestBody ProductOrder productOrder) {
        try {
            productOrderService.add(productOrder);
        } catch (Exception e) {
            return ApiResponseBody.error(BaseConstants.BASIC_ERROR_CODE, e.getMessage());
        }
        return ApiResponseBody.defaultSuccess("新增成功");
    }

    /**
     * 产品订单数量统计
     */
    @ApiOperation("产品订单数量统计")
    @GetMapping("stat/num")
    public ApiResponseBody statNum() {
        ProductOrderStatVo vo = productOrderService.statNum();
        return ApiResponseBody.defaultSuccess(vo);
    }

    /**
     * 产品检验数量统计
     */
    @ApiOperation("产品检验数量统计")
    @GetMapping("check/stat/num")
    public ApiResponseBody checkStatNum() {
        ProductCheckStatVo vo = productOrderService.checkStatNum();
        return ApiResponseBody.defaultSuccess(vo);
    }

    /**
     * 产品检验状态统计
     */
    @ApiOperation("产品检验状态统计")
    @GetMapping("check/stat/status/{year}")
    public ApiResponseBody checkStatStatus(@PathVariable Integer year) {
        if (year < 1970) {
            year = null;
        }
        List<ProductCheckStatusStatVo> vos = productOrderService.checkStatStatus(year);
        return ApiResponseBody.defaultSuccess(vos);
    }

    /**
     * 产品累计生产量
     */
    @ApiOperation("产品累计生产量")
    @GetMapping("stat/product/num/{year}")
    public ApiResponseBody statProductNum(@PathVariable Integer year) {
        if (year < 1970) {
            year = null;
        }
        List<ProductOrderProductStatVo> vos = productOrderService.statProductNum(year);
        return ApiResponseBody.defaultSuccess(vos);
    }

    @ApiOperation("修改产品订单")
    @PutMapping("update")
    public ApiResponseBody update(@RequestBody ProductOrder productOrder) {
        try {
            productOrderService.update(productOrder);
        } catch (Exception e) {
            return ApiResponseBody.error(BaseConstants.BASIC_ERROR_CODE, e.getMessage());
        }
        return ApiResponseBody.defaultSuccess("修改成功");
    }

    @ApiOperation("检验订单下产品是否被使用")
    @GetMapping("product/used/{orderId}")
    public ApiResponseBody<List<ProductObjUsedVO>> getUsedProductInOder(@PathVariable Long orderId) {
        return productOrderService.getUsedProductInOder(orderId);
    }

    /**
     * 获取产品生产订单信息（编辑）
     *
     * @param orderId
     * @return
     */
    @ApiOperation("获取产品生产订单采购信息（编辑）")
    @GetMapping("info/{orderId}")
    public ApiResponseBody<ProductOrder> productOrderInfo(@PathVariable Long orderId) {
        return productOrderService.productOrderInfo(orderId);
    }

    /**
     * 产品订单详情
     */
    @ApiOperation("产品订单详情")
    @GetMapping("detail/{orderId}")
    public ApiResponseBody detail(@PathVariable Long orderId) {
        ProductOrderDetailVo vo = productOrderService.detail(orderId);
        return ApiResponseBody.defaultSuccess(vo);
    }

    /**
     * 删除产品订单
     */
    @ApiOperation("删除产品订单")
    @DeleteMapping("del/{ids}")
    public ApiResponseBody del(@PathVariable Long[] ids) {
        productOrderService.del(ids);
        return ApiResponseBody.defaultSuccess("删除成功");
    }

    /**
     * 检验明细
     */
    @ApiOperation("检验明细")
    @PostMapping("check/detail")
    public ApiResponseBody checkDetail(@RequestBody CheckDetailQo qo) {
        List<CheckDetailVo> vos = productOrderService.checkDetail(qo);
        return ApiResponseBody.defaultSuccess(vos);
    }

    /**
     * 次品检验
     */
    @ApiOperation("次品检验")
    @PostMapping("check/defective")
    public ApiResponseBody defectiveCheck(@RequestBody PageParam pageParam) {
        PageInfo<CheckDetailVo> pageInfo = new PageInfo<>();
        productOrderService.defectiveCheck(pageInfo, pageParam.getPageNum(), pageParam.getPageSize());
        return ApiResponseBody.defaultSuccess(pageInfo);
    }

    /**
     * 产品检验
     */
    @ApiOperation("产品检验")
    @PostMapping("product/check")
    public ApiResponseBody productCheck(@RequestBody CheckQo qo) {
        productOrderService.productCheck(qo);
        return ApiResponseBody.defaultSuccess("检验成功");
    }

    /**
     * 获取所有产品检验责任人
     *
     * @return
     */
    @ApiOperation("产品检验责任人下拉选项")
    @GetMapping("product/check/user/list")
    public ApiResponseBody getAllProductCheckUser() {
        List<String> userList = productOrderService.getAllProductCheckUser();
        return ApiResponseBody.defaultSuccess(userList);
    }

    /**
     * 设置率定参数
     */
    @ApiOperation("设置率定参数")
    @PostMapping("setCalib")
    public ApiResponseBody setCalib(@RequestBody List<CheckCalib> checkCalibs) {
        productOrderService.setCalib(checkCalibs);
        return ApiResponseBody.defaultSuccess("设置成功");
    }

    /**
     * 率定参数模板下载(提交参数)
     */
    @ApiOperation("率定参数模板下载(提交参数)")
    @PostMapping("calib/template/submit")
    public ApiResponseBody templateSubmit(@RequestBody CheckDetailQo qo) {
        /* 参数保存至缓存，并设置失效时间 */
        String uuid = UUID.randomUUID().toString();
        localCache.set(uuid, JSON.toJSONString(qo), 10, TimeUnit.MINUTES);
        return ApiResponseBody.defaultSuccess(uuid);
    }

    /**
     * 率定参数模板下载(启动下载)
     */
    @ApiOperation("率定参数模板下载(启动下载)")
    @GetMapping("calib/template/launch/{uuid}")
    public void templateLaunch(@PathVariable String uuid, HttpServletResponse response) throws IOException {
        Object o = localCache.get(uuid);
        if (ObjectUtils.isEmpty(o)) {
            response.setContentType("text/html;charset=utf-8");
            response.getWriter().println("链接已失效（10分钟），请重新导出");
            return;
        }

        productOrderService.calibTemplateDown(response, JSON.parseObject((String) o, CheckDetailQo.class));
    }

    /**
     * 率定参数批量导入
     */
    @ApiOperation("率定参数批量导入")
    @PostMapping("calib/import/{orderId}")
    public ApiResponseBody calibImport(MultipartFile file, @PathVariable Long orderId) {
        String filepath;
        try {
            filepath = Utils.saveFileToLocal(file);
        } catch (IOException e) {
            e.printStackTrace();
            return ApiResponseBody.error(500, "服务器文件保存失败，请联系管理员");
        }

        String res;
        try {
            res = productOrderService.calibImport(filepath, orderId);
        } catch (Exception e) {
            String msg = "非法模板，请下载正确模板";
            if (!ObjectUtils.isEmpty(e.getMessage())) {
                msg = e.getMessage();
            }
            return ApiResponseBody.error(400, msg);
        }

        if (res == null) {
            return ApiResponseBody.defaultSuccess("导入成功");
        }
        return ApiResponseBody.error(400, res);
    }

    /**
     * 生成合格证(提交参数)
     */
    @ApiOperation("生成合格证(提交参数)")
    @PostMapping("gen/certificate/submit")
    public ApiResponseBody genSubmit(@RequestBody List<Long> ids) {
        /* 参数保存至缓存，并设置失效时间 */
        String uuid = UUID.randomUUID().toString();
        localCache.set(uuid, JSON.toJSONString(ids), 10, TimeUnit.MINUTES);
        return ApiResponseBody.defaultSuccess(uuid);
    }

    /**
     * 生成合格证(启动下载)
     */
    @ApiOperation("生成合格证(启动下载)")
    @GetMapping("gen/certificate/launch/{uuid}")
    public void genLaunch(@PathVariable String uuid, HttpServletResponse response) throws Exception {
        Object o = localCache.get(uuid);
        if (ObjectUtils.isEmpty(o)) {
            response.setContentType("text/html;charset=utf-8");
            response.getWriter().println("链接已失效，请重新导出");
            return;
        }

        productOrderService.genCertificate(response, JSON.parseArray((String) o, Long.class));
    }

    /**
     * 产品库存统计列表
     */
    @ApiOperation("产品库存统计列表")
    @PostMapping("product/inventory/list")
    public ApiResponseBody productInventoryList(@RequestBody CheckDetailQo qo) {
        PageInfo<ProductInventoryVo> pageInfo = new PageInfo<>();
        productOrderService.productInventoryList(pageInfo, qo);
        return ApiResponseBody.defaultSuccess(pageInfo);
    }

    /**
     * 产品库存统计数量
     */
    @ApiOperation("产品库存统计数量")
    @GetMapping("product/inventory")
    public ApiResponseBody productInventoryNum(@RequestParam(value = "year", defaultValue = "", required = false) String year) {
        ProductInventoryVo vo = productOrderService.productInventoryNum(year);
        return ApiResponseBody.defaultSuccess(vo);
    }


    @ApiOperation("产品使用状态")
    @PostMapping("product/status/list")
    public ApiResponseBody<PageInfo<ProductStatusVO>> productStatusList(@RequestBody ProductStatusParam param) {
        return productOrderService.productStatusList(param);
    }

}
