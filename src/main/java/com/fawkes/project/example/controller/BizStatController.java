package com.fawkes.project.example.controller;


import com.fawkes.core.base.api.ApiResponseBody;
import com.fawkes.project.example.domain.vo.*;
import com.fawkes.project.example.service.impl.BizStatService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.time.Year;
import java.util.List;

/**
 * <AUTHOR>
 */
@Api(tags = {"经营统计"})
@RestController
@RequestMapping("/stat")
public class BizStatController {

    @Resource
    BizStatService bizStatService;

    /****************************************** 收款利润统计 **************************************************/
    @ApiOperation("收款利润-总体统计")
    @ApiImplicitParam(name = "searchYear", value = "统计年份")
    @GetMapping(path = {"/profit"}, produces = {"application/json;charset=UTF-8"})
    public ApiResponseBody<BizStatProfitVO> statProfit(@RequestParam(value = "searchYear", defaultValue = "") Year searchYear) {
        return bizStatService.statProfit(searchYear);
    }

    @ApiOperation("收款利润-收款趋势")
    @ApiImplicitParam(name = "searchYear", value = "统计年份", required = true)
    @GetMapping(path = {"/profit/clientPayment"}, produces = {"application/json;charset=UTF-8"})
    public ApiResponseBody statClientPaymentByYear(@RequestParam Year searchYear) {
        return bizStatService.statClientPaymentByYear(searchYear);
    }

    @ApiOperation("收款利润-销售趋势")
    @ApiImplicitParam(name = "searchYear", value = "统计年份", required = true)
    @GetMapping(path = {"/profit/clientSale"}, produces = {"application/json;charset=UTF-8"})
    public ApiResponseBody statClientSaleByYear(@RequestParam Year searchYear) {
        return bizStatService.statClientSaleByYear(searchYear);
    }

    /****************************************** 付款统计 **************************************************/
    @ApiOperation("付款统计-总体统计")
    @ApiImplicitParam(name = "searchYear", value = "统计年份")
    @GetMapping(path = {"/purchase"}, produces = {"application/json;charset=UTF-8"})
    public ApiResponseBody<BizStatPurchaseVO> statPurchase(@RequestParam(value = "searchYear", defaultValue = "") Year searchYear) {
        return bizStatService.statPurchase(searchYear);
    }

    @ApiOperation("付款统计-采购/支付趋势")
    @ApiImplicitParam(name = "searchYear", value = "统计年份", required = true)
    @GetMapping(path = {"/purchase/month"}, produces = {"application/json;charset=UTF-8"})
    public ApiResponseBody<List<BizStatPurchaseMonthVO>> statPurchaseMonth(@RequestParam Year searchYear) {
        return bizStatService.statPurchaseMonth(searchYear);
    }


    /****************************************** 产品库存管理 **************************************************/
    @ApiOperation("产品库存-产品入库")
    @ApiImplicitParam(name = "productId", value = "产品id", required = true)
    @GetMapping(path = {"/product/in"}, produces = {"application/json;charset=UTF-8"})
    public ApiResponseBody<List<StatProductInVO>> listProductOrderInByProductId(@RequestParam("productId") String productId) {
        return bizStatService.listProductOrderInByProductId(productId);
    }

    @ApiOperation("产品库存-产品出库")
    @ApiImplicitParam(name = "productId", value = "产品id", required = true)
    @GetMapping(path = {"/product/out"}, produces = {"application/json;charset=UTF-8"})
    public ApiResponseBody<List<StatProductOutVO>> listProductOrderOutByProductId(@RequestParam("productId") String productId) {
        return bizStatService.listProductOrderOutByProductId(productId);
    }


    /****************************************** 配件库存管理 **************************************************/
    @ApiOperation("配件库存-配件入库")
    @ApiImplicitParam(name = "accessoryId", value = "配件id", required = true)
    @GetMapping(path = {"/accessory/in"}, produces = {"application/json;charset=UTF-8"})
    public ApiResponseBody<List<AccessoryOrderGoodsVO>> listAccessoryInByAccessoryId(@RequestParam("accessoryId") String accessoryId) {
        return bizStatService.listAccessoryInByAccessoryId(accessoryId);
    }

    @ApiOperation("配件库存-配件出库")
    @ApiImplicitParam(name = "accessoryId", value = "配件id", required = true)
    @GetMapping(path = {"/accessory/out"}, produces = {"application/json;charset=UTF-8"})
    public ApiResponseBody<List<StatAccessoryOutVO>> listAccessoryOutByAccessoryId(@RequestParam("accessoryId") String accessoryId) {
        return bizStatService.listAccessoryOutByAccessoryId(accessoryId);
    }
}
