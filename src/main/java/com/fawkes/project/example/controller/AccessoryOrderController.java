package com.fawkes.project.example.controller;

import com.fawkes.core.base.api.ApiResponseBody;
import com.fawkes.project.example.domain.param.AccessoryOrderQueryParam;
import com.fawkes.project.example.domain.param.AccessoryOutOrderQueryParam;
import com.fawkes.project.example.domain.vo.AccessoryOrderVO;
import com.fawkes.project.example.service.impl.AccessoryOrderService;
import com.github.pagehelper.PageInfo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.format.annotation.DateTimeFormat;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.time.LocalDate;

/**
 * <AUTHOR>
 */
@Api(tags = {"配件明细管理"})
@Slf4j
@RestController
@RequestMapping("/accessory/order")
public class AccessoryOrderController {

    @Resource
    AccessoryOrderService accessoryOrderService;

    @ApiOperation("获取最新的订单编号")
    @ApiImplicitParam(name = "orderDate", value = "订单日期", required = true)
    @GetMapping(path = {"/latestNo"}, produces = {"application/json;charset=UTF-8"})
    public ApiResponseBody getLatestOrderNo(@RequestParam(value = "orderDate") @DateTimeFormat(pattern = "yyyy-MM-dd") LocalDate orderDate) {
        return accessoryOrderService.getLatestOrderNo(orderDate);
    }

    @ApiOperation("配件入库")
    @PostMapping(produces = {"application/json;charset=UTF-8"})
    public ApiResponseBody add(@RequestBody AccessoryOrderVO accessoryOrderVO) {
        return accessoryOrderService.add(accessoryOrderVO);
    }

    @ApiOperation("查询配件入库订单")
    @ApiImplicitParam(name = "id", value = "fawkesId", required = true)
    @GetMapping(produces = {"application/json;charset=UTF-8"})
    public ApiResponseBody<AccessoryOrderVO> query(@RequestParam String id) {
        return accessoryOrderService.query(id);
    }

    @ApiOperation("修改配件入库订单")
    @PutMapping(produces = {"application/json;charset=UTF-8"})
    public ApiResponseBody update(@RequestBody AccessoryOrderVO accessoryOrderVO) {
        return accessoryOrderService.update(accessoryOrderVO);
    }

    @ApiOperation("删除配件入库订单")
    @ApiImplicitParam(name = "id", value = "fawkesId")
    @DeleteMapping(produces = {"application/json;charset=UTF-8"})
    public ApiResponseBody delete(@RequestParam(value = "id") String id) {
        return accessoryOrderService.delete(id);
    }

    @ApiOperation("配件入库订单分页查询")
    @PostMapping(path = "/list", produces = {"application/json;charset=UTF-8"})
    public ApiResponseBody<PageInfo<AccessoryOrderVO>> page(@RequestBody AccessoryOrderQueryParam accessoryOrderQueryParam) {
        return accessoryOrderService.page(accessoryOrderQueryParam);
    }

    @ApiOperation("配件出库订单分页查询")
    @PostMapping(path = "/out/list", produces = {"application/json;charset=UTF-8"})
    public ApiResponseBody outPage(@RequestBody AccessoryOutOrderQueryParam param) {
        return accessoryOrderService.outPage(param);
    }

    @ApiOperation("订单支付状态统计")
    @GetMapping(path = "/stat/paid", produces = {"application/json;charset=UTF-8"})
    public ApiResponseBody statPaid() {
        return accessoryOrderService.statPaid();
    }
}
