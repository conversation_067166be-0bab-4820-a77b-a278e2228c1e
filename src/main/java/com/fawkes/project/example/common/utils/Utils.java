package com.fawkes.project.example.common.utils;

import com.fawkes.project.example.runner.Cache;
import org.springframework.web.multipart.MultipartFile;

import java.io.*;
import java.util.List;

public class Utils {

    /**
     * 将上传文件保存到服务器本地临时目录
     */
    public static String saveFileToLocal(MultipartFile file) throws IOException {
        // 将文件存储在项目root/tmp下
        File localFile = new File(Cache.ftlPath, System.currentTimeMillis() + "-" + file.getOriginalFilename());
        if (!localFile.exists()) {
            localFile.mkdirs();
        }
        file.transferTo(localFile);
        return localFile.getAbsolutePath();
    }

    /**
     * 判断当前服务器是否是linux
     */
    public static boolean isLinux() {
        String os = System.getProperty("os.name").toLowerCase();
        return os.indexOf("linux") >= 0;
    }

    /***
     * 功能描述: set<bean> 深拷贝
     * (bean 对象必须序列化，即 implements Serializable)
     */
    public static <T> List<T> deepCopyListBean(List<T> src) {
        try {
            ByteArrayOutputStream byteOut = new ByteArrayOutputStream();//目标输出流
            ObjectOutputStream out = null;//对象输出流
            out = new ObjectOutputStream(byteOut);
            //对参数指定的obj对象进行序列化，把得到的字节序列写到一个目标输出流中
            out.writeObject(src);
            ByteArrayInputStream byteIn = new ByteArrayInputStream(byteOut.toByteArray());
            ObjectInputStream in = new ObjectInputStream(byteIn);//对象输入流
            //readObject()方法从一个源输入流中读取 字节序列，再把它们反序列化为一个对象
            return (List<T>) in.readObject();
        } catch (IOException e) {
            e.printStackTrace();
        } catch (ClassNotFoundException ex) {
            ex.printStackTrace();
        }
        return null;
    }

}
