<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.fawkes.project.example.common.mapper.ClientMapper">
    <resultMap id="Client" type="com.fawkes.project.example.common.model.Client"
               extends="com.fawkes.project.example.common.mapper.BaseMapper.BaseEntity">
        <result column="client_no" jdbcType="VARCHAR" property="clientNo"/>
        <result column="client_name" jdbcType="VARCHAR" property="clientName"/>
        <result column="client_type" jdbcType="VARCHAR" property="clientType"/>
        <result column="province" jdbcType="VARCHAR" property="province"/>
        <result column="city" jdbcType="VARCHAR" property="city"/>
        <result column="region" jdbcType="VARCHAR" property="region"/>
        <result column="address" jdbcType="VARCHAR" property="address"/>
    </resultMap>
    <resultMap id="ClientVO" type="com.fawkes.project.example.domain.vo.ClientVO" extends="Client">
        <result column="first_deal_date" jdbcType="DATE" property="firstDealDate"/>
        <result column="recent_deal_date" jdbcType="DATE" property="recentDealDate"/>
    </resultMap>
    <sql id="Column_List_Client">
        id
        , create_by, create_date, update_by, update_date, delete_flag, client_no, client_name, client_type, province, city, region, address
    </sql>

    <select id="isExistedClientName" resultType="java.lang.Boolean">
        SELECT COUNT( * ) FROM client WHERE delete_flag = 0 AND client_name = #{client.clientName}
        <if test="client.id != null">AND id != #{client.id}</if>
    </select>

    <select id="getMaxClientNoByClientType" resultType="java.lang.String">
        SELECT MAX(client_no)
        FROM client
        WHERE client_type = #{clientType}
    </select>

    <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="Client">
        select
        <include refid="Column_List_Client"/>
        from client where id = #{id,jdbcType=BIGINT}
    </select>

    <select id="selectClientList" parameterType="com.fawkes.project.example.domain.param.ClientParam"
            resultMap="ClientVO">
        SELECT
        o.id,
        o.create_by,
        o.create_date,
        o.update_by,
        o.update_date,
        o.delete_flag,
        o.client_no,
        o.client_name,
        o.client_type,
        o.province,
        o.city,
        o.region,
        o.address,
        temp.first_deal_date first_deal_date,
        temp.recent_deal_date recent_deal_date
        FROM client o RIGHT JOIN
        ( SELECT
        a.id,
        MIN( b.sign_date ) AS first_deal_date,
        MAX( b.sign_date ) AS recent_deal_date
        FROM
        client a
        LEFT JOIN sale_order b ON b.client_id = a.id AND b.delete_flag = 0
        LEFT JOIN client_contacts c ON c.client_id = a.id AND c.delete_flag = 0 AND c.contacts_type = 2
        <where>
            a.delete_flag = 0
            <if test="param.userInCharge != null and param.userInCharge != ''">
                AND c.contacts_name LIKE CONCAT('%', #{param.userInCharge}, '%')
            </if>
            <if test="param.clientName != null and param.clientName != ''">
                and a.client_name LIKE CONCAT('%', #{param.clientName}, '%')
            </if>
            <if test="param.clientNo != null and param.clientNo != ''">
                and a.client_no LIKE CONCAT('%', #{param.clientNo}, '%')
            </if>
            <if test="param.clientType != null and param.clientType != ''">
                and a.client_type = #{param.clientType}
            </if>
            <if test="param.industry != null and param.industry != ''">
                and b.industry LIKE CONCAT('%', #{param.industry}, '%')
            </if>
            <if test="param.province != null and param.province != ''">
                and a.province = #{param.province}
            </if>
            <if test="param.city != null and param.city != ''">
                and a.city = #{param.city}
            </if>
            <if test="param.region != null and param.region != ''">
                and a.region = #{param.region}
            </if>
        </where>
        group by a.id) temp ON o.id = temp.id
        <where>
            <if test="param.firstDealDateStart != null and param.firstDealDateStart != ''">
                and temp.first_deal_date >= #{param.firstDealDateStart}
            </if>
            <if test="param.firstDealDateEnd != null and param.firstDealDateEnd != ''">
                <![CDATA[ and temp.first_deal_date <= #{param.firstDealDateEnd} ]]>
            </if>

            <if test="param.recentDealDateStart != null and param.recentDealDateStart != ''">
                and temp.recent_deal_date >= #{param.recentDealDateStart}
            </if>
            <if test="param.recentDealDateEnd != null and param.recentDealDateEnd != ''">
                <![CDATA[ and temp.recent_deal_date <= #{param.recentDealDateEnd} ]]>
            </if>
        </where>
    </select>

    <insert id="insert" parameterType="com.fawkes.project.example.common.model.Client">
        insert into client (id, create_by, create_date,
                            update_by, update_date, delete_flag,
                            client_no, client_name, client_type,
                            province, city, region,
                            address)
        values (#{id,jdbcType=BIGINT}, #{createBy,jdbcType=VARCHAR}, #{createDate,jdbcType=TIMESTAMP},
                #{updateBy,jdbcType=VARCHAR}, #{updateDate,jdbcType=TIMESTAMP}, #{deleteFlag,jdbcType=INTEGER},
                #{clientNo,jdbcType=VARCHAR}, #{clientName,jdbcType=VARCHAR}, #{clientType,jdbcType=VARCHAR},
                #{province,jdbcType=VARCHAR}, #{city,jdbcType=VARCHAR}, #{region,jdbcType=VARCHAR},
                #{address,jdbcType=VARCHAR})
    </insert>

    <update id="deleteBatch">
        <foreach collection="ids" item="id" separator=";">
            update client
            set delete_flag = -1,
            update_by = #{updateInfo.updateBy,jdbcType=VARCHAR},
            update_date = #{updateInfo.updateDate,jdbcType=TIMESTAMP}
            where id = #{id,jdbcType=BIGINT}
        </foreach>
    </update>

    <update id="updateByPrimaryKey" parameterType="com.fawkes.project.example.common.model.Client">
        update client
        set update_by   = #{updateBy,jdbcType=VARCHAR},
            update_date = #{updateDate,jdbcType=TIMESTAMP},
            client_name = #{clientName,jdbcType=VARCHAR},
            client_type = #{clientType,jdbcType=VARCHAR},
            province    = #{province,jdbcType=VARCHAR},
            city        = #{city,jdbcType=VARCHAR},
            region      = #{region,jdbcType=VARCHAR},
            address     = #{address,jdbcType=VARCHAR}
        where id = #{id,jdbcType=BIGINT}
    </update>
</mapper>