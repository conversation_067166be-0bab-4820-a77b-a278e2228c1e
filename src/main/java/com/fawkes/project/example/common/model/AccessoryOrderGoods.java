package com.fawkes.project.example.common.model;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import com.fawkes.core.base.model.BaseEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;

import java.io.Serializable;
import java.math.BigDecimal;

@Data
@ToString
@NoArgsConstructor
public class AccessoryOrderGoods extends BaseEntity implements Serializable {
    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "订单主键id")
    @JsonSerialize(using = ToStringSerializer.class)
    private Long orderId;

    @ApiModelProperty(value = "配件id")
    @JsonSerialize(using = ToStringSerializer.class)
    private Long accessoryId;

    @ApiModelProperty(value = "采购数量")
    private Integer purchaseQuantity;

    @ApiModelProperty(value = "到货数量")
    private Integer arrivalQuantity;

    @ApiModelProperty(value = "采购金额")
    private BigDecimal purchasePrice;
}