package com.fawkes.project.example.common.mapper;

import com.fawkes.project.example.common.model.UseOrder;
import com.fawkes.project.example.common.model.UseOrderGoods;
import com.fawkes.project.example.domain.param.UseOrderPageParam;
import com.fawkes.project.example.domain.vo.ProductObjVO;
import com.fawkes.project.example.domain.vo.UseOrderGoodsVO;
import com.fawkes.project.example.domain.vo.UseOrderVO;
import org.apache.ibatis.annotations.Param;

import java.time.LocalDate;
import java.time.Year;
import java.util.List;

/**
 * <AUTHOR>
 */
public interface UseOrderMapper {

    /**
     * 获取最大订单编号
     *
     * @param searchDate
     * @return
     */
    String getMaxUseOrderNo(@Param("searchDate") LocalDate searchDate);

    /**
     * @param searchYear
     * @return
     * @deprecated 按年份统计订单数量（包含已删除订单）
     */
    Long countByCreateYear(@Param("searchYear") String searchYear);

    /**
     * 获取工艺下的可用库存
     *
     * @param craftId
     * @return
     */
    List<ProductObjVO> listAvailableObjByCraftId(@Param("craftId") Long craftId);

    /**
     * 判断订单编号是否重复
     *
     * @param order
     * @return
     */
    Boolean isExistedOrderNo(@Param("order") UseOrder order);

    /**
     * 获取订单下的领用记录
     *
     * @param orderIds
     * @return
     */
    List<UseOrderGoodsVO> selectUseOrderGoodsByOrderIds(@Param("orderIds") List<Long> orderIds);

    /**
     * 新增
     *
     * @param order
     * @return
     */
    int insert(@Param("order") UseOrder order);

    /**
     * 修改
     *
     * @param order
     * @return
     */
    int update(@Param("order") UseOrder order);

    /**
     * 批量领用产品
     *
     * @param useOrderId
     * @param objIds
     * @return
     */
    int useBatch(@Param("useOrderId") Long useOrderId, @Param("objIds") List<Long> objIds);

    /**
     * 批量记录日志
     *
     * @param goodsList
     * @return
     */
    int logBatch(@Param("goodsList") List<? extends UseOrderGoods> goodsList);

    /**
     * 批量修改日志
     *
     * @param goodsList
     * @return
     */
    int updateLogBatch(@Param("goodsList") List<? extends UseOrderGoods> goodsList);

    /**
     * 批量归还
     *
     * @param objList
     * @return
     */
    int returnUse(@Param("objList") List<ProductObjVO> objList);

    /**
     * 查询订单详情
     *
     * @param orderId
     * @return
     */
    UseOrder selectById(@Param("orderId") Long orderId);

    /**
     * 查询订单详情
     *
     * @param orderIds
     * @return
     */
    List<UseOrder> selectByIds(@Param("orderIds") List<Long> orderIds);

    /**
     * 查询已领用的产品对象
     *
     * @param orderId
     * @return
     */
    List<ProductObjVO> listProductObjByUseOrderId(@Param("orderId") Long orderId);

    /**
     * 查询已领用的产品对象
     *
     * @param orderIds
     * @return
     * @deprecated
     */
    List<ProductObjVO> listProductObjByUseOrderIds(@Param("orderIds") List<Long> orderIds);

    /**
     * 查询已领用的产品对象
     *
     * @param orderIds
     * @return
     */
    List<UseOrderGoodsVO> listUseOrderGoodsByUseOrderIds(@Param("orderIds") List<Long> orderIds);

    /**
     * 按年查询领用订单
     *
     * @param searchYear
     * @return
     */
    List<UseOrder> listByYear(@Param("searchYear") Year searchYear);

    /**
     * 分页查询
     *
     * @param param
     * @return
     */
    List<UseOrderVO> list(@Param("param") UseOrderPageParam param);

    /**
     * 批量删除
     *
     * @param ids
     * @return
     */
    int deleteByIds(@Param("ids") List<Long> ids);
}
