package com.fawkes.project.example.common.constants;

/**
 * @desc: 常量
 * @author: li_y29
 * @create: 2019-12-02
 */
public class FormConstants {
    /**
     * 导出最大条目数
     */
    public final static Integer EXPORT_SIZE_MAX = 5000;
    /**
     * 实体对象后缀
     */
    public final static String FORM_MAPPER = "Mapper";
    /**
     * 实体对象明细后缀
     */
    public final static String FORM_DETAIL = "Detail";
    /**
     * 实体对象example后缀
     */
    public final static String FORM_EXAMPLE = "Example";
    /**
     * ID
     */
    public final static String FORM_ID = "id";
    /**
     * 表单流程状态
     */
    public final static String FORM_PROCESS_STATE = "processState";
    /**
     * 表单流程状态消息交换机
     */
    public static final String FORM_PROCESS_EXCHANGE = "formProcessExchange";
    /**
     * 外键ID
     */
    public final static String FORM_FK_ID = "fkId";
    /**
     * 删除标识字段
     */
    public final static String FORM_DELETE_FLAG = "deleteFlag";
    /**
     * model包路径
     * TODO 项目迁移时注意修改
     */
    public final static String MODEL_PACKAGE_PATH = "com.fawkes.project.example.common.model";
    /**
     * 服务名常量，创建监听队列使用
     * TODO 项目迁移时注意修改
     */
    public final static String APPLICATION_NAME = "project-example";
}
