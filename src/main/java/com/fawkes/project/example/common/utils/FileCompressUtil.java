package com.fawkes.project.example.common.utils;


import net.lingala.zip4j.ZipFile;
import net.lingala.zip4j.model.FileHeader;
import net.lingala.zip4j.model.ZipParameters;

import java.io.File;
import java.nio.charset.Charset;
import java.nio.charset.StandardCharsets;
import java.util.List;

/**
 * <AUTHOR>
 */
public class FileCompressUtil {

    /**
     * 将目录压缩成zip
     *
     * @param dir       待压缩目录
     * @param targetDir 压缩文件存放目录（若不写则位于同一个目录）
     */
    public static void zipDir(String dir, String targetDir) throws Exception {
        /* 判断待压缩目录是否存在 */
        File dirFile = new File(dir);
        if (!dirFile.exists()) {
            throw new Exception("待压缩目录不存在");
        }

        /* 确保目标目录存在 */
        String outPath = targetDir == null ? dirFile.getParent() : targetDir;
        File outFile = new File(outPath);
        if (!outFile.exists()) {
            outFile.mkdirs();
        }

        /* 递归压缩目录 */
        ZipFile zipFile = new ZipFile(outPath + File.separator + dirFile.getName() + ".zip");
        ZipParameters zipParams = new ZipParameters();
        File[] fs = dirFile.listFiles();
        for (File f : fs) {
            if (f.isDirectory()) {
                zipFile.addFolder(f, zipParams);
            } else {
                zipFile.addFile(f, zipParams);
            }
        }
        zipFile.close();
    }

    /**
     * 将目录压缩成zip
     *
     * @param filePath  待压缩文件
     * @param targetDir 压缩文件存放目录（若不写则位于同一个目录）
     */
    public static void zipFile(String filePath, String targetDir) throws Exception {
        /* 判断待压缩文件是否存在 */
        File file = new File(filePath);
        if (!file.exists()) {
            throw new Exception("待压缩文件不存在");
        }

        /* 确保目标目录存在 */
        String outPath = targetDir == null ? file.getParent() : targetDir;
        File outFile = new File(outPath);
        if (!outFile.exists()) {
            outFile.mkdirs();
        }

        /* 递归压缩目录 */
        ZipFile zipFile = new ZipFile(outPath + File.separator + file.getName().substring(0, file.getName().lastIndexOf(".")) + ".zip");
        ZipParameters zipParams = new ZipParameters();
        zipFile.addFile(file, zipParams);
    }

    /**
     * 解压文件
     *
     * @param zipPath
     */
    public static void unzip(String zipPath) throws Exception {
        /* 判断待解压文件是否存在 */
        File dirFile = new File(zipPath);
        if (!dirFile.exists()) {
            throw new Exception("待解压文件不存在");
        }

        ZipFile zipFile = new ZipFile(zipPath);
        zipFile.setCharset(StandardCharsets.UTF_8);
        List<FileHeader> headers = zipFile.getFileHeaders();
        if (isRandomCode(headers)) {//判断文件名是否有乱码，有乱码，将编码格式设置成GBK
            zipFile.close();
            zipFile = new ZipFile(zipPath);
            zipFile.setCharset(Charset.forName("GBK"));
        }
        zipFile.extractAll(dirFile.getParent());
    }

    private static boolean isRandomCode(List<FileHeader> fileHeaders) {
        for (int i = 0; i < fileHeaders.size(); i++) {
            FileHeader fileHeader = fileHeaders.get(i);
            boolean canEnCode = Charset.forName("GBK").newEncoder().canEncode(fileHeader.getFileName());
            if (!canEnCode) {
                return true;
            }
        }
        return false;
    }

}
