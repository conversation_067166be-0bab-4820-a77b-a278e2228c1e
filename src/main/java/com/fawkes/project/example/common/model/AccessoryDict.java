package com.fawkes.project.example.common.model;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import com.fawkes.core.base.model.BaseEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;

import java.io.Serializable;

@Data
@ToString
@NoArgsConstructor
public class AccessoryDict extends BaseEntity implements Serializable {
    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "代码")
    private String dictCode;

    @ApiModelProperty(value = "名称")
    private String dictName;

    @ApiModelProperty(value = "备注")
    private String dictRemark;

    @ApiModelProperty(value = "父级id")
    @JsonSerialize(using = ToStringSerializer.class)
    private Long dictParent;
}