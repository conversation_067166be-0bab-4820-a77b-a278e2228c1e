package com.fawkes.project.example.common.model;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import com.fawkes.core.base.model.BaseEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;

/**
 * 产品订单配件损耗实体类
 *
 * <AUTHOR>
 */
@Data
@ToString
@NoArgsConstructor
public class ProductOrderLoss extends BaseEntity {

    @JsonSerialize(using = ToStringSerializer.class)
    private Long orderId;

    /**
     * 配件ID
     */
    @JsonSerialize(using = ToStringSerializer.class)
    private Long accessoryId;

    /**
     * 损耗量
     */
    private Integer lossQuantity;

    /**
     * 损耗责任人ID
     */
    @JsonSerialize(using = ToStringSerializer.class)
    private Long lossUserId;

    /**
     * 损耗责任人名称
     */
    private String lossUserName;

    @ApiModelProperty(value = "配件编号")
    private String accessoryNo;

    @ApiModelProperty(value = "配件名称")
    private String accessoryName;

    @ApiModelProperty(value = "配件类型")
    private String accessoryType;

    @ApiModelProperty(value = "配件规格")
    private String accessorySpec;

}
