<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.fawkes.project.example.common.mapper.ProductMapper">
    <resultMap id="Product" type="com.fawkes.project.example.common.model.Product"
               extends="com.fawkes.project.example.common.mapper.BaseMapper.BaseEntity">
        <result column="name" jdbcType="VARCHAR" property="name"/>
        <result column="model" jdbcType="VARCHAR" property="model"/>
        <result column="unit" jdbcType="VARCHAR" property="unit"/>
        <result column="category_code" jdbcType="VARCHAR" property="categoryCode"/>
        <result column="apply_status" jdbcType="BIT" property="applyStatus"/>
        <result column="industry" jdbcType="VARCHAR" property="industry"/>
        <result column="remark" jdbcType="VARCHAR" property="remark"/>
        <result column="cert_type" jdbcType="VARCHAR" property="certType"/>
    </resultMap>
    <resultMap id="ProductDTO" type="com.fawkes.project.example.domain.dto.ProductDTO" extends="Product"/>
    <sql id="Column_List_Product">
        id
        , create_by, create_date, update_by, update_date, delete_flag, name, model, unit,category_code, apply_status, industry, remark, cert_type
    </sql>


    <select id="isExistedName" parameterType="com.fawkes.project.example.common.model.Product"
            resultType="java.lang.Boolean">
        SELECT count( * ) FROM product
        <where>
            delete_flag = 0 AND `name` = #{product.name}
            <if test="product.id != null">AND id != #{product.id}</if>
        </where>
    </select>

    <select id="isExistedModel" parameterType="com.fawkes.project.example.common.model.Product"
            resultType="java.lang.Boolean">
        SELECT count( * ) FROM product
        <where>
            delete_flag = 0 AND model = #{product.model}
            <if test="product.id != null">AND id != #{product.id}</if>
        </where>
    </select>

    <select id="selectById" parameterType="java.lang.Long" resultMap="ProductDTO">
        SELECT
        <include refid="Column_List_Product"/>
        FROM product WHERE id = #{id,jdbcType=BIGINT}
    </select>

    <select id="selectByIds" parameterType="java.lang.Long" resultMap="ProductDTO">
        SELECT
        <include refid="Column_List_Product"/>
        FROM product WHERE id IN
        <foreach collection="ids" item="id" open="(" separator="," close=")">#{id,jdbcType=BIGINT}</foreach>
    </select>

    <select id="list" resultMap="ProductDTO">
        SELECT
        <include refid="Column_List_Product"/>
        FROM product
        WHERE delete_flag = 0
        <if test="name != null and name != ''">
            and `name` LIKE CONCAT('%', #{name}, '%')
        </if>
        ORDER BY create_date DESC
    </select>

    <select id="selectProductListByModelAndNameAndApplyStatus" resultMap="ProductDTO">
        select
        a.id            as product_id,
        a.model,
        a.name          as product_name,
        a.create_date,
        a.delete_flag   as product_delete_flag,
        -- 其他 product 表字段...

        b.id            as craft_id,
        b.apply_status,
        b.delete_flag   as craft_delete_flag
        -- 其他 product_craft 表字段...
        from product a
        left join product_craft b on a.id = b.product_id and b.delete_flag = 0
        <where>
            a.delete_flag = 0
            <if test="model != null and model != ''">
                and a.model LIKE CONCAT('%', #{model}, '%')
            </if>
            <if test="name != null and name != ''">
                and a.`name` LIKE CONCAT('%', #{name}, '%')
            </if>
            <if test="applyStatus != null">
                and b.apply_status = #{applyStatus}
            </if>
        </where>
        order by a.create_date DESC
    </select>

    <insert id="insert" parameterType="com.fawkes.project.example.common.model.Product">
        insert into product (id, create_by, create_date,
                             update_by, update_date, delete_flag,
                             `name`, model, unit,
                             category_code, apply_status, industry,
                             remark, cert_type)
        values (#{id,jdbcType=BIGINT}, #{createBy,jdbcType=VARCHAR}, #{createDate,jdbcType=TIMESTAMP},
                #{updateBy,jdbcType=VARCHAR}, #{updateDate,jdbcType=TIMESTAMP}, #{deleteFlag,jdbcType=INTEGER},
                #{name,jdbcType=VARCHAR}, #{model,jdbcType=VARCHAR}, #{unit,jdbcType=VARCHAR},
                #{categoryCode,jdbcType=VARCHAR}, #{applyStatus,jdbcType=BIT}, #{industry,jdbcType=VARCHAR},
                #{remark,jdbcType=VARCHAR}, #{certType,jdbcType=VARCHAR})
    </insert>


    <update id="deleteById">
        UPDATE product
        SET delete_flag = -1,
            update_by   = #{updateInfo.updateBy},
            update_date = #{updateInfo.updateDate}
        WHERE id = #{productId}
    </update>

    <update id="updateByPrimaryKey" parameterType="com.fawkes.project.example.common.model.Product">
        update product
        set update_by     = #{updateBy,jdbcType=VARCHAR},
            update_date   = #{updateDate,jdbcType=TIMESTAMP},
            `name`        = #{name,jdbcType=VARCHAR},
            model         = #{model,jdbcType=VARCHAR},
            unit          = #{unit,jdbcType=VARCHAR},
            category_code = #{categoryCode,jdbcType=VARCHAR},
            apply_status  = #{applyStatus,jdbcType=BIT},
            industry      = #{industry,jdbcType=VARCHAR},
            remark        = #{remark,jdbcType=VARCHAR},
            cert_type     = #{certType,jdbcType=VARCHAR}
        where id = #{id,jdbcType=BIGINT}
    </update>

    <update id="updateBatch" parameterType="com.fawkes.project.example.common.model.Product">
        <foreach collection="list" item="item" separator=";">
            update product
            set create_by = #{item.createBy,jdbcType=VARCHAR},
            create_date = #{item.createDate,jdbcType=TIMESTAMP},
            update_by = #{item.updateBy,jdbcType=VARCHAR},
            update_date = #{item.updateDate,jdbcType=TIMESTAMP},
            delete_flag = #{item.deleteFlag,jdbcType=INTEGER},
            `name` = #{item.name,jdbcType=VARCHAR},
            model = #{item.model,jdbcType=VARCHAR},
            unit = #{item.unit,jdbcType=VARCHAR},
            category_code = #{item.categoryCode,jdbcType=VARCHAR},
            apply_status = #{item.applyStatus,jdbcType=BIT},
            cert_type = #{item.certType,jdbcType=VARCHAR},
            industry = #{item.industry,jdbcType=VARCHAR},
            remark = #{item.remark,jdbcType=VARCHAR}
            where id = #{item.id,jdbcType=BIGINT}
        </foreach>
    </update>

    <update id="updateApplyStatusByProductId">
        UPDATE product
        SET apply_status = #{applyStatus,jdbcType=BIT},
            update_by    = #{updateInfo.updateBy,jdbcType=VARCHAR},
            update_date  = #{updateInfo.updateDate,jdbcType=TIMESTAMP}
        WHERE id = #{productId}
    </update>
</mapper>