<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.fawkes.project.example.common.mapper.UseOrderMapper">
    <resultMap id="UseOrder" type="com.fawkes.project.example.common.model.UseOrder"
               extends="com.fawkes.project.example.common.mapper.BaseMapper.BaseEntity">
        <result column="order_no" jdbcType="VARCHAR" property="orderNo"/>
        <result column="order_name" jdbcType="BIGINT" property="orderName"/>
        <result column="order_desc" jdbcType="VARCHAR" property="orderDesc"/>
        <result column="duty_user_id" jdbcType="BIGINT" property="dutyUserId"/>
        <result column="duty_user_name" jdbcType="VARCHAR" property="dutyUserName"/>
        <result column="plan_finish_date" jdbcType="TIMESTAMP" property="planFinishDate"/>
    </resultMap>

    <resultMap id="UseOrderVO" type="com.fawkes.project.example.domain.vo.UseOrderVO" extends="UseOrder"></resultMap>

    <resultMap id="ProductObjVO" type="com.fawkes.project.example.domain.vo.ProductObjVO"
               extends="com.fawkes.project.example.common.mapper.BaseMapper.BaseEntity">
        <result column="object_no" jdbcType="VARCHAR" property="objectNo"/>
        <result column="craft_id" jdbcType="BIGINT" property="craftId"/>
        <result column="craft_code" jdbcType="VARCHAR" property="craftCode"/>
        <result column="product_model" jdbcType="VARCHAR" property="productModel"/>
        <result column="product_name" jdbcType="VARCHAR" property="productName"/>
        <result column="industry" jdbcType="VARCHAR" property="industry"/>
        <result column="use_order_id" jdbcType="VARCHAR" property="useOrderId"/>
        <result column="use_date" jdbcType="TIMESTAMP" property="useDate"/>
    </resultMap>
    <resultMap id="UseOrderGoods" type="com.fawkes.project.example.common.model.UseOrderGoods"
               extends="com.fawkes.project.example.common.mapper.BaseMapper.BaseEntity">
        <result column="order_id" jdbcType="BIGINT" property="orderId"/>
        <result column="obj_id" jdbcType="BIGINT" property="objId"/>
        <result column="use_return_time" jdbcType="TIMESTAMP" property="useReturnTime"/>
        <result column="return_status" jdbcType="VARCHAR" property="returnStatus"/>
        <result column="return_reason" jdbcType="VARCHAR" property="returnReason"/>
        <result column="return_pic" jdbcType="LONGVARCHAR" property="returnPic"/>
    </resultMap>
    <resultMap id="UseOrderGoodsVO" type="com.fawkes.project.example.domain.vo.UseOrderGoodsVO" extends="UseOrderGoods">
        <result column="object_no" jdbcType="VARCHAR" property="objectNo"/>
        <result column="product_name" jdbcType="VARCHAR" property="productName"/>
        <result column="product_model" jdbcType="VARCHAR" property="productModel"/>
        <result column="craft_code" jdbcType="VARCHAR" property="craftCode"/>
        <result column="industry" jdbcType="VARCHAR" property="industry"/>
    </resultMap>

    <select id="getMaxUseOrderNo" resultType="java.lang.String">
        SELECT MAX(order_no)
        FROM use_order
        WHERE YEAR ( create_date ) = YEAR ( #{searchDate} )
    </select>

    <select id="countByCreateYear" parameterType="java.lang.String" resultType="java.lang.Long">
        SELECT count(*)
        FROM `use_order`
        WHERE YEAR ( create_date ) = #{searchYear}
    </select>

    <select id="listAvailableObjByCraftId" resultMap="ProductObjVO">
        SELECT a.id,
               a.object_no,
               a.craft_id,
               b.craft_code,
               c.industry,
               c.model  AS product_model,
               c.name AS product_name
        FROM product_object a
                 LEFT JOIN product_craft b ON b.id = a.craft_id
                 LEFT JOIN product c ON c.id = b.product_id
        WHERE a.delete_flag = 0
          AND a.status = 1
          AND a.sale_order_id IS NULL
          AND a.use_order_id IS NULL
          AND a.craft_id = #{craftId}
    </select>

    <select id="isExistedOrderNo" parameterType="com.fawkes.project.example.common.model.UseOrder" resultType="boolean">
        SELECT count( * ) FROM `use_order`
        <where>order_no = #{order.orderNo}
            <if test="order.id != null">AND id != #{order.id}</if>
        </where>
    </select>

    <select id="selectUseOrderGoodsByOrderIds" resultMap="UseOrderGoodsVO">
        SELECT
        a.*,
        b.object_no,
        c.craft_code,
        d.name AS product_name,
        d.model AS product_model,
        d.industry
        FROM
        use_order_goods a
        LEFT JOIN product_object b ON b.id = a.obj_id
        LEFT JOIN product_craft c ON c.id = b.craft_id
        LEFT JOIN product d ON d.id = c.product_id
        WHERE
        a.delete_flag = 0
        AND a.order_id in
        <foreach collection="orderIds" item="orderId" open="(" separator="," close=")">#{orderId,jdbcType=BIGINT}
        </foreach>
    </select>

    <insert id="insert" parameterType="com.fawkes.project.example.common.model.UseOrder">
        INSERT INTO use_order (id, create_by, create_date, update_by, update_date, delete_flag, order_no, order_name,
                               order_desc, duty_user_id, duty_user_name, plan_finish_date)
        VALUES (#{order.id,jdbcType=BIGINT}, #{order.createBy,jdbcType=VARCHAR}, #{order.createDate,jdbcType=TIMESTAMP},
                #{order.updateBy,jdbcType=VARCHAR}, #{order.updateDate,jdbcType=TIMESTAMP},
                #{order.deleteFlag,jdbcType=INTEGER},
                #{order.orderNo,jdbcType=VARCHAR}, #{order.orderName,jdbcType=VARCHAR},
                #{order.orderDesc,jdbcType=VARCHAR},
                #{order.dutyUserId,jdbcType=BIGINT}, #{order.dutyUserName,jdbcType=VARCHAR},
                #{order.planFinishDate,jdbcType=TIMESTAMP})
    </insert>

    <update id="update" parameterType="com.fawkes.project.example.common.model.UseOrder">
        UPDATE use_order
        SET update_by        = #{order.updateBy,jdbcType=VARCHAR},
            update_date      = #{order.updateDate,jdbcType=TIMESTAMP},
            order_desc       = #{order.orderDesc,jdbcType=VARCHAR},
            duty_user_id     = #{order.dutyUserId,jdbcType=BIGINT},
            duty_user_name   = #{order.dutyUserName,jdbcType=VARCHAR},
            plan_finish_date = #{order.planFinishDate,jdbcType=TIMESTAMP}
        WHERE id = #{order.id,jdbcType=BIGINT}
    </update>


    <update id="useBatch">
        <foreach collection="objIds" item="id" separator=";">
            update product_object
            set use_order_id = #{useOrderId}
            where id = #{id,jdbcType=BIGINT}
        </foreach>
    </update>

    <insert id="logBatch">
        <foreach collection="goodsList" item="item" separator=";">
            insert into use_order_goods (id, create_by, create_date, update_by, update_date, delete_flag,
            obj_id, order_id, use_return_time,
            return_status, return_reason, return_pic)
            VALUES (#{item.id,jdbcType=BIGINT}, #{item.createBy,jdbcType=VARCHAR},
            #{item.createDate,jdbcType=TIMESTAMP},#{item.updateBy,jdbcType=VARCHAR},
            #{item.updateDate,jdbcType=TIMESTAMP}, #{item.deleteFlag,jdbcType=INTEGER},
            #{item.objId,jdbcType=BIGINT}, #{item.orderId,jdbcType=BIGINT}, #{item.useReturnTime,jdbcType=TIMESTAMP},
            #{item.returnStatus,jdbcType=VARCHAR},#{item.returnReason,jdbcType=VARCHAR},#{item.returnPic,jdbcType=LONGVARCHAR})
        </foreach>
    </insert>

    <update id="updateLogBatch" parameterType="com.fawkes.project.example.common.model.UseOrderGoods">
        <foreach collection="goodsList" item="item" separator=";">
            update use_order_goods
            set update_by = #{item.updateBy,jdbcType=VARCHAR},
            update_date = #{item.updateDate,jdbcType=TIMESTAMP},
            delete_flag = #{item.deleteFlag,jdbcType=INTEGER},
            use_return_time = #{item.useReturnTime,jdbcType=TIMESTAMP},
            return_status = #{item.returnStatus,jdbcType=VARCHAR},
            return_reason = #{item.returnReason,jdbcType=VARCHAR},
            return_pic = #{item.returnPic,jdbcType=LONGVARCHAR}
            where id = #{item.id,jdbcType=BIGINT}
        </foreach>
    </update>

    <update id="returnUse">
        <foreach collection="objList" item="item" separator=";">
            update product_object
            set use_order_id = null,
            `status` = #{item.status},
            unqualified_reason = #{item.returnReason},
            unqualified_pic = #{item.returnPic}
            where id = #{item.id,jdbcType=BIGINT}
        </foreach>
    </update>

    <select id="selectById" resultMap="UseOrder">
        SELECT *
        FROM use_order
        where id = #{orderId,jdbcType=BIGINT}
    </select>

    <select id="selectByIds" resultMap="UseOrder">
        SELECT * FROM use_order
        where id IN
        <foreach collection="orderIds" item="orderId" open="(" separator="," close=")">#{orderId,jdbcType=BIGINT}
        </foreach>
    </select>

    <select id="listProductObjByUseOrderId" resultMap="ProductObjVO">
        SELECT a.*,
               b.craft_code,
               c.name AS product_name,
               c.model  AS product_model,
               c.industry
        FROM product_object a
                 LEFT JOIN product_craft b ON b.id = a.craft_id
                 LEFT JOIN product c ON c.id = b.product_id
        WHERE use_order_id = #{orderId,jdbcType=BIGINT}
    </select>

    <select id="listProductObjByUseOrderIds" resultMap="ProductObjVO">
        SELECT a.*,
        b.craft_code,
        c.name AS product_name,
        c.model AS product_model,
        c.industry,
        d.plan_finish_date as use_date
        FROM product_object a
        LEFT JOIN product_craft b ON b.id = a.craft_id
        LEFT JOIN product c ON c.id = b.product_id
        LEFT JOIN use_order d ON d.id = a.use_order_id
        WHERE use_order_id
        IN
        <foreach close=")" collection="orderIds" item="id" open="(" separator=", ">
            #{id}
        </foreach>
    </select>

    <select id="listUseOrderGoodsByUseOrderIds" resultMap="UseOrderGoodsVO">
        SELECT
        a.*,
        b.object_no,
        c.craft_code,
        d.name AS product_name,
        d.model AS product_model,
        d.industry
        FROM
        use_order_goods a
        LEFT JOIN product_object b ON b.id = a.obj_id
        LEFT JOIN product_craft c ON c.id = b.craft_id
        LEFT JOIN product d ON d.id = c.product_id
        WHERE
        a.delete_flag = 0
        AND a.order_id IN
        <foreach close=")" collection="orderIds" item="id" open="(" separator=", ">#{id}</foreach>
    </select>

    <select id="listByYear" resultMap="UseOrder">
        SELECT * FROM use_order
        <where>delete_flag = 0
            <if test="searchYear != null">
                and YEAR ( create_date ) = #{searchYear}
            </if>
        </where>
    </select>

    <select id="list" resultMap="UseOrderVO">
        SELECT * FROM use_order
        <where>delete_flag = 0
            <if test="param.orderNo != null and param.orderNo != '' ">
                AND order_no LIKE CONCAT( '%', #{param.orderNo}, '%' )
            </if>
            <if test="param.orderName != null and param.orderName != '' ">
                AND order_name LIKE CONCAT( '%', #{param.orderName}, '%' )
            </if>
            <if test="param.dutyUserId != null">
                AND duty_user_id = #{param.dutyUserId}
            </if>
        </where>
    </select>

    <update id="deleteByIds">
        <foreach collection="ids" item="id" separator=";">
            update use_order
            set delete_flag = -1
            where id = #{id,jdbcType=BIGINT}
        </foreach>
    </update>
</mapper>