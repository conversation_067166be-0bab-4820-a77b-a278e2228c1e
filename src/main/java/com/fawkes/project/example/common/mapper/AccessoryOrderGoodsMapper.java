package com.fawkes.project.example.common.mapper;

import com.fawkes.core.base.model.BaseEntity;
import com.fawkes.project.example.common.model.AccessoryOrderGoods;
import com.fawkes.project.example.domain.dto.AccessoryPriceDTO;
import com.fawkes.project.example.domain.vo.AccessoryOrderGoodsVO;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface AccessoryOrderGoodsMapper {
    /**
     * 查询配件明细
     *
     * @param id
     * @return
     */
    List<AccessoryOrderGoodsVO> selectAccessoryOrderGoodsByOrderId(Long id);

    /**
     * 根据订单id查询配件明细
     *
     * @param id
     * @return
     */
    List<AccessoryOrderGoods> selectByOrderId(Long id);

    /**
     * 根据配件id查询配件明细
     *
     * @param accessoryId
     * @return
     */
    List<AccessoryOrderGoodsVO> selectByAccessoryId(@Param("accessoryId") Long accessoryId);

    /**
     * 查询配件价格
     *
     * @param accessoryIds 为空时查询全部
     * @return
     */
    List<AccessoryPriceDTO> selectAccessoryPrice(@Param("accessoryIds") List<Long> accessoryIds);

    /**
     * 批量插入配件明细
     *
     * @param goodsList
     * @return
     */
    boolean insertBatch(@Param("list") List<? extends AccessoryOrderGoods> goodsList);

    /**
     * 批量更新
     *
     * @param goodsList
     * @return
     */
    boolean updateBatch(@Param("list") List<AccessoryOrderGoods> goodsList);

    /**
     * 根据订单id删除配件明细
     *
     * @param orderId
     * @param updateInfo
     * @return
     */
    int deleteByOrderId(@Param("orderId") Long orderId, @Param("updateInfo") BaseEntity updateInfo);
}