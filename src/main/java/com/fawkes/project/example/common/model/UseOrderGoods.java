package com.fawkes.project.example.common.model;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import com.fawkes.core.base.model.BaseEntity;
import io.swagger.annotations.ApiModelProperty;
import io.swagger.annotations.Extension;
import io.swagger.annotations.ExtensionProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.ToString;

import java.io.Serializable;
import java.sql.Timestamp;

/**
 * 物品领用明细
 *
 * <AUTHOR>
 */
@Data
@ToString
@NoArgsConstructor
@EqualsAndHashCode(callSuper = true)
public class UseOrderGoods extends BaseEntity implements Serializable {

    @ApiModelProperty(value = "物品领用订单id")
    @JsonSerialize(using = ToStringSerializer.class)
    private Long orderId;

    @ApiModelProperty(value = "物品领用id")
    @JsonSerialize(using = ToStringSerializer.class)
    private Long objId;

    @ApiModelProperty(
            value = "归还时间",
            extensions = @Extension(name = "rap2", properties = {
                    @ExtensionProperty(name = "type", value = "String"),
                    @ExtensionProperty(name = "value", value = "@datetime"),
                    @ExtensionProperty(name = "rule", value = "")
            }))
    private Timestamp useReturnTime;

    @ApiModelProperty(value = "归还状态：合格、不合格")
    private String returnStatus;

    @ApiModelProperty(value = "不合格理由")
    private String returnReason;

    @ApiModelProperty(value = "不合格图片")
    private String returnPic;

    public UseOrderGoods(Long orderId, Long objId) {
        this.orderId = orderId;
        this.objId = objId;
    }
}
