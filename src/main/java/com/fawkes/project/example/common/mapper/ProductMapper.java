package com.fawkes.project.example.common.mapper;

import com.fawkes.core.base.model.BaseEntity;
import com.fawkes.project.example.common.model.Product;
import com.fawkes.project.example.domain.dto.ProductDTO;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface ProductMapper {

    /**
     * 校验产品名称是否已存在
     *
     * @param product
     * @return
     */
    Boolean isExistedName(@Param("product") Product product);

    /**
     * 校验产品类型是否已存在
     *
     * @param product
     * @return
     */
    Boolean isExistedModel(@Param("product") Product product);

    /**
     * 按id查询
     *
     * @param id
     * @return
     */
    ProductDTO selectById(Long id);

    /**
     * 查询产品
     *
     * @param ids
     * @return
     */
    List<ProductDTO> selectByIds(@Param("ids") List<Long> ids);

    /**
     * 分页查询
     *
     * @param name
     * @return
     */
    List<ProductDTO> list(@Param("name") String name);

    /**
     * 分页查询
     *
     * @param model
     * @param name
     * @param applyStatus
     * @return
     */
    List<ProductDTO> selectProductListByModelAndNameAndApplyStatus(@Param("model") String model, @Param("name") String name, @Param("applyStatus") Boolean applyStatus);

    /**
     * 新增
     *
     * @param record
     * @return
     */
    int insert(Product record);

    /**
     * 逻辑删除
     *
     * @param productId
     * @param updateInfo
     * @return
     */
    int deleteById(@Param("productId") Long productId, @Param("updateInfo") BaseEntity updateInfo);

    /**
     * 更新
     *
     * @param record
     * @return
     */
    int updateByPrimaryKey(Product record);

    /**
     * 批量更新
     *
     * @param productList
     * @return
     */
    boolean updateBatch(@Param("list") List<? extends Product> productList);

    /**
     * 更新应用状态
     *
     * @param productId
     * @param applyStatus
     * @param updateInfo
     * @return
     */
    int updateApplyStatusByProductId(@Param("productId") Long productId, @Param("applyStatus") Boolean applyStatus, @Param("updateInfo") BaseEntity updateInfo);
}