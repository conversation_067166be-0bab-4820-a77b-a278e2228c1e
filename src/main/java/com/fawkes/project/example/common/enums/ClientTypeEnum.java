package com.fawkes.project.example.common.enums;

import com.fawkes.project.example.common.exception.BizDmException;
import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Arrays;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Getter
@AllArgsConstructor
public enum ClientTypeEnum {
    /**
     * 院内客户
     */
    YNKH("YNKH", "院内客户"),
    /**
     * 集团内客户
     */
    JTKH("JTKH", "集团内客户"),
    /**
     * 市场客户
     */
    SCKH("SCKH", "市场客户"),
    /**
     * 市场代理商
     */
    CPDLS("CPDLS", "市场代理商");
    /**
     * 缩写
     */
    private final String flag;
    /**
     * 描述
     */
    private final String desc;

    public static boolean containFlag(String flag) {
        return Arrays.stream(ClientContactsEnum.values()).anyMatch(e -> e.getFlag().equals(flag));
    }

    public static List<String> flags() {
        return Arrays.stream(ClientTypeEnum.values()).map(ClientTypeEnum::getFlag).collect(Collectors.toList());
    }

    public static String getDescByFlag(String flag) {
        Optional<ClientTypeEnum> optional = Arrays.stream(ClientTypeEnum.values()).filter(e -> e.getFlag().equals(flag)).findFirst();
        if (!optional.isPresent()) {
            throw new BizDmException("客户类型不存在：" + flag);
        }
        return optional.get().getDesc();
    }
}
