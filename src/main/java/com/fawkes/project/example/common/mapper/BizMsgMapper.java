package com.fawkes.project.example.common.mapper;

import com.fawkes.project.example.common.model.BizMsg;
import com.fawkes.project.example.common.model.ProductObject;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <AUTHOR>
 */
public interface BizMsgMapper {

    /**
     * 查询消息通知
     *
     * @param toUserId
     * @return
     */
    List<BizMsg> selectByToUserId(@Param("toUserId") Long toUserId);

    /**
     * 由产品ID获取产品信息
     */
    List<ProductObject> getObjsByIds(@Param("ids") List<Long> ids);

    /**
     * 批量插入
     *
     * @param bizMsgList
     * @return
     */
    int insertBatch(@Param("bizMsgList") List<BizMsg> bizMsgList);

    /**
     * 更新已读状态
     *
     * @param toUserId
     * @param status
     * @return
     */
    int updateStatusByToUserId(@Param("toUserId") Long toUserId, @Param("status") Integer status);
}
