package com.fawkes.project.example.common.model;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import com.fawkes.core.base.model.BaseEntity;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;

import java.util.Date;

/**
 * 产品实体类
 *
 * <AUTHOR>
 */
@Data
@ToString
@NoArgsConstructor
public class ProductObject extends BaseEntity {

    /**
     * 订单ID
     */
    @JsonSerialize(using = ToStringSerializer.class)
    private Long orderId;

    /**
     * 工艺ID
     */
    @JsonSerialize(using = ToStringSerializer.class)
    private Long craftId;

    /**
     * 产品编号
     */
    private String objectNo;
    @JsonSerialize(using = ToStringSerializer.class)
    private Long objectId;

    /**
     * 检验完成时间
     */
    private Date checkFinishTime;
    private Long productId;
    private String productModel;
    private String productName;
    private String certType;
    private String produceUserName;
    private String craftCode;
    private Date actualFinishTime;
    private Date outDate;

    /**
     * 出库状态
     */
    private Boolean outStatus;

    /**
     * 产品状态：0 - 未检验；1 - 合格品 ；2 - 次品；3 - 废品
     */
    private Integer status;

    /**
     * 不合格原因
     */
    private String unqualifiedReason;

    /**
     * 不合格图片
     */
    private String unqualifiedPic;

    private Long saleOrderId;


    /**
     * 不合格图片
     */

    public ProductObject(Long orderId, Long craftId, String objectNo) {
        this.orderId = orderId;
        this.craftId = craftId;
        this.objectNo = objectNo;
        this.status = 0;
    }

}
