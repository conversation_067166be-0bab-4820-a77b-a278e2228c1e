<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.fawkes.project.example.common.mapper.SaleGoodsAlterLogMapper">
    <resultMap id="SaleGoodsAlterLog" type="com.fawkes.project.example.common.model.SaleGoodsAlterLog"
               extends="com.fawkes.project.example.common.mapper.BaseMapper.BaseEntity">
        <result column="alter_id" jdbcType="BIGINT" property="alterId"/>
        <result column="alter_item" jdbcType="VARCHAR" property="alterItem"/>
        <result column="alter_source" jdbcType="LONGVARCHAR" property="alterSource"/>
        <result column="alter_target" jdbcType="LONGVARCHAR" property="alterTarget"/>
    </resultMap>

    <select id="selectByAlterId" resultMap="SaleGoodsAlterLog">
        select *
        from sale_goods_alter_log
        where delete_flag = 0
          AND alter_id = #{alterId}
    </select>

    <insert id="insertBatch" parameterType="com.fawkes.project.example.common.model.SaleGoodsAlterLog">
        insert into sale_goods_alter_log (id, create_by, create_date,
        update_by, update_date, delete_flag,
        alter_id, alter_item, alter_source,
        alter_target)
        VALUES
        <foreach collection="list" item="item" separator=",">
            (#{item.id,jdbcType=BIGINT}, #{item.createBy,jdbcType=VARCHAR}, #{item.createDate,jdbcType=TIMESTAMP},
            #{item.updateBy,jdbcType=VARCHAR}, #{item.updateDate,jdbcType=TIMESTAMP},
            #{item.deleteFlag,jdbcType=INTEGER},
            #{item.alterId,jdbcType=BIGINT}, #{item.alterItem,jdbcType=VARCHAR},
            #{item.alterSource,jdbcType=LONGVARCHAR},
            #{item.alterTarget,jdbcType=LONGVARCHAR})
        </foreach>
    </insert>
</mapper>