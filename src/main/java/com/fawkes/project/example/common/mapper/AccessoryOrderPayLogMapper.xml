<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.fawkes.project.example.common.mapper.AccessoryOrderPayLogMapper">
    <resultMap id="AccessoryOrderPayLog" type="com.fawkes.project.example.common.model.AccessoryOrderPayLog"
               extends="com.fawkes.project.example.common.mapper.BaseMapper.BaseEntity">
        <result column="order_id" jdbcType="BIGINT" property="orderId"/>
        <result column="amount" jdbcType="DECIMAL" property="amount"/>
    </resultMap>

    <insert id="insert" parameterType="com.fawkes.project.example.common.model.AccessoryOrderPayLog">
        insert into accessory_order_pay_log (id, create_by, create_date,
                                             update_by, update_date, delete_flag,
                                             order_id, amount)
        values (#{id,jdbcType=BIGINT}, #{createBy,jdbcType=VARCHAR}, #{createDate,jdbcType=TIMESTAMP},
                #{updateBy,jdbcType=VARCHAR}, #{updateDate,jdbcType=TIMESTAMP}, #{deleteFlag,jdbcType=INTEGER},
                #{orderId,jdbcType=BIGINT}, #{amount,jdbcType=DECIMAL})
    </insert>
</mapper>