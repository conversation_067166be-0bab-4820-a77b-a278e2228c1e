package com.fawkes.project.example.common.model;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import com.fawkes.core.base.model.BaseEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;

import java.io.Serializable;

@Data
@ToString
@NoArgsConstructor
public class ProductCraftCompose extends BaseEntity implements Serializable {
    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "工艺id")
    @JsonSerialize(using = ToStringSerializer.class)
    private Long craftId;

    @ApiModelProperty(value = "配件id")
    @JsonSerialize(using = ToStringSerializer.class)
    private Long accessoryId;

    @ApiModelProperty(value = "采购数量")
    private Integer quantity;
}