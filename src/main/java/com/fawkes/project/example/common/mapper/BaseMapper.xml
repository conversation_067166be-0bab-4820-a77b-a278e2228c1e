<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.fawkes.project.example.common.mapper.BaseMapper">
    <resultMap id="BaseEntity" type="com.fawkes.core.base.model.BaseEntity">
        <id column="id" jdbcType="BIGINT" property="id"/>
        <result column="create_by" jdbcType="VARCHAR" property="createBy"/>
        <result column="create_date" jdbcType="TIMESTAMP" property="createDate"/>
        <result column="update_by" jdbcType="VARCHAR" property="updateBy"/>
        <result column="update_date" jdbcType="TIMESTAMP" property="updateDate"/>
        <result column="delete_flag" jdbcType="INTEGER" property="deleteFlag"/>
    </resultMap>
</mapper>