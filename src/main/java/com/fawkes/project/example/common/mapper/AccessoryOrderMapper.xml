<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.fawkes.project.example.common.mapper.AccessoryOrderMapper">
    <resultMap id="AccessoryOrder" type="com.fawkes.project.example.common.model.AccessoryOrder"
               extends="com.fawkes.project.example.common.mapper.BaseMapper.BaseEntity">
        <result column="order_no" jdbcType="VARCHAR" property="orderNo"/>
        <result column="order_name" jdbcType="VARCHAR" property="orderName"/>
        <result column="order_date" jdbcType="DATE" property="orderDate"/>
        <result column="order_delivery_date" jdbcType="DATE" property="orderDeliveryDate"/>
        <result column="order_person_liable_id" jdbcType="BIGINT" property="orderPersonLiableId"/>
        <result column="order_person_liable_name" jdbcType="VARCHAR" property="orderPersonLiableName"/>
        <result column="order_enclosure" jdbcType="VARCHAR" property="orderEnclosure"/>
        <result column="order_total_paid" jdbcType="DECIMAL" property="orderTotalPaid"/>
        <result column="order_pic" jdbcType="LONGVARCHAR" property="orderPic"/>
    </resultMap>
    <resultMap id="AccessoryOrderVO" type="com.fawkes.project.example.domain.vo.AccessoryOrderVO"
               extends="AccessoryOrder">
        <result column="total_purchase_quantity" jdbcType="INTEGER" property="totalPurchaseQuantity"/>
        <result column="total_arrival_quantity" jdbcType="INTEGER" property="totalArrivalQuantity"/>
        <result column="total_purchase_price" jdbcType="DECIMAL" property="totalPurchasePrice"/>
    </resultMap>
    <resultMap id="AccessoryOutVO" type="com.fawkes.project.example.domain.vo.AccessoryOutVO">
        <id column="id" jdbcType="BIGINT" property="id"/>
        <result column="update_date" jdbcType="TIMESTAMP" property="updateDate"/>
        <result column="order_no" jdbcType="VARCHAR" property="orderNo"/>
        <result column="order_time" jdbcType="DATE" property="orderTime"/>
        <result column="total_product_quantity" jdbcType="INTEGER" property="totalProductQuantity"/>
        <result column="accessory_quantity" jdbcType="INTEGER" property="accessoryQuantity"/>
        <result column="accessory_quantity_with_loss" jdbcType="INTEGER" property="accessoryQuantityWithLoss"/>
        <result column="produce_user_name" jdbcType="VARCHAR" property="produceUserName"/>
    </resultMap>
    <resultMap id="AccessoryOrderStatPaidVO" type="com.fawkes.project.example.domain.vo.AccessoryOrderStatPaidVO">
        <result column="not_fully_paid_count" jdbcType="INTEGER" property="notFullyPaidCount"/>
        <result column="fully_paid_count" jdbcType="INTEGER" property="fullyPaidCount"/>
    </resultMap>
    <resultMap id="BizStatPurchaseVO" type="com.fawkes.project.example.domain.vo.BizStatPurchaseVO">
        <result column="order_quantity" jdbcType="INTEGER" property="orderQuantity"/>
        <result column="order_amount" jdbcType="DECIMAL" property="orderAmount"/>
    </resultMap>
    <resultMap id="BizStatPurchaseMonthVO" type="com.fawkes.project.example.domain.vo.BizStatPurchaseMonthVO">
        <result column="stat_month" jdbcType="INTEGER" property="month"/>
        <result column="purchase_amount" jdbcType="DECIMAL" property="purchaseAmount"/>
        <result column="payment_amount" jdbcType="DECIMAL" property="paymentAmount"/>
    </resultMap>

    <select id="getMaxOrderNoAtTheDate" resultType="java.lang.String">
        SELECT MAX(order_no)
        FROM accessory_order
        WHERE order_date = #{searchDate}
    </select>

    <select id="isExistedOrderNo" resultType="java.lang.Boolean">
        SELECT COUNT( * ) FROM accessory_order
        WHERE delete_flag = 0 AND order_no = #{order.orderNo}
        <if test="order.id != null">AND id != #{order.id}</if>
    </select>


    <select id="selectById" parameterType="java.lang.Long" resultMap="AccessoryOrderVO">
        SELECT *
        FROM (
                 SELECT a.id,
                        a.create_by,
                        a.create_date,
                        a.update_by,
                        a.update_date,
                        a.delete_flag,
                        a.order_no,
                        a.order_name,
                        a.order_date,
                        a.order_delivery_date,
                        a.order_person_liable_id,
                        a.order_person_liable_name,
                        a.order_pic,
                        a.order_enclosure,
                        IFNULL(sum(DISTINCT c.order_total_paid), 0) AS order_total_paid,
                        IFNULL(sum(b.purchase_quantity), 0)         AS total_purchase_quantity,
                        IFNULL(sum(b.purchase_price), 0)            AS total_purchase_price,
                        IFNULL(sum(b.arrival_quantity), 0)          AS total_arrival_quantity
                 FROM accessory_order a
                          LEFT JOIN accessory_order_goods b ON a.id = b.order_id AND b.delete_flag != - 1
                        LEFT JOIN ( SELECT order_id AS id, IFNULL( sum( amount ), 0 ) AS order_total_paid FROM accessory_order_pay_log GROUP BY order_id ) c
                 ON c.id = a.id
                 GROUP BY
                     a.id,
                     a.create_by,
                     a.create_date,
                     a.update_by,
                     a.update_date,
                     a.delete_flag,
                     a.order_no,
                     a.order_name,
                     a.order_date,
                     a.order_delivery_date,
                     a.order_person_liable_id,
                     a.order_person_liable_name,
                     a.order_pic,
                     a.order_enclosure
             ) temp
        WHERE temp.id = #{id,jdbcType=BIGINT}
    </select>

    <select id="selectAccessoryOrderList"
            parameterType="com.fawkes.project.example.domain.param.AccessoryOrderQueryParam"
            resultMap="AccessoryOrderVO">
        SELECT *
        from (
        SELECT a.id, a.create_by, a.create_date, a.update_by, a.update_date, a.delete_flag, a.order_no, a.order_name,
        a.order_date, a.order_delivery_date, a.order_type, a.order_person_liable_id, a.order_person_liable_name,
        a.order_enclosure, a.order_pic,
        IFNULL( sum( DISTINCT d.order_total_paid ), 0 ) AS order_total_paid,
        IFNULL( sum(b.purchase_quantity), 0 ) as total_purchase_quantity,
        IFNULL( sum(b.purchase_price), 0 ) as total_purchase_price,
        IFNULL( sum(b.arrival_quantity), 0 ) as total_arrival_quantity
        FROM accessory_order a
        LEFT JOIN accessory_order_goods b on a.id = b.order_id and b.delete_flag = 0
        LEFT JOIN accessory c on c.id = b.accessory_id
        LEFT JOIN ( SELECT order_id AS id, IFNULL( sum( amount ), 0 ) AS order_total_paid FROM accessory_order_pay_log
        GROUP BY order_id ) d ON d.id = a.id
        <where>
            a.delete_flag = 0
            <if test="accessorySupplier != null and accessorySupplier != ''">AND c.accessory_supplier LIKE CONCAT('%',
                #{accessorySupplier}, '%')
            </if>
        </where>
        GROUP BY a.id,
        a.create_by, a.create_date, a.update_by, a.update_date, a.delete_flag, a.order_no, a.order_name,
        a.order_date, a.order_delivery_date, a.order_type, a.order_person_liable_id, a.order_person_liable_name,
        a.order_enclosure, a.order_pic
        ) temp
        <where>
            temp.delete_flag = 0
            <!-- 配件明细 - 配件入库订单 条件搜索 -->
            <if test="paidStatus == 1"> <![CDATA[ and temp.order_total_paid < temp.total_purchase_price ]]> </if>
            <if test="paidStatus == 2"> <![CDATA[ and temp.order_total_paid >= temp.total_purchase_price ]]> </if>
            <if test="startDate != null">AND temp.order_date >= #{startDate}</if>
            <if test="endDate != null">AND <![CDATA[ temp.order_date <= #{endDate} ]]> </if>
            <!-- 经营统计 - 付款统计 - 订单统计 条件搜索 -->
            <if test="orderPersonLiableId != null">AND temp.order_person_liable_id = #{orderPersonLiableId}</if>
            <if test="orderNo != null and orderNo != ''">AND temp.order_no LIKE CONCAT('%', #{orderNo}, '%')</if>
            <if test="orderName != null and orderName != ''">AND temp.order_name LIKE CONCAT('%', #{orderName}, '%')
            </if>
            <if test="totalPurchasePriceMin != null">AND temp.total_purchase_price >= #{totalPurchasePriceMin}</if>
            <if test="totalPurchasePriceMax != null">AND
                <![CDATA[ temp.total_purchase_price <= #{totalPurchasePriceMax} ]]> </if>
            <if test="orderDateStart != null">AND temp.order_date >= #{orderDateStart}</if>
            <if test="orderDateEnd != null">AND <![CDATA[ temp.order_date <= #{orderDateEnd} ]]> </if>
            <if test="orderDeliveryDateStart != null">AND temp.order_delivery_date >= #{orderDeliveryDateStart}</if>
            <if test="orderDeliveryDateEnd != null">AND
                <![CDATA[ temp.order_delivery_date <= #{orderDeliveryDateEnd} ]]> </if>
        </where>
        ORDER BY update_date DESC
    </select>

    <select id="selectOutList" resultMap="AccessoryOutVO">
        SELECT a.id,
        a.order_no,
        a.update_date,
        a.order_time,
        a.produce_user_name AS produce_user_name,
        COUNT(b.id) AS total_product_quantity,
        IFNULL(SUM(c.quantity), 0) AS accessory_quantity
        FROM product_order a
        LEFT JOIN product_object b ON a.id = b.order_id
        AND b.delete_flag = 0
        LEFT JOIN product_craft_compose c ON c.craft_id = b.craft_id
        AND c.delete_flag = 0
        LEFT JOIN product_order_loss d ON a.id = d.order_id
        AND d.delete_flag = 0
        <where>
            a.delete_flag = 0
            <if test="orderNo != null and orderNo != ''">AND a.order_no LIKE CONCAT('%', #{orderNo}, '%')</if>
            <if test="startDate != null">AND a.order_time >= #{startDate}</if>
            <if test="endDate != null">AND <![CDATA[ a.order_time <= #{endDate} ]]> </if>
        </where>
        GROUP BY a.id,a.order_no,a.update_date,a.order_time,a.produce_user_name,b.id,c.quantity,b.order_id,b.delete_flag,c.craft_id,b.craft_id,c.delete_flag,d.order_id,d.delete_flag
        ORDER BY a.order_no DESC
    </select>

    <select id="selectStatPaid" resultMap="AccessoryOrderStatPaidVO">
        SELECT COUNT(CASE WHEN <![CDATA[ order_total_paid < total_purchase_price ]]>  THEN 1 ELSE NULL END)  AS not_fully_paid_count,
               COUNT(CASE WHEN order_total_paid >= total_purchase_price THEN 1 ELSE NULL END) AS fully_paid_count
        FROM (
                 SELECT a.id,
                        a.create_date,
                        a.order_no,
                        IFNULL(sum(DISTINCT c.order_total_paid), 0) AS order_total_paid,
                        IFNULL(sum(b.purchase_price), 0)            AS total_purchase_price
                 FROM accessory_order a
                          LEFT JOIN accessory_order_goods b ON a.id = b.order_id
                     AND b.delete_flag = 0
                          LEFT JOIN (SELECT order_id AS id, IFNULL(sum(amount), 0) AS order_total_paid
                                     FROM accessory_order_pay_log
                                     GROUP BY order_id) c ON c.id = a.id
                 WHERE a.delete_flag = 0
                 GROUP BY a.id,a.create_date,a.order_no,c.order_total_paid,b.purchase_price,b.order_id,b.delete_flag,c.id,a.delete_flag
             ) temp
    </select>

    <insert id="insert" parameterType="com.fawkes.project.example.common.model.AccessoryOrder">
        insert into accessory_order (id, create_by, create_date,
                                     update_by, update_date, delete_flag,
                                     order_no, order_name, order_date,
                                     order_delivery_date, order_person_liable_id,
                                     order_person_liable_name, order_enclosure, order_pic)
        values (#{id,jdbcType=BIGINT}, #{createBy,jdbcType=VARCHAR}, #{createDate,jdbcType=TIMESTAMP},
                #{updateBy,jdbcType=VARCHAR}, #{updateDate,jdbcType=TIMESTAMP}, #{deleteFlag,jdbcType=INTEGER},
                #{orderNo,jdbcType=VARCHAR}, #{orderName,jdbcType=VARCHAR}, #{orderDate,jdbcType=DATE},
                #{orderDeliveryDate,jdbcType=DATE}, #{orderPersonLiableId,jdbcType=BIGINT},
                #{orderPersonLiableName,jdbcType=VARCHAR}, #{orderEnclosure,jdbcType=VARCHAR},
                #{orderPic,jdbcType=LONGVARCHAR})
    </insert>

    <update id="deleteById">
        update accessory_order
        set delete_flag = - 1,
            update_by   = #{updateInfo.updateBy},
            update_date = #{updateInfo.updateDate}
        where id = #{orderId,jdbcType=BIGINT}
    </update>

    <update id="updateByPrimaryKey" parameterType="com.fawkes.project.example.common.model.AccessoryOrder">
        update accessory_order
        set update_by                = #{updateBy,jdbcType=VARCHAR},
            update_date              = #{updateDate,jdbcType=TIMESTAMP},
            order_name               = #{orderName,jdbcType=VARCHAR},
            order_date               = #{orderDate,jdbcType=DATE},
            order_delivery_date      = #{orderDeliveryDate,jdbcType=DATE},
            order_person_liable_id   = #{orderPersonLiableId,jdbcType=BIGINT},
            order_person_liable_name = #{orderPersonLiableName,jdbcType=VARCHAR},
            order_enclosure          = #{orderEnclosure,jdbcType=VARCHAR},
            order_pic                = #{orderPic,jdbcType=LONGVARCHAR}
        where id = #{id,jdbcType=BIGINT}
    </update>

    <select id="statPurchase" resultMap="BizStatPurchaseVO">
        SELECT
        count( DISTINCT a.id ) AS order_quantity,
        IFNULL( SUM( b.purchase_price ), 0 ) AS order_amount
        FROM
        accessory_order a
        LEFT JOIN accessory_order_goods b ON b.order_id = a.id
        AND b.delete_flag = 0
        <where>
            a.delete_flag = 0
            <if test="searchYear != null">AND YEAR ( a.order_date ) = #{searchYear}</if>
        </where>
    </select>

    <select id="statPurchaseAccessorySupplier" resultType="int">
        SELECT
        COUNT( DISTINCT a.accessory_supplier )
        FROM
        accessory a
        LEFT JOIN accessory_order_goods b ON b.accessory_id = a.id
        AND b.delete_flag = 0
        LEFT JOIN accessory_order c ON c.id = b.order_id
        AND c.delete_flag = 0
        <where>
            a.delete_flag = 0
            AND a.accessory_supplier IS NOT NULL
            AND b.id IS NOT NULL
            AND c.id IS NOT NULL
            <if test="searchYear != null">AND YEAR ( c.order_date ) = #{searchYear}</if>
        </where>
    </select>

    <select id="statPurchaseMonth" resultMap="BizStatPurchaseMonthVO">
        SELECT
            a.stat_month,
            IFNULL(SUM(c.purchase_price), 0) AS   purchase_amount,
            IFNULL(SUM(b.order_total_paid), 0) AS payment_amount
        FROM
            (SELECT
                 1 AS stat_month
             UNION ALL
             SELECT
                 2
             UNION ALL
             SELECT
                 3
             UNION ALL
             SELECT
                 4
             UNION ALL
             SELECT
                 5
             UNION ALL
             SELECT
                 6
             UNION ALL
             SELECT
                 7
             UNION ALL
             SELECT
                 8
             UNION ALL
             SELECT
                 9
             UNION ALL
             SELECT
                 10
             UNION ALL
             SELECT
                 11
             UNION ALL
             SELECT
                 12
             ORDER BY
                 stat_month) a
                LEFT JOIN accessory_order b ON MONTH(b.order_date) = a.stat_month AND b.delete_flag = 0
            LEFT JOIN accessory_order_goods c ON c.order_id = b.id AND c.delete_flag = 0
        GROUP BY
            a.stat_month
    </select>
</mapper>