package com.fawkes.project.example.common.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * <AUTHOR>
 */
@Getter
@AllArgsConstructor
public enum ClientContactsEnum {
    /**
     * 联系人类型： 联系人
     */
    TYPE_CONTACTS(1, "联系人"),
    /**
     * 联系人类型： 对接负责人
     */
    TYPE_IN_CHARGE(2, "对接负责人");
    /**
     * 状态值
     */
    private final Integer flag;
    /**
     * 描述
     */
    private final String desc;
}
