<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.fawkes.project.example.common.mapper.SaleOrderGoodsMapper">
    <resultMap id="SaleOrderGoods" type="com.fawkes.project.example.common.model.SaleOrderGoods"
               extends="com.fawkes.project.example.common.mapper.BaseMapper.BaseEntity">
        <result column="craft_id" jdbcType="BIGINT" property="craftId"/>
        <result column="sale_quantity" jdbcType="INTEGER" property="saleQuantity"/>
        <result column="sale_price" jdbcType="DECIMAL" property="salePrice"/>
        <result column="param_id" jdbcType="BIGINT" property="paramId"/>
        <result column="order_id" jdbcType="BIGINT" property="orderId"/>
    </resultMap>
    <resultMap id="SaleOrderGoodsVO" type="com.fawkes.project.example.domain.vo.SaleOrderGoodsVO"
               extends="SaleOrderGoods">
        <result column="product_id" jdbcType="BIGINT" property="productId"/>
        <result column="product_name" jdbcType="VARCHAR" property="productName"/>
        <result column="product_model" jdbcType="VARCHAR" property="productModel"/>
        <result column="param_name" jdbcType="VARCHAR" property="paramName"/>
        <result column="craft_code" jdbcType="VARCHAR" property="craftCode"/>
        <result column="industry" jdbcType="VARCHAR" property="industry"/>
    </resultMap>
    <resultMap id="SaleOrderGoodsWithStatVO" type="com.fawkes.project.example.domain.vo.SaleOrderGoodsWithStatVO">
        <id column="id" jdbcType="BIGINT" property="id"/>
        <result column="order_id" jdbcType="BIGINT" property="orderId"/>
        <result column="product_name" jdbcType="VARCHAR" property="productName"/>
        <result column="product_model" jdbcType="VARCHAR" property="productModel"/>
        <result column="craft_id" jdbcType="BIGINT" property="craftId"/>
        <result column="craft_code" jdbcType="VARCHAR" property="craftCode"/>
        <result column="sale_quantity" jdbcType="VARCHAR" property="saleQuantity"/>
        <result column="sale_price" jdbcType="VARCHAR" property="salePrice"/>
    </resultMap>

    <select id="listByOrderId" parameterType="java.lang.Long" resultMap="SaleOrderGoodsVO">
        SELECT a.id,
               a.craft_id,
               a.sale_quantity,
               a.sale_price,
               a.order_id,
               a.param_id,
               b.param_name,
               c.craft_code,
               d.name AS product_name,
               d.model  AS product_model,
               d.industry,
               d.id     AS product_id
        FROM sale_order_goods a
                 LEFT JOIN product_param b ON b.id = a.param_id
                 LEFT JOIN product_craft c ON c.id = a.craft_id
                 LEFT JOIN product d ON d.id = c.product_id
        WHERE a.delete_flag = 0
          AND a.order_id = #{orderId}
        ORDER BY a.update_date DESC
    </select>

    <select id="listByOrderIds" parameterType="java.lang.Long" resultMap="SaleOrderGoods">
        SELECT * FROM sale_order_goods
        WHERE
        delete_flag = 0
        AND order_id IN
        <foreach collection="orderIds" open="(" separator="," item="orderId" close=")">#{orderId}</foreach>
        ORDER BY
        update_date DESC
    </select>

    <insert id="insertBatch" parameterType="com.fawkes.project.example.common.model.SaleOrderGoods">
        insert into sale_order_goods (id, create_by, create_date,update_by, update_date, delete_flag,
        craft_id, sale_quantity, sale_price, param_id, order_id)
        VALUES
        <foreach collection="list" item="item" separator=",">
            (#{item.id,jdbcType=BIGINT}, #{item.createBy,jdbcType=VARCHAR},
            #{item.createDate,jdbcType=TIMESTAMP},#{item.updateBy,jdbcType=VARCHAR},
            #{item.updateDate,jdbcType=TIMESTAMP}, #{item.deleteFlag,jdbcType=INTEGER},
            #{item.craftId,jdbcType=BIGINT}, #{item.saleQuantity,jdbcType=INTEGER}, #{item.salePrice,jdbcType=DECIMAL},
            #{item.paramId,jdbcType=BIGINT}, #{item.orderId,jdbcType=BIGINT})
        </foreach>
    </insert>

    <update id="updateBatch" parameterType="com.fawkes.project.example.common.model.AccessoryOrderGoods">
        <foreach collection="list" item="item" separator=";">
            update sale_order_goods
            set update_by = #{item.updateBy,jdbcType=VARCHAR},
            update_date = #{item.updateDate,jdbcType=TIMESTAMP},
            craft_id = #{item.craftId,jdbcType=BIGINT},
            sale_quantity = #{item.saleQuantity,jdbcType=INTEGER},
            sale_price = #{item.salePrice,jdbcType=DECIMAL},
            param_id = #{item.paramId,jdbcType=BIGINT},
            order_id = #{item.orderId,jdbcType=BIGINT}
            where id = #{item.id,jdbcType=BIGINT}
        </foreach>
    </update>

    <update id="deleteBatch" parameterType="com.fawkes.project.example.common.model.AccessoryOrderGoods">
        <foreach collection="list" item="item" separator=";">
            update sale_order_goods
            set update_by = #{item.updateBy,jdbcType=VARCHAR},
            update_date = #{item.updateDate,jdbcType=TIMESTAMP},
            delete_flag = -1
            where id = #{item.id,jdbcType=BIGINT}
        </foreach>
    </update>

    <select id="listWithStatByOrderIds" resultMap="SaleOrderGoodsWithStatVO">
        SELECT
        a.id,
        a.craft_id,
        IFNULL(a.sale_quantity, 0) AS sale_quantity,
        IFNULL(a.sale_price, 0) AS sale_price,
        a.order_id,
        a.param_id,
        b.param_name,
        c.craft_code,
        d.name AS product_name,
        d.model AS product_model,
        d.industry,
        d.id AS product_id
        FROM
        sale_order_goods a
        LEFT JOIN product_param b ON b.id = a.param_id
        LEFT JOIN product_craft c ON c.id = a.craft_id
        LEFT JOIN product d ON d.id = c.product_id
        WHERE
        a.delete_flag = 0
        AND a.order_id IN
        <foreach collection="orderIds" item="id" separator=", " open="(" close=")">
            #{id}
        </foreach>
        ORDER BY
        a.update_date DESC
    </select>
</mapper>