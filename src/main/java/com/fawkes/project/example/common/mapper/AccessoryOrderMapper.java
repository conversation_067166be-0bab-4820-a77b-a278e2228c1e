package com.fawkes.project.example.common.mapper;

import com.fawkes.core.base.model.BaseEntity;
import com.fawkes.project.example.common.model.AccessoryOrder;
import com.fawkes.project.example.domain.param.AccessoryOrderQueryParam;
import com.fawkes.project.example.domain.param.AccessoryOutOrderQueryParam;
import com.fawkes.project.example.domain.vo.*;
import org.apache.ibatis.annotations.Param;

import java.time.LocalDate;
import java.time.Year;
import java.util.List;

public interface AccessoryOrderMapper {

    /**
     * 根据日期获取最大订单号
     *
     * @param searchDate
     * @return
     */
    String getMaxOrderNoAtTheDate(@Param("searchDate") LocalDate searchDate);

    /**
     * 校验订单编号是否已存在
     *
     * @param order
     * @return
     */
    Boolean isExistedOrderNo(@Param("order") AccessoryOrder order);

    /**
     * 查询配件入库订单详情
     *
     * @param id
     * @return
     */
    AccessoryOrderVO selectById(Long id);

    /**
     * 条件分页查询
     *
     * @param param
     * @return
     */
    List<AccessoryOrderVO> selectAccessoryOrderList(AccessoryOrderQueryParam param);

    /**
     * 查询配件出库订单
     *
     * @param param
     * @return
     */
    List<AccessoryOutVO> selectOutList(AccessoryOutOrderQueryParam param);

    /**
     * 统计支付情况
     *
     * @param year
     * @return
     */
    AccessoryOrderStatPaidVO selectStatPaid();

    /**
     * 新增
     *
     * @param record
     * @return
     */
    int insert(AccessoryOrder record);

    /**
     * 删除配件入库订单
     *
     * @param orderId
     * @param updateInfo
     * @return
     */
    int deleteById(@Param("orderId") Long orderId, @Param("updateInfo") BaseEntity updateInfo);

    /**
     * 更新配件入库订单
     *
     * @param record
     * @return
     */
    int updateByPrimaryKey(AccessoryOrder record);

    /**
     * 统计采购入库
     *
     * @param searchYear
     * @return
     */
    BizStatPurchaseVO statPurchase(@Param("searchYear") Year searchYear);

    /**
     * 统计签订配件厂商的数量
     *
     * @param searchYear
     * @return
     */
    int statPurchaseAccessorySupplier(@Param("searchYear") Year searchYear);

    /**
     * 逐月统计采购、支付
     *
     * @param searchYear
     * @return
     */
    List<BizStatPurchaseMonthVO> statPurchaseMonth(@Param("searchYear") Year searchYear);
}