package com.fawkes.project.example.common.model;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import com.fawkes.core.base.model.BaseEntity;
import com.fawkes.project.example.common.exception.BizDmException;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;

import java.io.Serializable;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.Objects;

@Data
@ToString
@NoArgsConstructor
public class SaleOrderGoods extends BaseEntity implements Serializable {
    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "工艺id")
    @JsonSerialize(using = ToStringSerializer.class)
    private Long craftId;

    @ApiModelProperty(value = "销售数量")
    private Integer saleQuantity;

    @ApiModelProperty(value = "售价（含税）")
    private BigDecimal salePrice;

    @ApiModelProperty(value = "产品参数id")
    @JsonSerialize(using = ToStringSerializer.class)
    private Long paramId;

    @ApiModelProperty(value = "销售订单id")
    @JsonSerialize(using = ToStringSerializer.class)
    private Long orderId;

    /**
     * 获取销售单价
     *
     * @return
     */
    public BigDecimal getUnitSalePrice() {
        if (Objects.isNull(salePrice)) {
            throw new BizDmException("未填写售价");
        }
        if (Objects.isNull(saleQuantity) || saleQuantity <= 0) {
            throw new BizDmException("未填写销售数量");
        }
        return salePrice.divide(BigDecimal.valueOf(saleQuantity), 2, RoundingMode.HALF_UP);
    }
}