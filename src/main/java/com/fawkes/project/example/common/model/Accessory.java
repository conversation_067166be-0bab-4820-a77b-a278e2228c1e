package com.fawkes.project.example.common.model;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import com.fawkes.core.base.model.BaseEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;

import java.io.Serializable;
import java.math.BigDecimal;

@Data
@ToString
@NoArgsConstructor
public class Accessory extends BaseEntity implements Serializable {
    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "配件名称")
    private String accessoryName;

    @ApiModelProperty(value = "配件类型：耗材、原材料")
    private String accessoryType;

    @ApiModelProperty(value = "配件种类id")
    @JsonSerialize(using = ToStringSerializer.class)
    private Long categoryId;

    @ApiModelProperty(value = "配件编号id")
    @JsonSerialize(using = ToStringSerializer.class)
    private Long refId;

    @ApiModelProperty(value = "单位：件、台、套")
    private String accessoryUnit;

    @ApiModelProperty(value = "规格型号")
    private String accessorySpec;

    @ApiModelProperty(value = "存放位置")
    private String accessoryStorage;

    @ApiModelProperty(value = "用途说明")
    private String accessoryRemark;

    @ApiModelProperty(value = "厂商名称")
    private String accessorySupplier;

    @ApiModelProperty(value = "当前单价")
    private BigDecimal curPrice;

    @ApiModelProperty(value = "配件图片")
    private String accessoryPic;

    /**
     * 配件编号
     */
    private String accessoryNo;

    /**
     * 配件数量
     */
    private Integer quantity;

    /**
     * 工艺ID
     */
    private Long craftId;

    /**
     * 配件ID
     */
    private Long accessoryId;

}