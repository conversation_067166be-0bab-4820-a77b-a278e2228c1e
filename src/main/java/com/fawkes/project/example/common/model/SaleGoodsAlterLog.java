package com.fawkes.project.example.common.model;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import com.fawkes.core.base.model.BaseEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;

import java.io.Serializable;

@Data
@ToString
@NoArgsConstructor
public class SaleGoodsAlterLog extends BaseEntity implements Serializable {
    private static final long serialVersionUID = 1L;

    @JsonSerialize(using = ToStringSerializer.class)
    @ApiModelProperty(value = "变更id")
    private Long alterId;

    @ApiModelProperty(value = "变更项")
    private String alterItem;

    @ApiModelProperty(value = "变更前")
    private String alterSource;

    @ApiModelProperty(value = "变更后")
    private String alterTarget;

    public SaleGoodsAlterLog(String alterItem, String alterSource, String alterTarget) {
        this.alterItem = alterItem;
        this.alterSource = alterSource;
        this.alterTarget = alterTarget;
    }

    public String getLogDesc() {
        return "变更项：" + alterItem + "，变更前：" + alterSource + "，变更后：" + alterTarget;
    }
}