package com.fawkes.project.example.common.model;


import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import com.fawkes.core.base.model.BaseEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDate;

@Data
@ToString
@NoArgsConstructor
public class SaleOrder extends BaseEntity implements Serializable {
    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "订单编号：CPXS+年份+四位序列号")
    private String orderNo;

    @ApiModelProperty(value = "订单名称")
    private String orderName;

    @ApiModelProperty(value = "签订日期")
    private LocalDate signDate;

    @ApiModelProperty(value = "计划交付日期")
    private LocalDate plannedDeliveryDate;

    @ApiModelProperty(value = "客户id")
    @JsonSerialize(using = ToStringSerializer.class)
    private Long clientId;

    @ApiModelProperty(value = "责任人id")
    @JsonSerialize(using = ToStringSerializer.class)
    private Long liableUserId;

    @ApiModelProperty(value = "责任人名称")
    private String liableUserName;

    @ApiModelProperty(value = "行业类型")
    private String industry;

    @ApiModelProperty(value = "项目类型：院内项目、市场项目")
    private String projectType;

    @ApiModelProperty(value = "附件")
    private String orderEnclosure;

    @ApiModelProperty(value = "累计收款金额")
    private BigDecimal paymentAmount;

    @ApiModelProperty(value = "合同状态: 进行中、已完成")
    private String status;
}