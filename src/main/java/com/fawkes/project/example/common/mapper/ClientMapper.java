package com.fawkes.project.example.common.mapper;

import com.fawkes.core.base.model.BaseEntity;
import com.fawkes.project.example.common.model.Client;
import com.fawkes.project.example.domain.param.ClientParam;
import com.fawkes.project.example.domain.vo.ClientVO;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface ClientMapper {

    /**
     * 判断客户是否存在
     *
     * @param client
     * @return
     */
    Boolean isExistedClientName(@Param("client") Client client);

    /**
     * 根据客户类型获取最大客户编号
     *
     * @param clientType - 客户类型
     * @return
     */
    String getMaxClientNoByClientType(@Param("clientType") String clientType);

    /**
     * 根据主键查询
     *
     * @param id
     * @return
     */
    Client selectByPrimaryKey(Long id);

    /**
     * 分页条件查询
     *
     * @param param
     * @return
     */
    List<ClientVO> selectClientList(@Param("param") ClientParam param);

    /**
     * 插入
     *
     * @param record
     * @return
     */
    int insert(Client record);

    /**
     * 更新
     *
     * @param record
     * @return
     */
    int updateByPrimaryKey(Client record);

    /**
     * 批量删除，更新操作人
     *
     * @param ids
     * @param updateInfo
     * @return
     */
    boolean deleteBatch(@Param("ids") List<Long> ids, @Param("updateInfo") BaseEntity updateInfo);
}