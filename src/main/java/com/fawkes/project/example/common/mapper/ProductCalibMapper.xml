<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.fawkes.project.example.common.mapper.ProductCalibMapper">
    <resultMap id="ProductCalib" type="com.fawkes.project.example.common.model.ProductCalib"
               extends="com.fawkes.project.example.common.mapper.BaseMapper.BaseEntity">
        <result column="product_id" jdbcType="BIGINT" property="productId"/>
        <result column="calib_code" jdbcType="VARCHAR" property="calibCode"/>
        <result column="calib_remark" jdbcType="VARCHAR" property="calibRemark"/>
    </resultMap>
    <sql id="Column_List_ProductCalib">
        id
        , create_by, create_date, update_by, update_date, delete_flag, product_id, calib_code,calib_remark
    </sql>

    <select id="selectByProductIds" resultMap="ProductCalib">
        SELECT
        <include refid="Column_List_ProductCalib"/>
        FROM product_calib
        WHERE delete_flag = 0 AND product_id IN
        <foreach collection="productIds" item="productId" separator=", " open="(" close=")">#{productId}</foreach>
        ORDER BY update_date DESC
    </select>


    <insert id="insertBatch" parameterType="com.fawkes.project.example.common.model.ProductCalib">
        insert into product_calib (id, create_by, create_date,
        update_by, update_date, delete_flag,
        product_id, calib_code, calib_remark
        )
        VALUES
        <foreach collection="list" item="item" separator=",">
            (#{item.id,jdbcType=BIGINT}, #{item.createBy,jdbcType=VARCHAR}, #{item.createDate,jdbcType=TIMESTAMP},
            #{item.updateBy,jdbcType=VARCHAR}, #{item.updateDate,jdbcType=TIMESTAMP},
            #{item.deleteFlag,jdbcType=INTEGER},
            #{item.productId,jdbcType=BIGINT}, #{item.calibCode,jdbcType=VARCHAR}, #{item.calibRemark,jdbcType=VARCHAR}
            )
        </foreach>
    </insert>

    <update id="updateBatch" parameterType="com.fawkes.project.example.common.model.ProductCalib">
        <foreach collection="list" item="item" separator=";">
            update product_calib
            set create_by = #{item.createBy,jdbcType=VARCHAR},
            create_date = #{item.createDate,jdbcType=TIMESTAMP},
            update_by = #{item.updateBy,jdbcType=VARCHAR},
            update_date = #{item.updateDate,jdbcType=TIMESTAMP},
            delete_flag = #{item.deleteFlag,jdbcType=INTEGER},
            product_id = #{item.productId,jdbcType=BIGINT},
            calib_code = #{item.calibCode,jdbcType=VARCHAR},
            calib_remark = #{item.calibRemark,jdbcType=VARCHAR}
            where id = #{item.id,jdbcType=BIGINT}
        </foreach>
    </update>
</mapper>