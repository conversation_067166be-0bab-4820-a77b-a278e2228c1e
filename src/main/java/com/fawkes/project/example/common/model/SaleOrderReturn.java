package com.fawkes.project.example.common.model;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import com.fawkes.core.base.model.BaseEntity;
import com.fawkes.project.example.domain.param.SaleReturnObj;
import com.fawkes.project.example.domain.param.SaleReturnParam;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;

import java.io.Serializable;
import java.sql.Timestamp;

@Data
@ToString
@NoArgsConstructor
public class SaleOrderReturn extends BaseEntity implements Serializable {
    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "销售订单id")
    @JsonSerialize(using = ToStringSerializer.class)
    private Long orderId;

    @ApiModelProperty(value = "产品对象id")
    @JsonSerialize(using = ToStringSerializer.class)
    private Long objId;

    @ApiModelProperty(value = "退换货时间")
    private Timestamp returnDate;

    @ApiModelProperty(value = "退换货类型")
    private String returnType;

    @ApiModelProperty(value = "退换货产品状态")
    private String returnStatus;

    @ApiModelProperty(value = "退换货产品状态")
    private String returnReason;

    @ApiModelProperty(value = "退换货产品状态")
    private String returnPic;

    @ApiModelProperty(value = "新产品id")
    @JsonSerialize(using = ToStringSerializer.class)
    private Long returnNewObjId;

    public SaleOrderReturn(SaleReturnObj obj, SaleReturnParam param) {
        this.orderId = param.getOrderId();
        this.objId = obj.getObjectId();
        this.returnType = param.getReturnType();
        this.returnStatus = obj.getReturnStatus();
        this.returnReason = obj.getReturnReason();
        this.returnPic = obj.getReturnPic();
        this.returnNewObjId = obj.getNewObjectId();
    }
}
