package com.fawkes.project.example.common.constants;

/**
 * <AUTHOR>
 */
public class CommonConstants {

    /************ 分页默认值 **********/
    public static final Integer PAGE_NUM = 1;
    public static final Integer PAGE_SIZE = 10;

    /************ 订单类型 **********/
    public static final String ORDER_TYPE_CGRK = "采购入库";
    public static final String ORDER_TYPE_CPSC = "产品生产";

    /************ 合格证类型 **********/
    public static final String CERE_TYPE_CGQ = "传感器";
    public static final String CERE_TYPE_SJCJY = "数据采集仪";

    /************ 合同状态 **********/
    public static final String CONTRACT_PROGRESS = "进行中";
    public static final String CONTRACT_FINISHED = "已完成";

    /************ 退换货产品状态 **********/
    public static final String RETURN_STATUS_QUALIFIED = "合格";
    public static final String RETURN_STATUS_UNQUALIFIED = "不合格";

    /************ 退换货类型 **********/
    public static final String RETURN_TYPE_REJECT = "退货";
    public static final String RETURN_TYPE_EXCHANGE = "换货";

    /************ 退换货产品状态 **********/
    public static final int OBJECT_STATUS_QUALIFIED = 1;
    public static final int OBJECT_STATUS_DEFECTIVE = 2;
    public static final int OBJECT_STATUS_WASTE = 3;

    /************ 月数 **********/
    public static final int MONTH_COUNT = 12;

    /************ 产品出入库形式 **********/
    public static final String PRODUCT_ORDER_TYPE_IN_PRODUCE = "生产入库";
    public static final String PRODUCT_ORDER_TYPE_OUT_SALE = "销售出库";
    public static final String PRODUCT_ORDER_TYPE_OUT_USE = "设备领用";

    /************ 消息状态 **********/
    public static final Integer BIZ_MSG_STATUS_UNREAD = 0;
    public static final Integer BIZ_MSG_STATUS_READ = 1;

    /************ 配件类型 **********/
    public static final String ACCESSORY_TYPE_RAW_MATERIAL = "原材料";
    public static final String ACCESSORY_TYPE_CONSUME_MATERIAL = "耗材";

    /************ 产品订单检验状态 **********/
    public static final Integer PRODUCT_ORDER_CHECK_STATUS_UNSTART = 0;
    public static final Integer PRODUCT_ORDER_CHECK_STATUS_CHECKING = 1;
    public static final Integer PRODUCT_ORDER_CHECK_STATUS_FINISHED = 2;

}
