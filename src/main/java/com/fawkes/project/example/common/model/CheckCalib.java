package com.fawkes.project.example.common.model;

import com.fawkes.core.base.model.BaseEntity;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;

/**
 * 产品订单实体类
 *
 * <AUTHOR>
 */
@Data
@ToString
@NoArgsConstructor
public class CheckCalib extends BaseEntity {

    /**
     * 产品ID
     */
    private Long objectId;

    /**
     * 产品ID
     */
    private Long productId;

    /**
     * 率定参数ID
     */
    private Long calibId;

    private String productNo;
    private String productModel;
    private String productName;

    /**
     * 率定参数名
     */
    private String calibCode;

    /**
     * 率定参数值
     */
    private Double calibValue;

    private String errorMsg;

}
