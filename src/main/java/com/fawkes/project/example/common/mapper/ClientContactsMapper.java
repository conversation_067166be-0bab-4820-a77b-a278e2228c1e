package com.fawkes.project.example.common.mapper;

import com.fawkes.project.example.common.model.ClientContacts;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface ClientContactsMapper {
    /**
     * 根据客户ID查询联系人
     *
     * @param clientIds
     * @return
     */
    List<ClientContacts> selectByClientIds(@Param("clientIds") List<Long> clientIds);

    /**
     * 批量插入
     *
     * @param list
     * @return
     */
    boolean insertBatch(List<ClientContacts> list);

    /**
     * 批量更新
     *
     * @param list
     * @return
     */
    boolean updateBatch(List<ClientContacts> list);
}