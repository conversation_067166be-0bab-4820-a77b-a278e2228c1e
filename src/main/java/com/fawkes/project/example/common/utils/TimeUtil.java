package com.fawkes.project.example.common.utils;

import org.joda.time.DateTime;
import org.springframework.util.ObjectUtils;

import java.text.DateFormat;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * 时间工具类
 *
 * <AUTHOR>
 */
public class TimeUtil {

    /**
     * 将时间加上毫秒
     */
    public static String addMillis(String time, int millis) throws ParseException {
        SimpleDateFormat sfd = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss.SSS");
        DateTime date = new DateTime(sfd.parse(time));
        DateTime dateTime = date.plusMillis(millis);
        return dateTime.toString("yyyy-MM-dd HH:mm:ss.SSS");
    }

    /**
     * 将时间加上分钟
     */
    public static Date addHours(Date time, int hours) {
        DateTime date = new DateTime(time);
        return date.plusHours(hours).toDate();
    }

    /**
     * 格式化到天
     */
    public static String round2Hour(Date date) {
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:00:00");
        return sdf.format(date);
    }

    /**
     * 格式化到秒
     */
    public static String round2Second(Date date) {
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        return sdf.format(date);
    }

    /**
     * 格式化到天
     */
    public static String round2Minute(Date date) {
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:00");
        return sdf.format(date);
    }

    /**
     * 将时间加上秒
     */
    public static String addSeconds(String time, int seconds) throws ParseException {
        SimpleDateFormat sfd = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        DateTime date = new DateTime(sfd.parse(time));
        DateTime dateTime = date.plusSeconds(seconds);
        return dateTime.toString("yyyy-MM-dd HH:mm:ss");
    }

    /**
     * 将时间加上分钟
     */
    public static String addMinutes(String time, int minutes) {
        SimpleDateFormat sfd = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        DateTime date = null;
        try {
            date = new DateTime(sfd.parse(time));
        } catch (ParseException e) {
            e.printStackTrace();
        }
        DateTime dateTime = date.plusMinutes(minutes);
        return dateTime.toString("yyyy-MM-dd HH:mm:ss");
    }

    /**
     * 将时间加上分钟
     */
    public static Date addMinutes(Date time, int minutes) {
        DateTime date = new DateTime(time);
        return date.plusMinutes(minutes).toDate();
    }

    /**
     * 只获取到整点时刻
     */
    public static Date only2Hour() {
        SimpleDateFormat format1 = new SimpleDateFormat("yyyy-MM-dd HH");
        SimpleDateFormat format2 = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        try {
            return format2.parse(format1.format(new Date()) + ":00:00");
        } catch (ParseException e) {
            e.printStackTrace();
        }
        return null;
    }

    /**
     * 将时间加上秒
     */
    public static Date addSeconds(Date time, int seconds) {
        DateTime date = new DateTime(time);
        DateTime dateTime = date.plusSeconds(seconds);
        return dateTime.toDate();
    }

    /**
     * 将时间加上秒，得到的秒数一定是偶数
     */
    public static Date addSecondsForEven(Date time, int seconds) {
        DateTime date = new DateTime(time);
        if (date.getSecondOfDay() % 2 == 1) {
            seconds -= 1;
        }
        DateTime dateTime = date.plusSeconds(seconds);
        return dateTime.toDate();
    }

    /**
     * 将时间加上秒
     */
    public static Date addMillis(Date time, long millis) throws ParseException {
        DateTime date = new DateTime(time);
        DateTime dateTime = date.plusMillis(Integer.parseInt(String.valueOf(millis)));
        return dateTime.toDate();
    }

    /**
     * 去掉分秒
     */
    public static Date trimMinSec(Date date) {
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(date);

        calendar.set(Calendar.MILLISECOND, 0);
        calendar.set(Calendar.SECOND, 0);
        calendar.set(Calendar.MINUTE, 0);
        return calendar.getTime();
    }

    /**
     * 去掉分秒
     */
    public static String format(Date date, String format) {
        SimpleDateFormat sdf = new SimpleDateFormat(format);
        return sdf.format(date);
    }

    /**
     * 格式化到天
     */
    public static String format2Day(Date date) {
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
        return sdf.format(date);
    }

    /**
     * 格式化到年
     */
    public static String format2Year(Date date) {
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy");
        return sdf.format(date);
    }

    /**
     * 格式化到月
     */
    public static String format2Month(Date date) {
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy年MM月");
        return sdf.format(date);
    }

    /**
     * 补全开始时间
     */
    public static String completeStart(String day) {
        if (ObjectUtils.isEmpty(day)) {
            return day;
        }
        return day + " 00:00:00";
    }

    /**
     * 补全结束时间
     */
    public static String completeEnd(String day) {
        if (ObjectUtils.isEmpty(day)) {
            return day;
        }
        return day + " 23:59:59";
    }

    /**
     * 毫秒时间转化为天时分秒毫秒
     */
    public static String millis2Human(long millis) {
        if (millis <= 0) {
            return "0s";
        }

        int s = 1000;
        int m = s * 60;
        int h = m * 60;
        int d = h * 24;

        long day = millis / d;
        long hour = (millis - day * d) / h;
        long minute = (millis - day * d - hour * h) / m;
        long second = (millis - day * d - hour * h - minute * m) / s;
        long milliSecond = millis - day * d - hour * h - minute * m - second * s;

        StringBuilder sb = new StringBuilder();
        if (day > 0) {
            sb.append(day + "d.");
        }
        if (hour > 0) {
            sb.append(hour + "h.");
        }
        if (minute > 0) {
            sb.append(minute + "m.");
        }
        if (second > 0) {
            sb.append(second + "s.");
        }
        if (milliSecond > 0) {
            sb.append(milliSecond + "ms");
        }
        if (sb.toString().endsWith(".")) {
            sb.deleteCharAt(sb.length() - 1);
        }
        return sb.toString();
    }

    /**
     * 毫秒时间转化为天时分秒毫秒
     */
    public static String second2Human(int seconds) {
        if (seconds <= 0) {
            return "0s";
        }

        int m = 60;
        int h = m * 60;
        int d = h * 24;

        long day = seconds / d;
        long hour = (seconds - day * d) / h;
        long minute = (seconds - day * d - hour * h) / m;
        long second = (seconds - day * d - hour * h - minute * m);

        StringBuilder sb = new StringBuilder();
        if (day > 0) {
            sb.append(day + "d.");
        }
        if (hour > 0) {
            sb.append(hour + "h.");
        }
        if (minute > 0) {
            sb.append(minute + "m.");
        }
        if (second > 0) {
            sb.append(second + "s.");
        }
        if (sb.toString().endsWith(".")) {
            sb.deleteCharAt(sb.length() - 1);
        }
        return sb.toString();
    }

    /**
     * 解析到天
     */
    public static Date parse2Day(String str) {
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
        try {
            return sdf.parse(str);
        } catch (ParseException e) {
            e.printStackTrace();
        }
        return null;
    }

    /**
     * 格式化到秒
     */
    public static String format2Second(Date date) {
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        return sdf.format(date);
    }

    /**
     * 格式化到小时
     */
    public static String format2Minute(Date date) {
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm");
        return sdf.format(date);
    }

    /**
     * 格式化到秒
     */
    public static String format2Ms(Date date) {
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss.SSS");
        return sdf.format(date);
    }

    /**
     * 解析到秒
     */
    public static Date parse2Second(String str) {
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        try {
            return sdf.parse(str);
        } catch (ParseException e) {
            e.printStackTrace();
        }
        return null;
    }

    /**
     * 解析到秒
     */
    public static Date parse2Ms(String str) {
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss.SSS");
        try {
            return sdf.parse(str);
        } catch (ParseException e) {
            e.printStackTrace();
        }
        return null;
    }

    /**
     * 解析到秒
     */
    public static Date parse2Second2(String str) {
        SimpleDateFormat sdf = new SimpleDateFormat("yyyyMMddHHmmss");
        try {
            return sdf.parse(str);
        } catch (ParseException e) {
            e.printStackTrace();
        }
        return null;
    }

    /**
     * 解析到分钟
     */
    public static Date parse2Minute(String str) {
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm");
        try {
            return sdf.parse(str);
        } catch (ParseException e) {
            e.printStackTrace();
        }
        return null;
    }

    /**
     * 校验字符串日期（精确到秒）
     */
    public static boolean checkDate2Second(String sDate) {
        int legalLen = 19;
        if ((sDate == null) || (sDate.length() != legalLen)) {
            return false;
        }
        DateFormat formatter = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        try {
            Date date = formatter.parse(sDate);
            return sDate.equals(formatter.format(date));
        } catch (Exception e) {
            return false;
        }
    }

    /**
     * 获取最近N天的日期(不包括当天)
     */
    public static List<String> lateNDays(int N) {
        DateTime now = new DateTime();

        List<String> days = new ArrayList<>();
        for (int i = 1; i <= N; i++) {
            DateTime dateTime = now.minusDays(i);
            days.add(dateTime.toString("yyyyMMdd"));
        }
        return days;
    }

    /**
     * 获取最近N天的小时(不包括当天)
     */
    public static List<String> getNHours(int N) {
        DateTime now = new DateTime();
        now = now.withMinuteOfHour(0);
        now = now.withSecondOfMinute(0);

        List<String> days = new ArrayList<>();
        for (int i = 1; i <= N; i++) {
            DateTime dateTime = now.minusHours(i);
            days.add(dateTime.toString("yyyy-MM-dd HH:mm"));
        }
        return days;
    }

    /**
     * 获取某天距离现在时刻的小时数
     */
    public static List<String> getHours(String day) {
        DateTime cur = new DateTime(day);

        List<String> hours = new ArrayList<>();
        for (int i = 0; i <= new DateTime().hourOfDay().get(); i++) {
            DateTime dateTime = cur.plusHours(i);
            hours.add(dateTime.toString("yyyy-MM-dd HH:mm"));
        }
        return hours;
    }

    /**
     * 获取最近N天的日期(不包括当天)
     */
    public static String lateNHours(int N) {
        DateTime now = new DateTime();
        DateTime dateTime = now.minusHours(N);
        return dateTime.toString("yyyy-MM-dd HH:mm:ss");
    }

    /**
     * 获取未来N天的日期(包括当天)
     */
    public static List<String> futureNDays(int N) {
        DateTime now = new DateTime();

        List<String> days = new ArrayList<>();
        for (int i = 0; i < N; i++) {
            DateTime dateTime = now.plusDays(i);
            days.add(dateTime.toString("yyyyMMdd"));
        }
        return days;
    }

    /**
     * 判断两个时间段是否重合
     */
    public static boolean isTimeBetween(Date s1, Date e1, Date s2, Date e2) {
        /* 不重合的情况只有2种 */
        return !e1.before(s2) && !e2.before(s1);
    }

    /**
     * 计算两个时间相差的秒数
     */
    public static int secondsBetween(String startTime, String endTime) {
        SimpleDateFormat df = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        long sTime = 0;
        long eTime = 0;
        try {
            sTime = df.parse(startTime).getTime();
            eTime = df.parse(endTime).getTime();
        } catch (ParseException e) {
            e.printStackTrace();
        }
        return (int) ((eTime - sTime) / 1000);
    }

    /**
     * 计算两个时间相差的秒数
     */
    public static int secondsBetween(Date startTime, Date endTime) {
        return (int) ((endTime.getTime() - startTime.getTime()) / 1000);
    }

    /**
     * 计算两个时间相差的秒数
     */
    public static long millisBetween(String startTime, String endTime) {
        SimpleDateFormat df = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        long sTime = 0;
        long eTime = 0;
        try {
            sTime = df.parse(startTime).getTime();
            eTime = df.parse(endTime).getTime();
        } catch (ParseException e) {
            e.printStackTrace();
        }
        return eTime - sTime;
    }

    /**
     * 两日期之间天
     */
    public static List<String> days(String startTime, String endTime) {
        String format = "yyyy-MM-dd";
        SimpleDateFormat sfd = new SimpleDateFormat(format);
        DateTime sDate;
        DateTime eDate;
        try {
            sDate = new DateTime(sfd.parse(startTime));
            eDate = new DateTime(sfd.parse(endTime));
        } catch (ParseException e) {
            e.printStackTrace();
            return Collections.emptyList();
        }
        if (sDate.isAfter(eDate)) {
            return Collections.emptyList();
        }

        List<String> days = new ArrayList<>();
        while (!sDate.equals(eDate)) {
            days.add(sDate.toString(format));
            sDate = sDate.plusDays(1);
        }
        days.add(sDate.toString(format));
        return days;
    }

    /**
     * 两日期之间分钟
     */
    public static List<String> minutes(Date startTime, Date endTime) {
        String format = "yyyy-MM-dd HH:mm";
        DateTime sDate;
        DateTime eDate;
        sDate = new DateTime(startTime);
        eDate = new DateTime(endTime);
        if (sDate.isAfter(eDate)) {
            return Collections.emptyList();
        }

        List<String> minutes = new ArrayList<>();
        while (!sDate.toString(format).equals(eDate.toString(format))) {
            minutes.add(sDate.toString(format));
            sDate = sDate.plusMinutes(1);
        }
        minutes.add(sDate.toString(format));
        return minutes;
    }

    /**
     * 转换时间格式为xx小时xx分xx秒
     */
    public static String secondFormat(String second) {
        Integer seconds = Integer.valueOf(second);
        if (seconds > 0 && seconds < 60) {
            return seconds + "秒";
        } else if (seconds >= 60 && seconds < 3600) {
            int changeM = (int) Math.floor(seconds / 60);
            int surplusM = (int) Math.floor(seconds % 60);
            if (surplusM > 0) {
                return changeM + "分" + surplusM + "秒";
            } else {
                return changeM + "分";
            }
        } else if (seconds >= 3600) {
            int changeH = (int) Math.floor(seconds / 1048576);
            int surplusH = (int) Math.floor(seconds % 1048576);
            if (surplusH >= 60) {
                int changeM = (int) Math.floor(surplusH / 60);
                int surplusM = (int) Math.floor(surplusH % 60);
                if (surplusM > 0) {
                    return changeH + "小时" + changeM + "分" + surplusM + "秒";
                } else {
                    return changeH + "小时" + changeM + "分";
                }
            } else if (surplusH < 60 && surplusH > 0) {
                int surplusM = (int) Math.floor(surplusH % 1024);
                return changeH + "小时" + surplusM + "秒";
            } else {
                return changeH + "小时";
            }
        }
        return "暂无数据";
    }

    /**
     * 判断当前时间是否在[startTime, endTime]区间，注意三个参数的时间格式要一致
     */
    public static boolean isTimeBetween(Date nowTime, Date startTime, Date endTime) {
        if (nowTime.getTime() == startTime.getTime() || nowTime.getTime() == endTime.getTime()) {
            return true;
        }

        Calendar date = Calendar.getInstance();
        date.setTime(nowTime);

        Calendar begin = Calendar.getInstance();
        begin.setTime(startTime);

        Calendar end = Calendar.getInstance();
        end.setTime(endTime);

        return (date.getTime() == begin.getTime() || date.after(begin)) && date.before(end);
    }

    public static boolean isTimeCoincide(Date s1, Date e1, Date s2, Date e2) {
        if (e1.getTime() == s2.getTime() || e1.before(s2)) {
            return false;
        }
        return e2.getTime() != s1.getTime() && !e2.before(s1);
    }

    /***
     * 功能描述:
     * 对匹配截取的日期格式化
     * @Date: 2023-02-03 09:41:43
     * @Param dateStr:
     * @return: java.lang.String
     * @since: 1.0.0
     */
    public static String formatDate(String dateStr) {
        if (ObjectUtils.isEmpty(dateStr)) {
            return "";
        }
        LinkedHashMap<String, String> dateRegFormat = new LinkedHashMap();
        dateRegFormat.put(
                "\\d{4}\\D+\\d{1,2}\\D+\\d{1,2}\\D+\\d{1,2}\\D+\\d{1,2}\\D+\\d{1,2}",
                "yyyy-MM-dd-HH-mm-ss");//2014年3月12日 13时5分34秒，2014-03-12 12:05:34，2014/3/12 12:5:34
        dateRegFormat.put("\\d{4}\\D+\\d{2}\\D+\\d{2}\\D+\\d{2}\\D+\\d{2}",
                "yyyy-MM-dd-HH-mm");//2014-03-12 12:05
        dateRegFormat.put("\\d{4}\\D+\\d{2}\\D+\\d{2}\\D+\\d{2}",
                "yyyy-MM-dd-HH");//2014-03-12 12
        dateRegFormat.put("\\d{4}\\D+\\d{2}\\D+\\d{2}", "yyyy-MM-dd");//2014-03-12
        dateRegFormat.put("\\d{4}\\D+\\d{2}", "yyyy-MM");//2014-03
        dateRegFormat.put("\\d{4}", "yyyy");//2014
        dateRegFormat.put("\\d{14}", "yyyyMMddHHmmss");//20140312120534
        dateRegFormat.put("\\d{12}", "yyyyMMddHHmm");//201403121205
        dateRegFormat.put("\\d{10}", "yyyyMMddHH");//2014031212
        dateRegFormat.put("\\d{8}", "yyyyMMdd");//20140312
        dateRegFormat.put("\\d{6}", "yyyyMM");//201403
        dateRegFormat.put("\\d{2}\\s*:\\s*\\d{2}\\s*:\\s*\\d{2}",
                "yyyy-MM-dd-HH-mm-ss");//13:05:34  拼接当前日期
        dateRegFormat.put("\\d{2}\\s*:\\s*\\d{2}", "yyyy-MM-dd-HH-mm");//13:05  拼接当前日期
        dateRegFormat.put("\\d{2}\\D+\\d{1,2}\\D+\\d{1,2}", "yy-MM-dd");//14.10.18(年.月.日)
        dateRegFormat.put("\\d{1,2}\\D+\\d{1,2}", "yyyy-dd-MM");//30.12(日.月) 拼接当前年份
        dateRegFormat.put("\\d{1,2}\\D+\\d{1,2}\\D+\\d{4}", "dd-MM-yyyy");//12.21.2013(日.月.年)
        String curDate = new SimpleDateFormat("yyyy-MM-dd").format(new Date());
        DateFormat formatter1 = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        DateFormat formatter2;
        String dateReplace;
        String strSuccess = "";
        try {
            for (Object key : dateRegFormat.keySet()) {
                Pattern p = Pattern.compile(String.valueOf(key));
                Matcher matcher = p.matcher(dateStr);
                if (matcher.find()) {
                    dateStr = matcher.group(0);
                    formatter2 = new SimpleDateFormat(dateRegFormat.get(key));
                    if (key.equals("^\\d{2}\\s*:\\s*\\d{2}\\s*:\\s*\\d{2}$")
                            || key.equals("^\\d{2}\\s*:\\s*\\d{2}$")) {//13:05:34 或 13:05 拼接当前日期
                        dateStr = curDate + "-" + dateStr;
                    } else if (key.equals("^\\d{1,2}\\D+\\d{1,2}$")) {//21.1 (日.月) 拼接当前年份
                        dateStr = curDate.substring(0, 4) + "-" + dateStr;
                    }
                    dateReplace = dateStr.replaceAll("\\D+", "-");
                    try {
                        strSuccess = formatter1.format(formatter2.parse(dateReplace));
                    } catch (ParseException e) {
                        // 日期格式化出错后继续循环
                        e.printStackTrace();
                        continue;
                    }
                    break;
                }
            }
        } catch (Exception e) {
            e.printStackTrace();
        } finally {
            return strSuccess;
        }
    }

    /**
     * 两日期之间每隔N天的日期集合
     */
    public static List<Long> apartDate(String startTime, String endTime, int N) throws ParseException {
        long startMilli = parse2Day(startTime).getTime();
        long endMilli = parse2Day(endTime).getTime();
        if (startMilli >= endMilli) {
            return Collections.emptyList();
        }

        List<Long> list = new ArrayList<>();
        while (startMilli < endMilli) {
            list.add(startMilli);
            startMilli += N * 86400 * 1000;
        }
        return list;
    }

    public static long secondsBetweenByDay(String startTime, String endTime) throws ParseException {
        SimpleDateFormat df = new SimpleDateFormat("yyyy-MM-dd");
        long eTime = df.parse(endTime).getTime();
        long sTime = df.parse(startTime).getTime();
        long diff = (eTime - sTime) / 1000;
        return diff;
    }

    public static List<String> splitBySpan(String startTime, String endTime, int timeSpan) {
        String format = "yyyy-MM-dd HH:mm:ss";
        SimpleDateFormat sfd = new SimpleDateFormat(format);
        DateTime sDate;
        DateTime eDate;
        try {
            sDate = new DateTime(sfd.parse(startTime));
            eDate = new DateTime(sfd.parse(endTime));
        } catch (ParseException e) {
            e.printStackTrace();
            return Collections.emptyList();
        }
        if (sDate.isAfter(eDate)) {
            return Collections.emptyList();
        }
        sDate = sDate.plusSeconds(-1);

        List<String> spans = new ArrayList<>();
        DateTime temp = sDate;
        sDate = sDate.plusMinutes(timeSpan);
        while (!sDate.isAfter(eDate)) {
            spans.add(temp.plusSeconds(1).toString(format) + "," + sDate.toString(format));
            temp = sDate;
            sDate = sDate.plusMinutes(timeSpan);
        }
        return spans;
    }

    /**
     * 将时间加上天
     */
    public static Date addDays(Date date, int days) {
        Calendar cal = Calendar.getInstance();
        cal.setTime(date);
        cal.add(Calendar.DATE, days);
        return cal.getTime();
    }

    public static boolean isOverlap(Date s1, Date e1, String sTime, String eTime) {
        Date s2 = parse2Second(sTime);
        Date e2 = parse2Second(eTime);

        return s1.before(s2) && e1.after(e2);
    }
}
