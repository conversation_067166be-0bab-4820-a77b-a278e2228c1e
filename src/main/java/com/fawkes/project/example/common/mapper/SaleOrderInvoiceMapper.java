package com.fawkes.project.example.common.mapper;

import com.fawkes.core.base.model.BaseEntity;
import com.fawkes.project.example.common.model.SaleOrderInvoice;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface SaleOrderInvoiceMapper {

    /**
     * 判断合同编号是否已存在
     *
     * @param invoice
     * @return
     */
    Boolean isExistedInvoiceNo(@Param("invoice") SaleOrderInvoice invoice);

    /**
     * 根据id查询合同
     *
     * @param id
     * @return
     */
    SaleOrderInvoice selectByPrimaryKey(Long id);

    /**
     * 根据订单id查询合同
     *
     * @param orderId
     * @return
     */
    List<SaleOrderInvoice> selectByOrderId(@Param("orderId") Long orderId);

    /**
     * 添加合同
     *
     * @param record
     * @return
     */
    int insert(SaleOrderInvoice record);

    /**
     * 更新合同
     *
     * @param record
     * @return
     */
    int updateByPrimaryKey(@Param("invoice") SaleOrderInvoice record);

    /**
     * 删除合同
     *
     * @param id
     * @param updateInfo
     * @return
     */
    int deleteByPrimaryKey(@Param("id") Long id, @Param("updateInfo") BaseEntity updateInfo);
}