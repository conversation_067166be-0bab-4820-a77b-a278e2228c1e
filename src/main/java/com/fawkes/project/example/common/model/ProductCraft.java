package com.fawkes.project.example.common.model;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import com.fawkes.core.base.model.BaseEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDate;

@Data
@ToString
@NoArgsConstructor
public class ProductCraft extends BaseEntity implements Serializable {
    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "产品（型号）ID")
    @JsonSerialize(using = ToStringSerializer.class)
    private Long productId;

    @ApiModelProperty(value = "工艺版本号")
    private String craftCode;

    @ApiModelProperty(value = "耗材成本")
    private BigDecimal consumeCost;

    @ApiModelProperty(value = "应用现状")
    private Boolean applyStatus;

    @ApiModelProperty(value = "应用起始日期")
    private LocalDate applyStartDate;

    @ApiModelProperty(value = "应用终止日期")
    private LocalDate applyEndDate;

    @ApiModelProperty(value = "图片")
    private String pic;
}