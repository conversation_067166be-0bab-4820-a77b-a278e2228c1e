package com.fawkes.project.example.common.utils;

import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.CharUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.poi.ss.usermodel.*;
import org.apache.poi.xssf.usermodel.XSSFCellStyle;
import org.apache.poi.xssf.usermodel.XSSFRichTextString;
import org.apache.poi.xssf.usermodel.XSSFSheet;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;

import java.beans.IntrospectionException;
import java.beans.PropertyDescriptor;
import java.io.*;
import java.lang.reflect.Constructor;
import java.lang.reflect.Field;
import java.lang.reflect.InvocationTargetException;
import java.lang.reflect.Method;
import java.math.BigDecimal;
import java.text.ParseException;
import java.time.Instant;
import java.time.format.DateTimeParseException;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * Excel工具类
 * 工具类只提供static方法，禁止实例化
 *
 * <AUTHOR>
 */
public class ExcelUtils {
    private final static String EXCEL2003 = "xls";
    private final static String EXCEL2007 = "xlsx";

    private ExcelUtils() {
    }

    /**
     * @param cls   对应实体类
     * @param heads 实体类列名对应Excel列名 例：{{"dept","部门"},{"post","岗位"},{"name","姓名"}}
     * @param <T>   指定泛型
     * @return 返回读取结果
     * @throws IOException 上层调用自行处理文件流异常
     */
    public static <T> List<T> readExcel(String filepath, Class<T> cls, String[][] heads) throws Exception {
        List<T> dataList = new ArrayList<>();

        File file = new File(filepath);
        InputStream is = new FileInputStream(file);
        XSSFWorkbook wb = new XSSFWorkbook(is);
        XSSFSheet sheet = wb.getSheetAt(0);

        /* 校验标题栏 */
        Row headRow = sheet.getRow(0);
        checkExcelHead(headRow, heads);

        for (int i = 1; i <= sheet.getLastRowNum(); i++) {
            Row row = sheet.getRow(i);

            // 过滤掉空行
            if (row == null || row.toString().isEmpty()) {
                T t = cls.newInstance();
                Field field = t.getClass().getDeclaredField("importErrorMsg");
                field.setAccessible(true);
                field.set(t, "空行，已忽略");
                dataList.add(t);
                continue;
            }

            T data;
            try {
                data = readFromRow(row, cls, heads);
            } catch (Exception e) {
                T t = cls.newInstance();
                Field field = t.getClass().getDeclaredField("importErrorMsg");
                field.setAccessible(true);
                field.set(t, e.getMessage());
                dataList.add(t);
                continue;
            }

            // 通过ValidatorFactory触发实体上的Validator校验
            ValidationResult validationResult = ValidationUtils.validateEntity(data);
            if (validationResult.getHasError()) {
                // 封装错误信息
                Map<String, String> errorMsg = validationResult.getErrorMsg();
                StringBuilder sb = new StringBuilder();
                errorMsg.forEach((k, v) -> {
                    sb.append(v);
                    sb.append("。");
                });

                Field field = data.getClass().getDeclaredField("importErrorMsg");
                field.setAccessible(true);
                field.set(data, sb.toString());
            }
            dataList.add(data);
        }
        return dataList;
    }

    /**
     * 写Excel
     */
    public static void writeExcel(String filepath, Map<Integer, String> msgs, int colNum) throws Exception {
        File file = new File(filepath);
        InputStream is = new FileInputStream(file);
        XSSFWorkbook wb = new XSSFWorkbook(is);
        XSSFSheet sheet = wb.getSheetAt(0);

        for (int i = 1; i <= sheet.getLastRowNum(); i++) {
            int idx = i - 1;
            Row row = sheet.getRow(i);

            if (ObjectUtils.isEmpty(msgs.get(idx))) {
                continue;
            }

            if (row == null || row.toString().isEmpty()) {
                row = sheet.createRow(i);
            }

            // 将错误信息写到当前行的最后一列
            Cell lastCol = row.createCell(colNum);
            lastCol.setCellValue(new XSSFRichTextString(msgs.get(idx)));

            // 设置字体样式
            XSSFCellStyle style = wb.createCellStyle();
            lastCol.setCellStyle(style);

            Font font = wb.createFont();
            font.setFontName("等线");
            font.setColor(IndexedColors.RED.getIndex());
            style.setFont(font);
        }


        // 删除没有错误的行
//        int count = 0;
//        int lastRowNum = sheet.getLastRowNum();
//        for (int i = 1; i < lastRowNum; i++) {
//            if (ObjectUtils.isEmpty(msgs.get(i - 1))) {
//                sheet.shiftRows(i + 1 - count, sheet.getLastRowNum(), -1);
//                count ++;
//            }
//        }

        // 重新保存Excel
        FileOutputStream fos = new FileOutputStream(file);
        wb.write(fos);
        wb.close();
        fos.close();
    }

    // 判断行是否为空
    public static boolean isRowEmpty(Row row) {
        for (int c = row.getFirstCellNum(); c < row.getLastCellNum(); c++) {
            Cell cell = row.getCell(c);
            if (cell != null && cell.getCellType() != CellType.BLANK) {  //此处判断得根据poi版本来修改相应判断
                return false;
            }
        }
        return true;
    }

    /**
     * 校验Excel的标题栏
     */
    private static void checkExcelHead(Row headRow, String[][] heads) throws Exception {
        int firstCellNum = headRow.getFirstCellNum();
        for (int j = 0; j < heads.length; j++) {
            Cell cell = headRow.getCell(firstCellNum + j);
            String cellValue = cell.getStringCellValue().trim();
            if (!cellValue.equals(heads[j][1])) {
                throw new ExcelValidException("非法模板，请下载正确模板");
            }
        }
    }

    /**
     * 将Excel中内容设置到实体类
     */
    private static <T> T readFromRow(Row row, Class<T> cls, String[][] heads) throws InstantiationException, NoSuchFieldException, IllegalAccessException, ExcelValidException {
        T t = cls.newInstance();
        for (int j = 0; j < heads.length; j++) {
            Cell cell = row.getCell(j);
            String cellValue = getCellValue(cell);
            Field field = cls.getDeclaredField(heads[j][0]);
            try {
                handleField(t, cellValue, field);
            } catch (NumberFormatException e) {
                throw new ExcelValidException("\"" + heads[j][1] + "\"列请输入数字");
            } catch (DateTimeParseException e) {
                throw new ExcelValidException("\"" + heads[j][1] + "\"列请输入正确格式日期");
            } catch (Exception e) {
                throw new ExcelValidException("\"" + heads[j][1] + "\"列类型不符合要求");
            }
        }
        return t;
    }


    private static <T> void handleField(T t, String value, Field field) throws IllegalAccessException, NoSuchMethodException,
            InvocationTargetException, InstantiationException, IntrospectionException, NumberFormatException, DateTimeParseException, ParseException {
        PropertyDescriptor pd = new PropertyDescriptor(field.getName(), t.getClass());
        Method method = pd.getWriteMethod();
        Class<?> type = field.getType();
        if (type == void.class || StringUtils.isBlank(value)) {
            return;
        }
        if (type == Object.class) {
            method.invoke(t, value);
        } else if (type.getSuperclass() == null || type.getSuperclass() == Number.class) {
            //数字类型
            if (type == int.class || type == Integer.class) {
                method.invoke(t, Double.valueOf(value).intValue());
            } else if (type == long.class || type == Long.class) {
                method.invoke(t, Double.valueOf(value).longValue());
            } else if (type == byte.class || type == Byte.class) {
                method.invoke(t, Double.valueOf(value).byteValue());
            } else if (type == short.class || type == Short.class) {
                method.invoke(t, Double.valueOf(value).shortValue());
            } else if (type == double.class || type == Double.class) {
                method.invoke(t, Double.valueOf(value));
            } else if (type == float.class || type == Float.class) {
                method.invoke(t, Double.valueOf(value).floatValue());
            } else if (type == char.class || type == Character.class) {
                method.invoke(t, CharUtils.toChar(value));
            } else if (type == boolean.class) {
                method.invoke(t, BooleanUtils.toBoolean(value));
            } else if (type == BigDecimal.class) {
                method.invoke(t, new BigDecimal(value));
            }
        } else if (type == Boolean.class) {
            method.invoke(t, BooleanUtils.toBoolean(value));
        } else if (type == Date.class) {
            //使用Instant类接受Date值
            try {
                method.invoke(t, Date.from(Instant.parse(value)));
            } catch (Exception e) {
                Date date = TimeUtil.parse2Second(TimeUtil.formatDate(value));
                method.invoke(t, date);
            }
        } else if (type == String.class) {
            method.invoke(t, value);
        } else {
            Constructor<?> constructor = type.getConstructor(String.class);
            method.invoke(t, constructor.newInstance(value));
        }
    }

    private static String getCellValue(Cell cell) {
        if (cell == null) {
            return "";
        }
        if (cell.getCellType() == CellType.NUMERIC) {
            if (DateUtil.isCellDateFormatted(cell)) {
                //使用Instant类解析传递Date值
                return DateUtil.getJavaDate(cell.getNumericCellValue()).toInstant().toString();
            } else {
                double n = cell.getNumericCellValue();
                if (Math.round(n) == n) {
                    return BigDecimal.valueOf(cell.getNumericCellValue()).intValue() + "";
                } else {
                    return BigDecimal.valueOf(cell.getNumericCellValue()).toString();
                }
            }
        } else if (cell.getCellType() == CellType.STRING) {
            return StringUtils.trimToEmpty(cell.getStringCellValue());
        } else if (cell.getCellType() == CellType.FORMULA) {
            return StringUtils.trimToEmpty(cell.getCellFormula());
        } else if (cell.getCellType() == CellType.BLANK) {
            return "";
        } else if (cell.getCellType() == CellType.BOOLEAN) {
            return String.valueOf(cell.getBooleanCellValue());
        } else if (cell.getCellType() == CellType.ERROR) {
            return "ERROR";
        } else {
            return cell.toString().trim();
        }

    }


}
