package com.fawkes.project.example.common.listener;


import com.fawkes.core.constants.QueueConstants;
import com.fawkes.core.utils.SpringTool;
import com.fawkes.core.utils.StringPool;
import com.fawkes.core.utils.jackson.JsonTool;
import com.fawkes.project.example.common.constants.FormConstants;
import com.fawkes.project.example.service.ICommonService;
import com.fawkes.stream.msg.send.bpm.BpmFormProcessStateMsg;
import com.rabbitmq.client.Channel;
import lombok.extern.slf4j.Slf4j;
import org.springframework.amqp.core.Queue;
import org.springframework.amqp.rabbit.annotation.RabbitListener;
import org.springframework.amqp.rabbit.connection.Connection;
import org.springframework.amqp.rabbit.connection.ConnectionFactory;
import org.springframework.boot.context.event.ApplicationStartedEvent;
import org.springframework.context.ApplicationEvent;
import org.springframework.context.annotation.Bean;
import org.springframework.context.event.SmartApplicationListener;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;

/**
 * 表单流程状态监听
 *
 * <AUTHOR>
 * @date 2021-06-09
 */
@Slf4j
@Component
public class FormProcessStateListener implements SmartApplicationListener {

    /**
     * 创建服务自己的队列
     */
    private static final String PROCESS_STATE_QUEUE = QueueConstants.SysBpmQueue.SYS_BPM_FORM_PROCESS_STATE_QUEUE + StringPool.DASH + FormConstants.APPLICATION_NAME;
    @Resource
    private ICommonService iCommonService;
    @Resource
    private ConnectionFactory connectionFactory;

    @Bean
    public Queue serviceQueue() {
        return new Queue(PROCESS_STATE_QUEUE);
    }

    @RabbitListener(queues = PROCESS_STATE_QUEUE)
    private void listener(byte[] bytes) {
        try {
            BpmFormProcessStateMsg processState = JsonTool.parse(bytes, BpmFormProcessStateMsg.class);
            Object clazz = SpringTool.getBean((StringUtils.uncapitalize(processState.getEntityName()) + FormConstants.FORM_MAPPER));
            if (clazz != null) {
                iCommonService.updateFormProcessState(processState, clazz);
            }
        } catch (Exception e) {
            log.error("流程状态回写失败:", e);
        }
    }

    @Override
    public boolean supportsEventType(Class<? extends ApplicationEvent> aClass) {
        return aClass == ApplicationStartedEvent.class;
    }

    @Override
    public void onApplicationEvent(ApplicationEvent applicationEvent) {
        Connection connection = connectionFactory.createConnection();
        Channel channel = connection.createChannel(false);
        try {
            log.info("流程状态监听队列绑定交换机");
            //实例化一个 持久化 非独占 空闲不删除 无其余参数的队列
            channel.queueDeclare(PROCESS_STATE_QUEUE, true, false, false, null);
            //绑定队列和交换机
            channel.queueBind(PROCESS_STATE_QUEUE, QueueConstants.SysBpmQueue.SYS_BPM_FORM_PROCESS_STATE_EXCHANGE, StringPool.EMPTY);
        } catch (Exception e) {
            log.error(e.getMessage());
        }
    }
}
