package com.fawkes.project.example.common.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

@Getter
@AllArgsConstructor
public enum MapperMethodEnums {
    countByExample("countByExample"),
    deleteByExample("deleteByExample"),
    insert("insert"),
    insertSelective("insertSelective"),
    selectByExample("selectByExample"),
    updateByExampleSelective("updateByExampleSelective"),
    updateByExample("updateByExample"),
    updateByPrimaryKeySelective("updateByPrimaryKeySelective"),
    selectByPrimaryKey("selectByPrimaryKey"),
    deleteByPrimaryKey("deleteByPrimaryKey"),
    createCriteria("createCriteria"),
    andFkIdEqualTo("andFkIdEqualTo"),
    andDeleteFlagEqualTo("andDeleteFlagEqualTo"),
    andIdIn("andIdIn");
    /**
     * 方法
     */
    private final String Method;


}
