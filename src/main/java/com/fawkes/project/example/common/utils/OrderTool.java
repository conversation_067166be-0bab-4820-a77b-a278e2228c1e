package com.fawkes.project.example.common.utils;

import com.google.common.base.Strings;
import org.springframework.util.ObjectUtils;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.text.DecimalFormat;
import java.text.NumberFormat;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.Collections;
import java.util.Comparator;
import java.util.List;
import java.util.Objects;
import java.util.regex.Pattern;

/**
 * <AUTHOR>
 */
public class OrderTool {

    /**
     * 配件采购订单
     */
    private final static String ACCESSORY_ORDER_PREFIX = "PJCG";

    /**
     * 销售订单
     */
    private final static String SALE_ORDER_PREFIX = "CPXS";

    /**
     * 销售订单
     */
    private final static String USE_ORDER_PREFIX = "SBLY";

    private final static String FORMAT_DATE = "yyyyMMdd";

    private final static String FORMAT_YEAR = "yyyy";

    private static final DecimalFormat FORMAT_LENGTH_3 = new DecimalFormat("000");

    private static final DecimalFormat FORMAT_LENGTH_4 = new DecimalFormat("0000");

    private static final Pattern CRAFT_VERSION_PATTERN = Pattern.compile("^[v]\\d+\\.\\d+$");

    private static final String[] MONTH_STR = {"一月", "二月", "三月", "四月", "五月", "六月", "七月", "八月", "九月", "十月", "十一月", "十二月"};

    private static final String PRODUCE_COMPANY_CODE = "HD";


    /**
     * 生成配件入库订单号
     *
     * @param orderDate
     * @param prevOrderNo
     * @return
     */
    public static String genAccessoryOrderNoFromPrev(LocalDate orderDate, String prevOrderNo) {
        Integer count = Strings.isNullOrEmpty(prevOrderNo) ? 0 : Integer.parseInt(prevOrderNo.substring(FORMAT_DATE.length() + ACCESSORY_ORDER_PREFIX.length()));
        return String.format("%s%s%s", orderDate.format(DateTimeFormatter.ofPattern(FORMAT_DATE)), ACCESSORY_ORDER_PREFIX, FORMAT_LENGTH_3.format(count + 1));
    }

    /**
     * 生成销售订单编号
     *
     * @param prevOrderNo 数据库中的最新订单编号
     * @return
     */
    public static String genSaleOrderNoFromPrev(LocalDate crateDate, String prevOrderNo) {
        Integer count = Strings.isNullOrEmpty(prevOrderNo) ? 0 : Integer.parseInt(prevOrderNo.substring(SALE_ORDER_PREFIX.length() + FORMAT_YEAR.length()));
        return String.format("%s%s%s", SALE_ORDER_PREFIX, crateDate.format(DateTimeFormatter.ofPattern(FORMAT_YEAR)), FORMAT_LENGTH_4.format(count + 1));
    }

    /**
     * 生成设备领用订单编号
     *
     * @param localDate
     * @param prevOrderNo
     * @return
     */
    public static String genUseOrderNoFromPrev(LocalDate localDate, String prevOrderNo) {
        Integer count = Strings.isNullOrEmpty(prevOrderNo) ? 0 : Integer.parseInt(prevOrderNo.substring(USE_ORDER_PREFIX.length() + FORMAT_YEAR.length()));
        return String.format("%s%s%s", USE_ORDER_PREFIX, localDate.format(DateTimeFormatter.ofPattern(FORMAT_YEAR)), FORMAT_LENGTH_4.format(count + 1));
    }

    /**
     * 生成客户编号
     *
     * @param clientType   客户类型
     * @param prevClientNo 数据库中最大客户序列号
     * @return
     */
    public static String genClientNoFromPrev(String clientType, String prevClientNo) {
        Integer count = Strings.isNullOrEmpty(prevClientNo) ? 0 : Integer.parseInt(prevClientNo.substring(clientType.length()));
        return String.format("%s%s", clientType, FORMAT_LENGTH_4.format(count + 1));
    }

    /**
     * 生成产品对象编号前缀
     * 公司名称代码(2位）+产品种类代码(2位）+产品生产年份(2位）+产品批次代码(3位）
     *
     * @param categoryCode
     * @param year
     * @param batch
     * @return
     */
    public static String genProductObjPrefix(String categoryCode, int year, String batch) {
        return String.format("%s%s%s%s", PRODUCE_COMPANY_CODE, categoryCode, year % 100, batch);
    }

    /**
     * 产品对象编号前缀长度
     *
     * @return
     */
    public static Integer getProductObjPrefixLength() {
        return PRODUCE_COMPANY_CODE.length() + 2 + 2 + 3;
    }

    /**
     * 产品工艺版本号
     *
     * @param versions - 已有的所有版本号
     * @return
     */
    public static String genCraftCodeFromCodeList(List<String> versions) {
        if (ObjectUtils.isEmpty(versions)) {
            return "v0.1";
        }

        Comparator<String> versionComparator = (v1, v2) -> {
            String[] vs1 = v1.replace("v", "").split("\\.");
            String[] vs2 = v2.replace("v", "").split("\\.");

            if (vs1.length != 2 || vs2.length != 2) {
                return 0;
            }

            int num1 = Integer.parseInt(vs1[0]);
            int num2 = Integer.parseInt(vs2[0]);
            if (num1 == num2) {
                // 继续比较
                int num3 = Integer.parseInt(vs1[1]);
                int num4 = Integer.parseInt(vs2[1]);
                return Integer.compare(num3, num4);
            } else {
                return Integer.compare(num1, num2);
            }
        };

        // 使用自定义比较器找到最大版本号
        String maxVersion = Collections.max(versions, versionComparator);
        return incrementLastNumber(maxVersion);
    }

    /**
     * 产品工艺版本号
     *
     * @param version
     * @return
     */
    public static String incrementLastNumber(String version) {
        // 移除'v'并分割版本号
        String[] parts = version.substring(1).split("\\.");
        int lastIndex = parts.length - 1;

        // 将最后一部分转换为整数并加一
        int lastNumber = Integer.parseInt(parts[lastIndex]) + 1;
        parts[lastIndex] = String.valueOf(lastNumber);

        // 将数组转换回版本字符串并添加'v'前缀
        StringBuilder sb = new StringBuilder();
        for (int i = 0; i < parts.length; i++) {
            sb.append(parts[i]);
            if (i < parts.length - 1) {
                sb.append(".");
            }
        }
        return "v" + sb;
    }

    /**
     * 校验工艺版本号格式
     *
     * @param version
     * @return
     */
    public static boolean isValidCraftCode(String version) {
        if (version == null || version.isEmpty()) {
            return false;
        }
        return CRAFT_VERSION_PATTERN.matcher(version).matches();
    }

    /**
     * 判断值是否变化
     *
     * @param oldValue 旧值
     * @param newValue 新值
     * @return
     */
    public static boolean isChangedValue(Object oldValue, Object newValue) {
        boolean hasNewValue = Objects.isNull(oldValue) && !Objects.isNull(newValue);
        boolean hasChangedValue = !Objects.isNull(oldValue) && !oldValue.equals(newValue);
        return hasNewValue || hasChangedValue;
    }

    /**
     * 判断值是否变化
     *
     * @param oldValue 旧值
     * @param newValue 新值
     * @return
     */
    public static boolean isChangedBigDecimal(BigDecimal oldValue, BigDecimal newValue) {
        boolean hasNewValue = Objects.isNull(oldValue) && !Objects.isNull(newValue);
        boolean hasChangedValue = !Objects.isNull(oldValue) && oldValue.compareTo(newValue) != 0;
        return hasNewValue || hasChangedValue;
    }

    /**
     * BigDecimal 加法
     *
     * @param a
     * @param b
     */
    public static BigDecimal bdAdd(BigDecimal a, BigDecimal b) {
        a = a.add(b);
        return a;
    }

    /**
     * BigDecimal 减法
     *
     * @param a
     * @param b
     * @return
     */
    public static BigDecimal bdSub(BigDecimal a, BigDecimal b) {
        a = a.subtract(b);
        return a;
    }

    public static BigDecimal bdMul(BigDecimal a, BigDecimal b) {
        a = a.multiply(b);
        return a;
    }

    /**
     * BigDecimal 除法
     *
     * @param a
     * @param b
     * @return
     */
    public static BigDecimal bdDivide(BigDecimal a, BigDecimal b) {
        if (Objects.isNull(a) || bdIsZERO(a) || Objects.isNull(b) || bdIsZERO(b)) {
            return BigDecimal.ZERO;
        }
        return a.divide(b, 2, RoundingMode.HALF_UP);
    }

    /**
     * 百分比
     *
     * @param a
     * @param b
     * @return
     */
    public static String bdPercent(BigDecimal a, BigDecimal b) {
        NumberFormat percentFormat = NumberFormat.getPercentInstance();
        percentFormat.setMinimumFractionDigits(2);
        BigDecimal percent = BigDecimal.ZERO;
        if (!Objects.isNull(a) && !bdIsZERO(a) && !Objects.isNull(b) && !bdIsZERO(b)) {
            percent = OrderTool.bdDivide(a, b);
        }
        return percentFormat.format(percent);
    }

    /**
     * BigDecimal 比较大小
     *
     * @param a
     * @param b
     * @return
     */
    public static Boolean bdIsLessThan(BigDecimal a, BigDecimal b) {
        return a.compareTo(b) < 0;
    }

    /**
     * BigDecimal 判断是否为 0
     *
     * @param value
     * @return
     */
    public static Boolean bdIsZERO(BigDecimal value) {
        return value.compareTo(BigDecimal.ZERO) == 0;
    }

    public static String month2str(String monthNum) {
        return month2str(Integer.parseInt(monthNum));
    }

    public static String month2str(int month) {
        return MONTH_STR[month - 1];
    }
}
