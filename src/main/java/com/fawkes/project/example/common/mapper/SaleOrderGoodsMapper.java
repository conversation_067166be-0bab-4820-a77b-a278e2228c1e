package com.fawkes.project.example.common.mapper;

import com.fawkes.project.example.common.model.SaleOrderGoods;
import com.fawkes.project.example.domain.vo.SaleOrderGoodsVO;
import com.fawkes.project.example.domain.vo.SaleOrderGoodsWithStatVO;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface SaleOrderGoodsMapper {

    /**
     * 根据订单id查询销售明细详情列表
     *
     * @param orderId
     * @return
     */
    List<SaleOrderGoodsVO> listByOrderId(@Param("orderId") Long orderId);

    /**
     * 根据订单id查询销售明细
     *
     * @param orderIds
     * @return
     */
    List<SaleOrderGoods> listByOrderIds(@Param("orderIds") List<Long> orderIds);

    /**
     * 查询销售明细（带统计信息）
     *
     * @param orderIds
     * @return
     */
    List<SaleOrderGoodsWithStatVO> listWithStatByOrderIds(@Param("orderIds") List<Long> orderIds);

    /**
     * 批量插入
     *
     * @param goodsList
     * @return
     */
    int insertBatch(@Param("list") List<? extends SaleOrderGoods> goodsList);

    /**
     * 批量更新
     *
     * @param goodsList
     * @return
     */
    int updateBatch(@Param("list") List<? extends SaleOrderGoods> goodsList);

    /**
     * 批量删除
     *
     * @param goodsList
     * @return
     */
    int deleteBatch(@Param("list") List<? extends SaleOrderGoods> goodsList);
}