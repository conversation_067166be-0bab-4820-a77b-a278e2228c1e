<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.fawkes.project.example.common.mapper.BizMsgMapper">
    <resultMap id="BizMsg" type="com.fawkes.project.example.common.model.BizMsg"
               extends="com.fawkes.project.example.common.mapper.BaseMapper.BaseEntity">
        <result column="to_user_id" jdbcType="VARCHAR" property="toUserId"/>
        <result column="title" jdbcType="VARCHAR" property="title"/>
        <result column="content" jdbcType="VARCHAR" property="content"/>
        <result column="status" jdbcType="INTEGER" property="status"/>
    </resultMap>

    <select id="selectByToUserId" resultMap="BizMsg">
        SELECT *
        FROM biz_msg
        WHERE delete_flag = 0
          AND to_user_id = #{toUserId}
        ORDER BY create_date DESC
    </select>

    <select id="getObjsByIds" resultType="com.fawkes.project.example.common.model.ProductObject">
        SELECT
        a.object_no AS objectNo,
        a.actual_finish_time AS checkFinishTime,
        a.order_id AS orderId,
        c.`name` AS productName,
        c.model AS productModel,
        cert_type AS certType,
        d.produce_user_name AS produceUserName
        FROM
        product_object a
        LEFT JOIN product_craft b ON a.craft_id = b.id
        LEFT JOIN product c ON b.product_id = c.id
        LEFT JOIN product_order d on a.order_id = d.id
        WHERE
        a.id IN
        <foreach collection="ids" item="id" separator=", " open="(" close=")">
            #{id}
        </foreach>
    </select>

    <insert id="insertBatch">
        insert into biz_msg (id, to_user_id, title, content, status, create_date, create_by, delete_flag)
        values
        <foreach collection="bizMsgList" item="item" separator=",">
            (#{item.id}, #{item.toUserId}, #{item.title}, #{item.content}, #{item.status}, #{item.createDate},
            #{item.createBy}, #{item.deleteFlag})
        </foreach>
    </insert>

    <update id="updateStatusByToUserId">
        UPDATE biz_msg
        SET `status` = #{status,jdbcType=INTEGER}
        WHERE delete_flag = 0
          AND to_user_id = #{toUserId,jdbcType=BIGINT}
    </update>

</mapper>