<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.fawkes.project.example.common.mapper.ProductParamMapper">
    <resultMap id="ProductParam" type="com.fawkes.project.example.common.model.ProductParam"
               extends="com.fawkes.project.example.common.mapper.BaseMapper.BaseEntity">
        <result column="param_name" jdbcType="VARCHAR" property="paramName"/>
        <result column="param_remark" jdbcType="VARCHAR" property="paramRemark"/>
        <result column="param_parent" jdbcType="BIGINT" property="paramParent"/>
    </resultMap>
    <resultMap id="ProductParamDTO" type="com.fawkes.project.example.domain.dto.ProductParamDTO"
               extends="ProductParam"/>

    <sql id="Column_List_ProductParam">
        id
        , create_by, create_date, update_by, update_date, delete_flag, param_name, param_remark,
    param_parent
    </sql>

    <select id="isExistedParamName" resultType="java.lang.Boolean">
        SELECT COUNT(*) FROM product_param
        WHERE delete_flag = 0
        AND param_name = #{param.paramName}
        <if test="param.paramParent == null">AND param_parent IS NULL</if>
        <if test="param.paramParent != null">AND param_parent = #{param.paramParent}</if>
        <if test="param.id != null">AND id != #{param.id}</if>
    </select>

    <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="ProductParamDTO">
        select
        <include refid="Column_List_ProductParam"/>
        from product_param where id = #{id,jdbcType=BIGINT}
    </select>

    <select id="selectByParamParentIds" resultMap="ProductParam">
        select
        <include refid="Column_List_ProductParam"/>
        from product_param
        where delete_flag = 0
        AND param_parent IN
        <foreach collection="paramParentIds" item="paramParent" separator=", " open="(" close=")">#{paramParent}
        </foreach>
    </select>

    <select id="selectByIds" resultMap="ProductParam">
        select
        <include refid="Column_List_ProductParam"/>
        from product_param
        where id IN
        <foreach collection="ids" item="id" separator=", " open="(" close=")">#{id}</foreach>
    </select>

    <select id="selectProductParamList" resultMap="ProductParamDTO">
        select
        <include refid="Column_List_ProductParam"/>
        from product_param
        where delete_flag != -1 and param_parent is null
        <if test="name != null and name != ''">
            and param_name LIKE CONCAT('%', #{name}, '%')
        </if>
        order by create_date DESC
    </select>

    <insert id="insert" parameterType="com.fawkes.project.example.common.model.ProductParam">
        insert into product_param (id, create_by, create_date,
                                   update_by, update_date, delete_flag,
                                   param_name, param_remark, param_parent)
        values (#{id,jdbcType=BIGINT}, #{createBy,jdbcType=VARCHAR}, #{createDate,jdbcType=TIMESTAMP},
                #{updateBy,jdbcType=VARCHAR}, #{updateDate,jdbcType=TIMESTAMP}, #{deleteFlag,jdbcType=INTEGER},
                #{paramName,jdbcType=VARCHAR}, #{paramRemark,jdbcType=VARCHAR}, #{paramParent,jdbcType=BIGINT})
    </insert>

    <update id="updateByPrimaryKey" parameterType="com.fawkes.project.example.common.model.ProductParam">
        update product_param
        set update_by    = #{updateBy,jdbcType=VARCHAR},
            update_date  = #{updateDate,jdbcType=TIMESTAMP},
            param_name   = #{paramName,jdbcType=VARCHAR},
            param_remark = #{paramRemark,jdbcType=VARCHAR},
            param_parent = #{paramParent,jdbcType=BIGINT}
        where id = #{id,jdbcType=BIGINT}
    </update>

    <update id="deleteById">
        update product_param
        set delete_flag = -1,
            update_by   = #{updateInfo.updateBy,jdbcType=VARCHAR},
            update_date = #{updateInfo.updateDate,jdbcType=TIMESTAMP}
        where id = #{id}
    </update>
</mapper>