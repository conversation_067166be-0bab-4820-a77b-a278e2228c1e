package com.fawkes.project.example.common.mapper;

import com.fawkes.core.base.model.BaseEntity;
import com.fawkes.project.example.common.model.ProductParam;
import com.fawkes.project.example.domain.dto.ProductParamDTO;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface ProductParamMapper {
    /**
     * 校验产品参数是否已存在
     *
     * @param param
     * @return
     */
    Boolean isExistedParamName(@Param("param") ProductParam param);

    /**
     * 按照id查询
     *
     * @param id
     * @return
     */
    ProductParamDTO selectByPrimaryKey(Long id);

    /**
     * 根据父级id查询
     *
     * @param paramParentIds
     * @return
     */
    List<ProductParam> selectByParamParentIds(@Param("paramParentIds") List<Long> paramParentIds);

    /**
     * 根据id查询
     *
     * @param ids
     * @return
     */
    List<ProductParam> selectByIds(@Param("ids") List<Long> ids);

    /**
     * 分页查询
     *
     * @param name
     * @return
     */
    List<ProductParamDTO> selectProductParamList(String name);

    /**
     * 新增
     *
     * @param record
     * @return
     */
    int insert(ProductParam record);

    /**
     * 删除
     *
     * @param record
     * @return
     */
    int updateByPrimaryKey(ProductParam record);

    /**
     * 删除产品参数
     *
     * @param id
     * @param updateInfo
     * @return
     */
    int deleteById(@Param("id") Long id, @Param("updateInfo") BaseEntity updateInfo);
}