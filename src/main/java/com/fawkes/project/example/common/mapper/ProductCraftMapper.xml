<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.fawkes.project.example.common.mapper.ProductCraftMapper">
    <resultMap id="ProductCraft" type="com.fawkes.project.example.common.model.ProductCraft"
               extends="com.fawkes.project.example.common.mapper.BaseMapper.BaseEntity">
        <result column="product_id" jdbcType="BIGINT" property="productId"/>
        <result column="craft_code" jdbcType="VARCHAR" property="craftCode"/>
        <result column="consume_cost" jdbcType="DECIMAL" property="consumeCost"/>
        <result column="apply_status" jdbcType="TINYINT" property="applyStatus"/>
        <result column="apply_start_date" jdbcType="DATE" property="applyStartDate"/>
        <result column="apply_end_date" jdbcType="DATE" property="applyEndDate"/>
        <result column="pic" jdbcType="LONGVARCHAR" property="pic"/>
    </resultMap>
    <resultMap id="ProductCraftVO" type="com.fawkes.project.example.domain.vo.ProductCraftVO" extends="ProductCraft">
        <result column="product_name" jdbcType="VARCHAR" property="productName"/>
        <result column="product_model" jdbcType="VARCHAR" property="productMode"/>
        <result column="guide_cost" jdbcType="DECIMAL" property="guideCost"/>
    </resultMap>
    <sql id="Column_List_ProductCraft">
        id
        , create_by, create_date, update_by, update_date, delete_flag, product_id, craft_code, consume_cost, apply_status, apply_start_date, apply_end_date
    </sql>

    <select id="isExistedCraftCode" resultType="java.lang.Boolean">
        SELECT count(*) from product_craft where delete_flag = 0 AND craft_code = #{craft.craftCode} AND product_id =
        #{craft.productId}
        <if test="craft.id != null">AND id != #{craft.id}</if>
    </select>

    <select id="selectUsedCraftByProductIds" resultMap="ProductCraft">
        SELECT DISTINCT a.*
        FROM product_craft a
        LEFT JOIN product_object b ON b.craft_id = a.id AND b.delete_flag = 0
        LEFT JOIN product_order c ON c.id = b.order_id AND c.delete_flag = 0
        LEFT JOIN sale_order_goods d ON d.craft_id = a.id AND d.delete_flag = 0
        LEFT JOIN sale_order e ON e.id = d.order_id AND e.delete_flag = 0
        WHERE
        a.delete_flag = 0
        AND b.craft_id IS NOT NULL
        AND d.craft_id IS NOT NULL
        AND a.product_id IN
        <foreach collection="productIds" item="productId" separator=", " open="(" close=")">#{productId}</foreach>
    </select>

    <select id="selectById" resultMap="ProductCraftVO">
        SELECT a.*,
               b.model                                                   AS product_model,
               IFNULL(a.consume_cost + SUM(d.cur_price * c.quantity), 0) AS guide_cost
        FROM product_craft a
                 LEFT JOIN product b ON b.id = a.product_id
                 LEFT JOIN product_craft_compose c ON c.craft_id = a.id
            AND c.delete_flag = 0
                 LEFT JOIN accessory d ON d.id = c.accessory_id AND d.accessory_type != '耗材'
        WHERE a.id = #{id,jdbcType=BIGINT}
        GROUP BY a.id, b.model </select>

    <select id="selectByIds" resultMap="ProductCraftVO">
        SELECT
            pc.*,
            p.name AS product_name,
            p.model AS product_model,
            IFNULL (pc.consume_cost + gt.total_price, 0) AS guide_cost
        FROM
            product_craft pc
                LEFT JOIN product p ON p.id = pc.product_id
                LEFT JOIN (
                SELECT
                    pcc.craft_id AS id,
                    SUM(a.cur_price * pcc.quantity) AS total_price
                FROM
                    product_craft_compose pcc
                        LEFT JOIN accessory a ON pcc.accessory_id = a.id
                WHERE
                    pcc.delete_flag = 0
                  AND a.accessory_type != '耗材'
                  AND pcc.craft_id in
                      <foreach open="(" close=")" collection="ids" item="id"  separator=", ">#{id}</foreach>
                GROUP BY
                    pcc.craft_id
            ) gt ON pc.id = gt.id
    </select>

    <select id="selectByProductIds" resultMap="ProductCraftVO">
        SELECT
        a.id,
        a.product_id,
        a.apply_status,
        a.consume_cost,
        b.model AS product_model,
        IFNULL( a.consume_cost + SUM( d.cur_price * c.quantity ), 0 ) AS guide_cost
        FROM
        product_craft a
        LEFT JOIN product b ON b.id = a.product_id
        LEFT JOIN product_craft_compose c ON c.craft_id = a.id
        AND c.delete_flag = 0
        LEFT JOIN accessory d ON d.id = c.accessory_id AND d.accessory_type != '耗材'
        <where>
            a.delete_flag = 0
            <if test="applyStatus != null">
                AND a.apply_status = #{applyStatus}
            </if>
            AND a.product_id IN
            <foreach close=")" collection="productIds" item="productId" open="(" separator=", ">#{productId}</foreach>
        </where>
        GROUP BY a.id, a.product_id, a.apply_status, a.consume_cost, b.model
    </select>


    <select id="selectActiveByProductIds" resultMap="ProductCraftVO">
        SELECT
        a.*,
        b.model AS product_model,
        IFNULL( a.consume_cost + SUM( d.cur_price * c.quantity ), 0 ) AS guide_cost
        FROM
        product_craft a
        LEFT JOIN product b ON b.id = a.product_id
        LEFT JOIN product_craft_compose c ON c.craft_id = a.id
        AND c.delete_flag = 0
        LEFT JOIN accessory d ON d.id = c.accessory_id AND d.accessory_type != '耗材'
        WHERE a.delete_flag = 0 AND a.apply_status = 1 AND a.product_id IN
        <foreach close=")" collection="productIds" item="productId" open="(" separator=", ">#{productId}</foreach>
        GROUP BY a.id, b.model </select>

    <select id="judgeUsedCraftById" resultType="java.lang.Boolean">
        SELECT COUNT(*)
        FROM product_order a
                 LEFT JOIN product_object b ON b.order_id = a.id
        WHERE a.delete_flag = 0
          AND b.delete_flag = 0
          AND b.craft_id = #{id,jdbcType=BIGINT}
    </select>

    <select id="judgeUsedCraftByIds" resultMap="ProductCraft">
        SELECT a.*
        FROM product_craft a
        LEFT JOIN product_object b ON b.craft_id = a.id
        LEFT JOIN product_order c ON c.id = b.order_id
        WHERE
        a.delete_flag = 0
        AND b.delete_flag = 0
        AND c.delete_flag = 0
        AND b.id IS NOT NULL
        AND c.id IS NOT NULL
        AND b.craft_id IN
        <foreach collection="craftIds" item="id" separator=", " open="(" close=")">#{id}</foreach>
        GROUP BY
        a.id
    </select>

    <select id="hasActiveCraftByProductId" resultType="java.lang.Boolean">
        SELECT COUNT(*)
        FROM product_craft
        WHERE delete_flag = 0
          AND apply_status = 1
          AND product_id = #{productId}
    </select>

    <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="ProductCraft">
        select
        <include refid="Column_List_ProductCraft"/>
        from product_craft where id = #{id,jdbcType=BIGINT}
    </select>

    <select id="selectAllCraftCodeByProductId" resultType="java.lang.String">
        select craft_code
        from product_craft
        where delete_flag = 0
          AND product_id = #{productId}
    </select>

    <insert id="insert" parameterType="com.fawkes.project.example.common.model.ProductCraft">
        insert into product_craft (id, create_by, create_date,
                                   update_by, update_date, delete_flag,
                                   product_id, craft_code,
                                   consume_cost, apply_status, apply_start_date,
                                   apply_end_date, pic)
        values (#{id,jdbcType=BIGINT}, #{createBy,jdbcType=VARCHAR}, #{createDate,jdbcType=TIMESTAMP},
                #{updateBy,jdbcType=VARCHAR}, #{updateDate,jdbcType=TIMESTAMP}, #{deleteFlag,jdbcType=INTEGER},
                #{productId,jdbcType=BIGINT}, #{craftCode,jdbcType=VARCHAR},
                #{consumeCost,jdbcType=DECIMAL}, #{applyStatus,jdbcType=TINYINT}, #{applyStartDate,jdbcType=DATE},
                #{applyEndDate,jdbcType=DATE}, #{pic,jdbcType=LONGVARCHAR})
    </insert>

    <update id="disableBatchByProductId">
        UPDATE product_craft
        SET apply_status = 0,
            update_by    = #{updateInfo.updateBy},
            update_date  = #{updateInfo.updateDate}
        WHERE product_id = #{productId}
    </update>

    <update id="deleteById">
        UPDATE product_craft
        SET delete_flag = -1,
            update_by   = #{updateInfo.updateBy},
            update_date = #{updateInfo.updateDate}
        WHERE id = #{craftId}
    </update>

    <update id="deleteByProductIds">
        UPDATE product_craft
        SET delete_flag = -1,
        update_by = #{updateInfo.updateBy},
        update_date = #{updateInfo.updateDate}
        WHERE product_id IN
        <foreach collection="productIds" item="productId" open="(" separator=", " close=")">#{productId}</foreach>
    </update>

    <update id="updateByPrimaryKey" parameterType="com.fawkes.project.example.common.model.ProductCraft">
        update product_craft
        set update_by        = #{updateBy,jdbcType=VARCHAR},
            update_date      = #{updateDate,jdbcType=TIMESTAMP},
            product_id       = #{productId,jdbcType=BIGINT},
            craft_code       = #{craftCode,jdbcType=VARCHAR},
            consume_cost     = #{consumeCost,jdbcType=DECIMAL},
            apply_start_date = #{applyStartDate,jdbcType=DATE},
            apply_end_date   = #{applyEndDate,jdbcType=DATE},
            pic              = #{pic,jdbcType=LONGVARCHAR},
            apply_status= #{applyStatus,jdbcType=TINYINT}
        where id = #{id,jdbcType=BIGINT}
    </update>

    <update id="updateBatch" parameterType="com.fawkes.project.example.common.model.ProductCraft">
        <foreach collection="list" item="item" separator=";">
            update product_craft
            set create_by = #{item.createBy,jdbcType=VARCHAR},
            create_date = #{item.createDate,jdbcType=TIMESTAMP},
            update_by = #{item.updateBy,jdbcType=VARCHAR},
            update_date = #{item.updateDate,jdbcType=TIMESTAMP},
            delete_flag = #{item.deleteFlag,jdbcType=INTEGER},
            product_id = #{item.productId,jdbcType=BIGINT},
            craft_code = #{item.craftCode,jdbcType=VARCHAR},
            consume_cost = #{item.consumeCost,jdbcType=DECIMAL},
            apply_status = #{item.applyStatus,jdbcType=TINYINT},
            apply_start_date = #{item.applyStartDate,jdbcType=DATE},
            apply_end_date = #{item.applyEndDate,jdbcType=DATE}
            where id = #{item.id,jdbcType=BIGINT}
        </foreach>
    </update>
</mapper>