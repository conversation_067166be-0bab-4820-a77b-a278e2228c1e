package com.fawkes.project.example.common.model;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import com.fawkes.core.base.model.BaseEntity;
import com.fawkes.project.example.domain.param.ProductCraftQo;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;

import java.util.Date;
import java.util.List;

/**
 * 产品订单实体类
 *
 * <AUTHOR>
 */
@Data
@ToString
@NoArgsConstructor
public class ProductOrder extends BaseEntity {

    /**
     * 订单编号
     */
    private String orderNo;

    /**
     * 订单日期
     */
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date orderTime;

    /**
     * 生产年份
     */
    @JsonFormat(pattern = "yyyy", timezone = "GMT+8")
    private Integer orderYear;

    /**
     * 生产批次
     */
    private String orderBatch;

    /**
     * 生产责任人ID
     */
    @JsonSerialize(using = ToStringSerializer.class)
    private Long produceUserId;

    /**
     * 生产责任人名称
     */
    private String produceUserName;

    /**
     * 检验责任人ID
     */
    @JsonSerialize(using = ToStringSerializer.class)
    private Long checkUserId;

    /**
     * 检验责任人名称
     */
    private String checkUserName;

    /**
     * 预计检验完成时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date expectCheckFinishTime;

    /**
     * 实际检验完成时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date actualCheckFinishTime;

    /**
     * 累计生产量
     */
    private Integer totalProductQuantity;

    /**
     * 废品数量
     */
    private Long wasteNum;

    /**
     * 次品数量
     */
    private Long defectiveNum;

    /**
     * 合格品数量
     */
    private Long qualifiedNum;

    /**
     * 检验进度
     */
    private String checkProgress;

    /**
     * 检验状态: 0 - 未启动，1 - 检验中， 2 - 已完成
     */
    private Integer checkStatus;

    /**
     * 工艺及数量
     */
    private List<ProductCraftQo> crafts;

    /**
     * 配件损耗
     */
    private List<ProductOrderLoss> losses;

    @Override
    public String toString() {
        return super.toString() + " ProductOrder{" +
                "orderNo='" + orderNo + '\'' +
                ", orderTime=" + orderTime +
                ", orderYear=" + orderYear +
                ", orderBatch='" + orderBatch + '\'' +
                ", produceUserId=" + produceUserId +
                ", produceUserName='" + produceUserName + '\'' +
                ", checkUserId=" + checkUserId +
                ", checkUserName='" + checkUserName + '\'' +
                ", expectCheckFinishTime=" + expectCheckFinishTime +
                '}';
    }
}
