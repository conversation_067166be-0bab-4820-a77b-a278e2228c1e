package com.fawkes.project.example.common.mapper;

import com.fawkes.core.base.model.BaseEntity;
import com.fawkes.project.example.common.model.SaleOrder;
import com.fawkes.project.example.common.model.SaleOrderReturn;
import com.fawkes.project.example.domain.dto.SaleOrderDTO;
import com.fawkes.project.example.domain.param.SaleOrderPageParam;
import com.fawkes.project.example.domain.param.SaleReturnObj;
import com.fawkes.project.example.domain.vo.*;
import org.apache.ibatis.annotations.Param;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.Year;
import java.util.List;

public interface SaleOrderMapper {

    /**
     * 判断订单号是否已存在
     *
     * @param order
     * @return
     */
    Boolean isExistedOrderNo(@Param("order") SaleOrder order);

    /**
     * 根据年份获取最大订单号
     *
     * @param searchYear
     * @return
     */
    String getMaxOrderNoInYear(@Param("searchYear") String searchYear);

    /**
     * 根据id查询订单
     *
     * @param id
     * @return
     */
    SaleOrderDTO selectById(Long id);

    /**
     * 根据id集合查询订单
     *
     * @param orderIds
     * @return
     */
    List<SaleOrderVO> selectByIds(@Param("orderIds") List<Long> orderIds);

    /**
     * 根据客户id集合查询订单
     *
     * @param clientIds
     * @return
     */
    List<SaleOrderVO> selectByClientIds(@Param("clientIds") List<Long> clientIds);

    /**
     * 分页查询
     *
     * @param param
     * @return
     */
    List<SaleOrderVO> list(@Param("param") SaleOrderPageParam param);

    /**
     * 获取所有订单
     *
     * @return
     */
    List<SaleOrder> listAll();

    /**
     * 获取总销售额
     *
     * @return
     */
    BigDecimal getTotalSaleAmount();

    /**
     * 按照年份查询
     *
     * @param searchYear
     * @return
     */
    List<SaleOrderVO> listByYear(@Param("searchYear") Year searchYear);

    /**
     * 按照年份和省份查询
     *
     * @param searchYear
     * @param province
     * @return
     */
    List<SaleOrderVO> listByYearAndProvince(@Param("searchYear") Year searchYear, @Param("province") String province);

    /**
     * 查询订单下的工艺详情
     *
     * @param orderId
     * @param craftId
     * @return
     */
    SaleOutCraftDetailVO selectCraftDetail(@Param("orderId") Long orderId, @Param("craftId") Long craftId);

    /**
     * 查询工艺的可用库存
     *
     * @param craftId
     * @return
     */
    List<SaleProductObjVO> selectAvailableObj(@Param("craftId") Long craftId);

    /**
     * 查询已出库的产品列表
     *
     * @param orderId
     * @return
     */
    List<SaleProductObjVO> selectOutObjByOrderId(@Param("orderId") Long orderId);

    /**
     * 查询已出库的产品列表
     *
     * @param orderIds
     * @return
     */
    List<SaleProductObjVO> selectOutObjByOrderIds(@Param("orderIds") List<Long> orderIds);

    /**
     * 查询退换货的产品列表
     *
     * @param orderId
     * @return
     */
    List<SaleProductObjVO> selectReturnObjByOrderId(@Param("orderId") Long orderId);

    /**
     * 根据订单id查询退换货的产品列表
     *
     * @param orderIds
     * @return
     */
    List<SaleProductObjVO> selectReturnObjByOrderIds(@Param("orderIds") List<Long> orderIds);

    /**
     * 根据退换货id查询退换货的订单
     *
     * @param saleOrderReturnId
     * @return
     */
    SaleProductObjVO selectReturnObjBySaleOrderReturnId(@Param("saleOrderReturnId") Long saleOrderReturnId);

    /**
     * 根据id和订单id查询退换货的订单
     *
     * @param objId
     * @param orderId
     * @return
     */
    SaleOrderReturn selectReturnByObjIdAndOrderId(@Param("objId") Long objId, @Param("orderId") Long orderId);

    /**
     * 查询工艺信息（带产品信息）
     *
     * @param craftIds
     * @return
     */
    List<SaleGoodsVO> selectOutGoodsByCraftIds(@Param("craftIds") List<Long> craftIds);

    /**
     * 新增销售订单
     *
     * @param record
     * @return
     */
    int insert(SaleOrder record);

    /**
     * 批量退换货
     *
     * @param returnList
     * @return
     */
    int returnBatch(@Param("returnList") List<SaleOrderReturn> returnList);

    /**
     * 批量出库
     *
     * @param saleOrderId 销售订单id
     * @param outDate     出库时间
     * @param outUserId   出库员id
     * @param outUserName 出库员姓名
     * @param outRemark   出库备注
     * @param outPic      出库图片
     * @param objIds      待出库的实体id
     * @return
     */
    int outBatch(@Param("saleOrderId") Long saleOrderId, @Param("outDate") LocalDate outDate, @Param("outUserId") Long outUserId, @Param("outUserName") String outUserName, @Param("outRemark") String outRemark, @Param("outPic") String outPic, @Param("objIds") List<Long> objIds);

    /**
     * 绑定产品对象 销售订单
     *
     * @param objIds
     * @param saleOrderId
     * @param updateInfo
     * @return
     */
    int updateSaleOrderIdByIds(@Param("objIds") List<Long> objIds, @Param("saleOrderId") Long saleOrderId, @Param("updateInfo") BaseEntity updateInfo);

    /**
     * 更新产品的检验状态
     *
     * @param objList
     * @return
     */
    int updateBatchUnqualifiedObj(@Param("objList") List<SaleReturnObj> objList);

    /**
     * 删除退换货记录
     *
     * @param id
     * @param updateInfo
     * @return
     */
    int deleteReturn(@Param("id") Long id, @Param("updateInfo") BaseEntity updateInfo);

    /**
     * 更新销售订单
     *
     * @param record
     * @return
     */
    int updateByPrimaryKeyWithBLOBs(SaleOrder record);

    /**
     * 批量删除
     *
     * @param ids
     * @param updateInfo
     * @return
     */
    int deleteByIds(@Param("ids") List<Long> ids, @Param("updateInfo") BaseEntity updateInfo);

    /**
     * 清空产品对象的出库信息
     *
     * @param objIds
     * @return
     */
    int cleanOutByObjIds(@Param("objIds") List<Long> objIds);

    /**
     * 清空销售订单的出库信息
     *
     * @param saleOrderIds
     * @return
     */
    int cleanOutBySaleOrderIds(@Param("saleOrderIds") List<Long> saleOrderIds);

    /**
     * 更新合同状态
     *
     * @param id
     * @param status
     * @param actualDeliveryDate
     * @return
     */
    int updateStatus(@Param("id") Long id, @Param("status") String status, @Param("actualDeliveryDate") LocalDate actualDeliveryDate);

    /**
     * 查询产品对象
     *
     * @param objId
     * @return
     */
    SaleProductObjVO selectProductObjectById(@Param("objId") Long objId);

    /**
     * 更新产品信息
     *
     * @param objVO
     * @return
     */
    int updateProductObjByPrimaryKey(@Param("obj") SaleProductObjVO objVO);

    /**
     * 查询销售订单下的所有产品对象
     *
     * @param saleOrderIds
     * @return
     */
    List<SaleProductObjVO> listProductObjectBySaleOrderIds(@Param("saleOrderIds") List<Long> saleOrderIds);

    /**
     * 逐月统计收款量、销售额
     *
     * @param year
     * @return
     */
    List<StatSaleOrderVO> statSaleOrderByYear(@Param("searYear") Year year);

    /**
     * 按签订年份查询
     *
     * @param signYear
     * @return
     */
    List<SaleOrder> selectBySignDate(@Param("signYear") String signYear);
}