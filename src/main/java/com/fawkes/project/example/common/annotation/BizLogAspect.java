package com.fawkes.project.example.common.annotation;

import com.fawkes.core.utils.EntityTool;
import com.fawkes.core.utils.http.HttpHeaderTool;
import com.fawkes.project.example.common.model.BizLog;
import com.fawkes.project.example.service.impl.BizLogService;
import lombok.extern.slf4j.Slf4j;
import org.aspectj.lang.JoinPoint;
import org.aspectj.lang.annotation.AfterReturning;
import org.aspectj.lang.annotation.Aspect;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 */
@Aspect
@Component
@Slf4j
public class BizLogAspect {
    @Resource
    BizLogService bizLogService;

    @AfterReturning(pointcut = "@annotation(controllerLog)", returning = "jsonResult")
    public void doAfterReturning(JoinPoint joinPoint, BizLogAnnotation controllerLog, Object jsonResult) {
        handleLog(joinPoint, controllerLog, null, jsonResult);
    }

    protected void handleLog(final JoinPoint joinPoint, BizLogAnnotation controllerLog, final Exception e, Object jsonResult) {
        try {
            // 日志记录
            BizLog bizLog = new BizLog();
            bizLog.setModule(controllerLog.module());
            bizLog.setContent(controllerLog.content());
            bizLog.setOperateIP(HttpHeaderTool.getHeaderValue(HttpHeaderTool.HeaderEnum.IP_ADDRESS));
            EntityTool.insertEntity(bizLog);
            bizLogService.save(bizLog);
        } catch (Exception exp) {
            log.error("异常信息:{}", exp.getMessage());
            exp.printStackTrace();
        }
    }
}
