<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.fawkes.project.example.common.mapper.ProductCraftComposeMapper">
    <resultMap id="ProductCraftCompose" type="com.fawkes.project.example.common.model.ProductCraftCompose"
               extends="com.fawkes.project.example.common.mapper.BaseMapper.BaseEntity">
        <result column="craft_id" jdbcType="BIGINT" property="craftId"/>
        <result column="accessory_id" jdbcType="BIGINT" property="accessoryId"/>
        <result column="quantity" jdbcType="INTEGER" property="quantity"/>
    </resultMap>
    <resultMap id="ProductCraftComposeVO" type="com.fawkes.project.example.domain.vo.ProductCraftComposeVO"
               extends="ProductCraftCompose">
        <result column="accessory_code" jdbcType="VARCHAR" property="accessoryCode"/>
        <result column="accessory_name" jdbcType="VARCHAR" property="accessoryName"/>
        <result column="cur_price" jdbcType="VARCHAR" property="curPrice"/>
    </resultMap>

    <select id="selectAllByCraftId" resultMap="ProductCraftComposeVO">
        SELECT a.id,
               a.create_by,
               a.create_date,
               a.update_by,
               a.update_date,
               a.delete_flag,
               a.craft_id,
               a.accessory_id,
               a.quantity,
               CONCAT(c.dict_name, '-', d.dict_name) as accessory_name,
               CONCAT(c.dict_code, '-', d.dict_code) as accessory_code,
               b.cur_price
        FROM product_craft_compose a
                 LEFT JOIN accessory b on a.accessory_id = b.id and a.delete_flag != -1
         LEFT JOIN accessory_dict c
        ON b.category_id = c.id
            LEFT JOIN accessory_dict d ON b.ref_id = d.id
        where a.delete_flag = 0 AND a.craft_id = #{craftId}
    </select>

    <insert id="insertBatch" parameterType="com.fawkes.project.example.common.model.ProductCraftCompose">
        insert into product_craft_compose (id, create_by, create_date,update_by, update_date, delete_flag,
        craft_id, accessory_id, quantity)
        VALUES
        <foreach collection="list" item="item" separator=",">
            (#{item.id,jdbcType=BIGINT}, #{item.createBy,jdbcType=VARCHAR},
            #{item.createDate,jdbcType=TIMESTAMP},#{item.updateBy,jdbcType=VARCHAR},
            #{item.updateDate,jdbcType=TIMESTAMP}, #{item.deleteFlag,jdbcType=INTEGER},
            #{item.craftId,jdbcType=BIGINT}, #{item.accessoryId,jdbcType=BIGINT}, #{item.quantity,jdbcType=INTEGER}
            )
        </foreach>
    </insert>

    <update id="updateBatch" parameterType="com.fawkes.project.example.common.model.ProductCraftCompose">
        <foreach collection="list" item="item" separator=";">
            update product_craft_compose
            set update_by = #{item.updateBy,jdbcType=VARCHAR},
            update_date = #{item.updateDate,jdbcType=TIMESTAMP},
            delete_flag = #{item.deleteFlag,jdbcType=INTEGER},
            craft_id = #{item.craftId,jdbcType=BIGINT},
            accessory_id = #{item.accessoryId,jdbcType=BIGINT},
            quantity = #{item.quantity,jdbcType=INTEGER}
            where id = #{item.id,jdbcType=BIGINT}
        </foreach>
    </update>
</mapper>