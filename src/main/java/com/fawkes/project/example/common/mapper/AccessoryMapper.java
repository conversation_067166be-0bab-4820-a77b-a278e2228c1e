package com.fawkes.project.example.common.mapper;

import com.fawkes.core.base.model.BaseEntity;
import com.fawkes.project.example.common.model.Accessory;
import com.fawkes.project.example.domain.dto.AccessoryPriceDTO;
import com.fawkes.project.example.domain.vo.AccessoryInventoryVO;
import com.fawkes.project.example.domain.vo.AccessoryVO;
import com.fawkes.project.example.domain.vo.StatAccessoryOutVO;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface AccessoryMapper {

    /**
     * 校验配件是否存在 零件种类、零件编号
     *
     * @param accessory
     * @return
     */
    Boolean isExistedAccessory(@Param("accessory") Accessory accessory);

    /**
     * 校验配件是否在配件入库中使用
     *
     * @param accessoryId
     * @return
     */
    Boolean isUsedInAccessoryOrderById(Long accessoryId);

    /**
     * 校验配件是否在工艺中使用
     *
     * @param accessoryId
     * @return
     */
    Boolean isUsedInCraftById(Long accessoryId);

    /**
     * 根据id查询配件
     *
     * @param id
     * @return
     */
    Accessory selectByPrimaryKey(Long id);

    /**
     * 模糊查询
     *
     * @param accessoryCode
     * @param accessoryName
     * @param accessoryType
     * @return
     */
    List<AccessoryVO> selectAccessoryList(String accessoryCode, String accessoryName, String accessoryType);

    /**
     * 查询配件库存
     *
     * @param accessoryCode
     * @param accessoryName
     * @param accessoryType
     * @param accessorySupplier
     * @return
     */
    List<AccessoryInventoryVO> selectAccessoryInventoryList(String accessoryCode, String accessoryName, String accessoryType, String accessorySupplier);

    /**
     * 获取配件使用列表
     *
     * @return
     */
    List<AccessoryInventoryVO> getAccessoryUseList();

    /**
     * 获取配件损耗列表
     *
     * @return
     */
    List<AccessoryInventoryVO> getAccessoryLossList();

    /**
     * 统计配件出库列表
     *
     * @param accessoryId
     * @return
     */
    List<StatAccessoryOutVO> statAccessoryOutUseByAccessoryId(@Param("accessoryId") Long accessoryId);

    /**
     * 新增
     *
     * @param record
     * @return
     */
    int insert(Accessory record);

    /**
     * 批量新增
     *
     * @param list
     * @return
     * @deprecated 测试用
     */
    int insertBatch(List<Accessory> list);

    /**
     * 根据id删除
     *
     * @param id
     * @param updateInfo
     * @return
     */
    int deleteById(@Param("id") Long id, @Param("updateInfo") BaseEntity updateInfo);

    /**
     * 修改
     *
     * @param record
     * @return
     */
    int updateByPrimaryKey(Accessory record);

    /**
     * 批量更新配件单价
     *
     * @param accessoryPriceDTOList
     * @return
     */
    int batchUpdateCurPrice(@Param("list") List<AccessoryPriceDTO> accessoryPriceDTOList);
}