package com.fawkes.project.example.common.model;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import com.fawkes.core.base.model.BaseEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDate;

@Data
@ToString
@NoArgsConstructor
public class AccessoryOrder extends BaseEntity implements Serializable {
    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "日期+PJCG+序列号（20240221PJCG001）")
    private String orderNo;

    @ApiModelProperty(value = "订单名称")
    private String orderName;

    @ApiModelProperty(value = "订单日期")
    private LocalDate orderDate;

    @ApiModelProperty(value = "交付日期")
    private LocalDate orderDeliveryDate;

    @ApiModelProperty(value = "责任人id")
    @JsonSerialize(using = ToStringSerializer.class)
    private Long orderPersonLiableId;

    @ApiModelProperty(value = "责任人名称")
    private String orderPersonLiableName;

    @ApiModelProperty(value = "合同附件")
    private String orderEnclosure;

    @ApiModelProperty(value = "累计已支付金额")
    private BigDecimal orderTotalPaid;

    @ApiModelProperty(value = "订单图片")
    private String orderPic;
}