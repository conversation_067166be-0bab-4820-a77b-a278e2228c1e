package com.fawkes.project.example.common.mapper;

import com.fawkes.project.example.common.model.SaleGoodsAlter;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface SaleGoodsAlterMapper {

    /**
     * 根据订单id查询变更记录
     *
     * @param orderId
     * @return
     */
    List<SaleGoodsAlter> selectByOrderId(@Param("orderId") Long orderId);

    /**
     * 插入变更记录
     *
     * @param record
     * @return
     */
    int insert(SaleGoodsAlter record);
}