package com.fawkes.project.example.common.model;

import com.fawkes.core.base.model.BaseEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;

import java.io.Serializable;

@Data
@ToString
@NoArgsConstructor
public class Product extends BaseEntity implements Serializable {
    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "产品名称")
    private String name;

    @ApiModelProperty(value = "产品型号")
    private String model;

    @ApiModelProperty(value = "单位")
    private String unit;

    @ApiModelProperty(value = "产品种类代码（两位）")
    private String categoryCode;

    @ApiModelProperty(value = "应用现状：false-弃用，true-启用")
    private Boolean applyStatus;

    @ApiModelProperty(value = "应用行业")
    private String industry;

    @ApiModelProperty(value = "备注")
    private String remark;

    @ApiModelProperty(value = "合格证类型: 传感器（默认），,数据采集仪")
    private String certType;
}