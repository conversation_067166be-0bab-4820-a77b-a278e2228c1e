<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.fawkes.project.example.common.mapper.BizLogMapper">
    <resultMap id="BizLog" type="com.fawkes.project.example.common.model.BizLog"
               extends="com.fawkes.project.example.common.mapper.BaseMapper.BaseEntity">
        <id column="id" jdbcType="BIGINT" property="id"/>
        <result column="module" jdbcType="VARCHAR" property="module"/>
        <result column="content" jdbcType="VARCHAR" property="content"/>
        <result column="ip" jdbcType="VARCHAR" property="operateIP"/>
    </resultMap>

    <select id="list" resultMap="BizLog">
        select *
        from biz_log
        where delete_flag = 0
        order by create_date desc
    </select>

    <insert id="insertBatch" parameterType="com.fawkes.project.example.common.model.BizLog">
        insert into biz_log (id, create_by, create_date, update_by, update_date, delete_flag, `module`, content, ip)
        values
        <foreach collection="bizLogList" item="log" separator=",">
            (#{log.id}, #{log.createBy}, #{log.createDate}, #{log.updateBy}, #{log.updateDate}, #{log.deleteFlag},
            #{log.module}, #{log.content}, #{log.operateIP})
        </foreach>
    </insert>
</mapper>