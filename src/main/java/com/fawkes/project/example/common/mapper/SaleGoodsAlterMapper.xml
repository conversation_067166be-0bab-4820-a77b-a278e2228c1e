<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.fawkes.project.example.common.mapper.SaleGoodsAlterMapper">
    <resultMap id="SaleGoodsAlter" type="com.fawkes.project.example.common.model.SaleGoodsAlter"
               extends="com.fawkes.project.example.common.mapper.BaseMapper.BaseEntity">
        <result column="order_id" jdbcType="BIGINT" property="orderId"/>
        <result column="alter_reason" jdbcType="VARCHAR" property="alterReason"/>
    </resultMap>

    <select id="selectByOrderId" resultMap="SaleGoodsAlter">
        select *
        from sale_goods_alter
        where delete_flag = 0
          AND order_id = #{orderId}
        order by create_date desc
    </select>

    <insert id="insert" parameterType="com.fawkes.project.example.common.model.SaleGoodsAlter">
        insert into sale_goods_alter (id, create_by, create_date,
                                      update_by, update_date, delete_flag,
                                      order_id, alter_reason)
        values (#{id,jdbcType=BIGINT}, #{createBy,jdbcType=VARCHAR}, #{createDate,jdbcType=TIMESTAMP},
                #{updateBy,jdbcType=VARCHAR}, #{updateDate,jdbcType=TIMESTAMP}, #{deleteFlag,jdbcType=INTEGER},
                #{orderId,jdbcType=BIGINT}, #{alterReason,jdbcType=VARCHAR})
    </insert>
</mapper>