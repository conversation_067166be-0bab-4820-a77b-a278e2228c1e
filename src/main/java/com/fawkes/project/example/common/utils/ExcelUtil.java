package com.fawkes.project.example.common.utils;

import lombok.extern.slf4j.Slf4j;
import org.apache.poi.hssf.usermodel.HSSFWorkbook;
import org.apache.poi.ss.usermodel.*;
import org.apache.poi.ss.util.CellRangeAddress;
import org.apache.poi.ss.util.CellRangeAddressList;
import org.apache.poi.xssf.usermodel.*;

import javax.servlet.ServletOutputStream;
import javax.servlet.http.HttpServletResponse;
import java.io.*;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.util.*;
import java.util.concurrent.atomic.AtomicInteger;

/**
 * Excel工具类
 *
 * <AUTHOR>
 */
@Slf4j
public class ExcelUtil {

    /**
     * 读取本地Excel(跳过没有数据的sheet)
     */
    public static Map<String, List<Map<String, String>>> readLocalExcel(String filepath) throws Exception {
        Map<String, List<Map<String, String>>> res = new HashMap<>();
        InputStream is = new FileInputStream(new File(filepath));

        boolean isExcel2003 = filepath.toLowerCase().endsWith("xls");
        Workbook wb;
        if (isExcel2003) {
            wb = new HSSFWorkbook(is);
        } else {
            wb = new XSSFWorkbook(is);
        }
        Iterator<Sheet> sheetIt = wb.sheetIterator();
        while (sheetIt.hasNext()) {
            Sheet sheet = sheetIt.next();
            List<Map<String, String>> list = new ArrayList<>();
            int lastRowNum = sheet.getLastRowNum();
            for (int i = 1; i <= lastRowNum; i++) {
                if (i % 1000 == 0) {
                    log.info("读取第{}/{}行", i, lastRowNum);
                }
                Row row = sheet.getRow(i);
                if (row == null) {
                    continue;
                }
                Map<String, String> map = new HashMap<>();
                for (int j = 0; j < row.getLastCellNum(); j++) {
                    Cell cell = row.getCell(j);
                    if (cell == null) {
                        continue;
                    }
                    CellType cellType = cell.getCellType();
                    String cellValue = "";
                    switch (cellType) {
                        case STRING:
                            cellValue = cell.getStringCellValue();
                            break;
                        case BLANK:
                            break;
                        case NUMERIC:
                            if (DateUtil.isCellDateFormatted(cell)) {
                                cellValue = TimeUtil.format2Second(cell.getDateCellValue());
                            } else {
                                //不是日期格式，防止时间过长！
                                cell.setCellType(CellType.NUMERIC);
                                cellValue = String.valueOf(cell.getNumericCellValue());
                            }
                            break;
                        case ERROR:
                            log.error("数据类型错误");
                            break;
                    }
                    map.put(String.valueOf(j + 1), cellValue);
                }
                if (map.size() != 0) {
                    list.add(map);
                }
            }
            if (list.size() != 0) {
                res.put(sheet.getSheetName(), list);
            }
        }
        return res;
    }


    private static void delRows(Sheet sheet, List<Integer> arr) {
        System.out.println("arr:" + arr);
        int count = 0;
        int rowNums = sheet.getLastRowNum();
        for (int i : arr) {
            System.out.println("正在删除第" + i + "个");
            sheet.shiftRows(i + 1 - count, rowNums, -1);
            count++;
        }
    }

    /**
     * 在excel准备数据时，向浏览器输出字节流
     *
     * @param response
     * @param fileName
     * @throws Exception
     */
    public static void exportByte(HttpServletResponse response, String fileName) throws Exception {
        String hex = "50 4B 03 04 14 00 08 08 08 00";
        String[] sArr = hex.split(" ");
        Object[] arr = new Object[sArr.length];
        for (int i = 0; i < sArr.length; i++) {
            arr[i] = hexToByteArray(sArr[i]);
        }

        response.setContentType("application/octet-stream");
        response.setHeader("Content-Disposition", "attachment;fileName=" + URLEncoder.encode(fileName, "UTF-8") + ".xlsx");
        response.addHeader("Content-Length", "-1");

        OutputStream os = response.getOutputStream();

        for (int i = 0; i < arr.length; i++) {
            os.write((byte[]) arr[i]);
            os.flush();
        }
    }


    public static byte[] hexToByteArray(String inHex) {
        int hexlen = inHex.length();
        byte[] result;
        if (hexlen % 2 == 1) {
            //奇数
            hexlen++;
            result = new byte[(hexlen / 2)];
            inHex = "0" + inHex;
        } else {
            //偶数
            result = new byte[(hexlen / 2)];
        }
        int j = 0;
        for (int i = 0; i < hexlen; i += 2) {
            result[j] = hexToByte(inHex.substring(i, i + 2));
            j++;
        }
        return result;
    }

    public static byte hexToByte(String inHex) {
        return (byte) Integer.parseInt(inHex, 16);
    }

    /**
     * 导出普通Excel
     */
    public static void exportNormalExcel(String name, String[] titles, String[] keys, Map<String, List<Map<String, Object>>> sheetMaps,
                                         HttpServletResponse response, boolean needRemoveBytes, String dateFormat, boolean needDesc) throws IOException {
        /* 获取输出流 */
        ServletOutputStream os = response.getOutputStream();
        response.setHeader("Content-disposition", "attachment; filename=" + new String((name + ".xlsx").getBytes(StandardCharsets.UTF_8), StandardCharsets.ISO_8859_1));
        response.setContentType("application/x-download");

        /* 创建sheet */
        XSSFWorkbook wb = new XSSFWorkbook();

        /* 创建内容、表头样式 */
        CellStyle descStyle = createBoldBigBackgroundCellStyle(wb);
        CellStyle titleStyle = createBoldCellStyle(wb);
        CellStyle contentStyle = createNormalCellStyle(wb);

        /* 写入多Sheet */
        sheetMaps.forEach((sheetName, valueMaps) -> {
            XSSFSheet sheet = wb.createSheet(sheetName.replace("/", ""));

            int start = 0;
            if (needDesc) {
                start = 1;
                /* 写描述行（合并前2行） */
                for (int i = 0; i < 2; i++) {
                    XSSFRow row = sheet.createRow((short) i);
                    for (int j = 0; j < titles.length; j++) {
                        XSSFCell cell = row.createCell((short) j);
                        cell.setCellStyle(descStyle);
                        if (i == 0 && j == 0) {
                            cell.setCellValue(new XSSFRichTextString(name));
                        }
                    }
                }
                sheet.addMergedRegion(new CellRangeAddress(0, 0, 0, titles.length - 1));
            }

            /* 写表头行 */
            XSSFRow row = sheet.createRow((short) start);
            for (int i = 0; i < titles.length; i++) {
                XSSFCell cell = row.createCell((short) i);
                cell.setCellStyle(titleStyle);
                cell.setCellValue(new XSSFRichTextString(titles[i]));
            }

            /* 写内容行 */
            for (int i = 0; i < valueMaps.size(); i++) {
                row = sheet.createRow((short) (i + start + 1));
                Map<String, Object> map = valueMaps.get(i);
                for (int j = 0; j < keys.length; j++) {
                    XSSFCell cell = row.createCell((short) (j));
                    cell.setCellStyle(contentStyle);
                    Object value = map.get(keys[j]);
                    if (value == null) {
                        cell.setCellValue(new XSSFRichTextString(""));
                    } else {
                        XSSFDataFormat df = wb.createDataFormat();
                        if (value instanceof Date) {
                            /* 日期类型 */
                            CellStyle dateStyle = wb.createCellStyle();
                            dateStyle.cloneStyleFrom(contentStyle);
                            DataFormat format = wb.createDataFormat();
                            dateStyle.setDataFormat(format.getFormat(dateFormat));
                            cell.setCellValue((Date) value);
                            cell.setCellStyle(dateStyle);
                        } else if (value instanceof Integer) {
                            /* 整数类型 */
                            CellStyle numStyle = wb.createCellStyle();
                            numStyle.cloneStyleFrom(contentStyle);
                            numStyle.setDataFormat(df.getFormat("#0"));
                            cell.setCellValue((Integer) value);
                            cell.setCellStyle(numStyle);
                        } else if (value instanceof Float || value instanceof Double) {
                            /* 小数类型 */
                            CellStyle floatStyle = wb.createCellStyle();
                            floatStyle.cloneStyleFrom(contentStyle);
                            floatStyle.setDataFormat(df.getFormat("#0.00"));
                            if (value instanceof Float) {
                                cell.setCellValue((Float) value);
                            } else {
                                cell.setCellValue((Double) value);
                            }
                            cell.setCellStyle(floatStyle);
                        } else {
                            cell.setCellValue(new XSSFRichTextString(value.toString()));
                        }
                    }
                }
            }

            /* 调整列宽自适应 */
            for (int i = 0; i < titles.length; i++) {
                sheet.autoSizeColumn(i);
//                sheet.setColumnWidth(i, sheet.getColumnWidth(i) + 1500);
                // 对太宽的列做限制
                if (sheet.getColumnWidth(i) > 6000) {
                    sheet.setColumnWidth(i, 6000);
                }
                if ("时间".equals(titles[i])) {
                    // 日期列设置24个字符
                    sheet.setColumnWidth(i, 24 * 256);
                }
            }
        });


        /* 输出到客户端 */
        if (needRemoveBytes) {
            System.out.println("去掉了10字节");
            try {
                ByteArrayOutputStream bos = new ByteArrayOutputStream();
                wb.write(bos);
                byte[] srcBytes = bos.toByteArray();
                byte[] resBytes = new byte[srcBytes.length - 10];
                System.arraycopy(srcBytes, 10, resBytes, 0, resBytes.length);
                os.write(resBytes);
                os.flush();
            } catch (IOException e) {
                e.printStackTrace();
            } finally {
                os.close();
            }
        } else {
            System.out.println("else");
            wb.write(os);
            os.flush();
            os.close();
        }
    }

    /**
     * 导出带有下拉列表的Excel
     */
    public static void exportFanChoiceExcel(String name, String[] titles, String[] keys, Map<String, List<Map<String, Object>>> sheetMaps,
                                            HttpServletResponse response, boolean needRemoveBytes, String dateFormat, boolean needDesc, String[] choices, int colNum) throws IOException {
        /* 获取输出流 */
        ServletOutputStream os = response.getOutputStream();
        response.setHeader("Content-disposition", "attachment; filename=" + new String((name + ".xlsx").getBytes(StandardCharsets.UTF_8), StandardCharsets.ISO_8859_1));
        response.setContentType("application/x-download");

        /* 创建sheet */
        XSSFWorkbook wb = new XSSFWorkbook();

        /* 创建内容、表头样式 */
        CellStyle descStyle = createBoldBigBackgroundCellStyle(wb);
        CellStyle titleStyle = createBoldCellStyle(wb);
        CellStyle contentStyle = createNormalCellStyle(wb);

        /* 写入多Sheet */
        sheetMaps.forEach((sheetName, valueMaps) -> {
            XSSFSheet sheet = wb.createSheet(sheetName.replace("/", ""));

            int start = 0;
            if (needDesc) {
                start = 1;
                /* 写描述行（合并前2行） */
                for (int i = 0; i < 2; i++) {
                    XSSFRow row = sheet.createRow((short) i);
                    for (int j = 0; j < titles.length; j++) {
                        XSSFCell cell = row.createCell((short) j);
                        cell.setCellStyle(descStyle);
                        if (i == 0 && j == 0) {
                            cell.setCellValue(new XSSFRichTextString(name));
                        }
                    }
                }
                sheet.addMergedRegion(new CellRangeAddress(0, 0, 0, titles.length - 1));
            }

            /* 写表头行 */
            XSSFRow row = sheet.createRow((short) start);
            for (int i = 0; i < titles.length; i++) {
                XSSFCell cell = row.createCell((short) i);
                cell.setCellStyle(titleStyle);
                cell.setCellValue(new XSSFRichTextString(titles[i]));
            }

            /* 写内容行 */
            for (int i = 0; i < valueMaps.size(); i++) {
                row = sheet.createRow((short) (i + start + 1));
                Map<String, Object> map = valueMaps.get(i);
                for (int j = 0; j < keys.length; j++) {
                    XSSFCell cell = row.createCell((short) (j));
                    cell.setCellStyle(contentStyle);
                    Object value = map.get(keys[j]);
                    if (value == null) {
                        cell.setCellValue(new XSSFRichTextString(""));
                    } else {
                        XSSFDataFormat df = wb.createDataFormat();
                        if (value instanceof Date) {
                            /* 日期类型 */
                            CellStyle dateStyle = wb.createCellStyle();
                            dateStyle.cloneStyleFrom(contentStyle);
                            DataFormat format = wb.createDataFormat();
                            dateStyle.setDataFormat(format.getFormat(dateFormat));
                            cell.setCellValue((Date) value);
                            cell.setCellStyle(dateStyle);
                        } else if (value instanceof Integer) {
                            /* 整数类型 */
                            CellStyle numStyle = wb.createCellStyle();
                            numStyle.cloneStyleFrom(contentStyle);
                            numStyle.setDataFormat(df.getFormat("#0"));
                            cell.setCellValue((Integer) value);
                            cell.setCellStyle(numStyle);
                        } else if (value instanceof Float || value instanceof Double) {
                            /* 小数类型 */
                            CellStyle floatStyle = wb.createCellStyle();
                            floatStyle.cloneStyleFrom(contentStyle);
                            floatStyle.setDataFormat(df.getFormat("#0.00"));
                            if (value instanceof Float) {
                                cell.setCellValue((Float) value);
                            } else {
                                cell.setCellValue((Double) value);
                            }
                            cell.setCellStyle(floatStyle);
                        } else {
                            cell.setCellValue(new XSSFRichTextString(value.toString()));
                        }
                    }
                }
            }


            // 新建一个sheet页
            String hiddenSheetName = "hiddenSheet";
            XSSFSheet hiddenSheet = wb.createSheet(hiddenSheetName);

            // 写入下拉数据到新的sheet页中
            for (int i = 0; i < choices.length; i++) {
                Cell cell = hiddenSheet.createRow(i).createCell(0);
                cell.setCellValue(choices[i]);
            }

            // 获取新sheet页内容
            String strFormula = hiddenSheetName + "!$A$1:$A$65535";
            XSSFDataValidationConstraint constraint0 = new XSSFDataValidationConstraint(DataValidationConstraint.ValidationType.LIST, strFormula);
            CellRangeAddressList regions = new CellRangeAddressList(0, 200, colNum, colNum);
            // 数据有效性对象
            DataValidationHelper help = new XSSFDataValidationHelper(sheet);
            DataValidation validation0 = help.createValidation(constraint0, regions);
            validation0.setShowErrorBox(true);
            sheet.addValidationData(validation0);

            // 将新建的sheet页隐藏掉
            wb.setSheetHidden(1, true);

            /* 添加下拉项 */
            DataValidationHelper helper = sheet.getDataValidationHelper();

            DataValidationConstraint constraint2 = helper.createExplicitListConstraint(new String[]{"高桩承台基础", "单桩基础", "四桩导管架基础", "吸力式导管架基础", "复合筒基础", "漂浮式基础"});
            CellRangeAddressList addressList = new CellRangeAddressList(0, 200, 6, 6);
            DataValidation validation = helper.createValidation(constraint2, addressList);
            // 输入非法数据时，弹窗警告框
            validation.setShowErrorBox(true);
            sheet.addValidationData(validation);


            DataValidationConstraint constraint3 = helper.createExplicitListConstraint(new String[]{"未建", "已建"});
            addressList = new CellRangeAddressList(0, 200, 12, 12);
            validation = helper.createValidation(constraint3, addressList);
            // 输入非法数据时，弹窗警告框
            validation.setShowErrorBox(true);
            sheet.addValidationData(validation);

            /* 调整列宽自适应 */
            for (int i = 0; i < titles.length; i++) {
                sheet.autoSizeColumn(i);
                if (sheet.getColumnWidth(i) < 3000) {
                    sheet.setColumnWidth(i, 3000);
                }
                // 对太宽的列做限制
                if (sheet.getColumnWidth(i) > 8000) {
                    sheet.setColumnWidth(i, 8000);
                }
                if ("时间".equals(titles[i])) {
                    // 日期列设置24个字符
                    sheet.setColumnWidth(i, 24 * 256);
                }
            }
        });


        /* 输出到客户端 */
        if (needRemoveBytes) {
            System.out.println("去掉了10字节");
            try {
                ByteArrayOutputStream bos = new ByteArrayOutputStream();
                wb.write(bos);
                byte[] srcBytes = bos.toByteArray();
                byte[] resBytes = new byte[srcBytes.length - 10];
                System.arraycopy(srcBytes, 10, resBytes, 0, resBytes.length);
                os.write(resBytes);
                os.flush();
            } catch (IOException e) {
                e.printStackTrace();
            } finally {
                os.close();
            }
        } else {
            wb.write(os);
            os.flush();
            os.close();
        }
    }


    /**
     * 导出带有下拉列表的Excel
     */
    public static void exportChoiceExcel(String name, String[] titles, String[] keys, Map<String, List<Map<String, Object>>> sheetMaps,
                                         HttpServletResponse response, boolean needRemoveBytes, String dateFormat, boolean needDesc, String[] choices, int colNum) throws IOException {
        /* 获取输出流 */
        ServletOutputStream os = response.getOutputStream();
        response.setHeader("Content-disposition", "attachment; filename=" + new String((name + ".xlsx").getBytes(StandardCharsets.UTF_8), StandardCharsets.ISO_8859_1));
        response.setContentType("application/x-download");

        /* 创建sheet */
        XSSFWorkbook wb = new XSSFWorkbook();

        /* 创建内容、表头样式 */
        CellStyle descStyle = createBoldBigBackgroundCellStyle(wb);
        CellStyle titleStyle = createBoldCellStyle(wb);
        CellStyle contentStyle = createNormalCellStyle(wb);

        /* 写入多Sheet */
        sheetMaps.forEach((sheetName, valueMaps) -> {
            XSSFSheet sheet = wb.createSheet(sheetName.replace("/", ""));

            int start = 0;
            if (needDesc) {
                start = 1;
                /* 写描述行（合并前2行） */
                for (int i = 0; i < 2; i++) {
                    XSSFRow row = sheet.createRow((short) i);
                    for (int j = 0; j < titles.length; j++) {
                        XSSFCell cell = row.createCell((short) j);
                        cell.setCellStyle(descStyle);
                        if (i == 0 && j == 0) {
                            cell.setCellValue(new XSSFRichTextString(name));
                        }
                    }
                }
                sheet.addMergedRegion(new CellRangeAddress(0, 0, 0, titles.length - 1));
            }

            /* 写表头行 */
            XSSFRow row = sheet.createRow((short) start);
            for (int i = 0; i < titles.length; i++) {
                XSSFCell cell = row.createCell((short) i);
                cell.setCellStyle(titleStyle);
                cell.setCellValue(new XSSFRichTextString(titles[i]));
            }

            /* 写内容行 */
            for (int i = 0; i < valueMaps.size(); i++) {
                row = sheet.createRow((short) (i + start + 1));
                Map<String, Object> map = valueMaps.get(i);
                for (int j = 0; j < keys.length; j++) {
                    XSSFCell cell = row.createCell((short) (j));
                    cell.setCellStyle(contentStyle);
                    Object value = map.get(keys[j]);
                    if (value == null) {
                        cell.setCellValue(new XSSFRichTextString(""));
                    } else {
                        XSSFDataFormat df = wb.createDataFormat();
                        if (value instanceof Date) {
                            /* 日期类型 */
                            CellStyle dateStyle = wb.createCellStyle();
                            dateStyle.cloneStyleFrom(contentStyle);
                            DataFormat format = wb.createDataFormat();
                            dateStyle.setDataFormat(format.getFormat(dateFormat));
                            cell.setCellValue((Date) value);
                            cell.setCellStyle(dateStyle);
                        } else if (value instanceof Integer) {
                            /* 整数类型 */
                            CellStyle numStyle = wb.createCellStyle();
                            numStyle.cloneStyleFrom(contentStyle);
                            numStyle.setDataFormat(df.getFormat("#0"));
                            cell.setCellValue((Integer) value);
                            cell.setCellStyle(numStyle);
                        } else if (value instanceof Float || value instanceof Double) {
                            /* 小数类型 */
                            CellStyle floatStyle = wb.createCellStyle();
                            floatStyle.cloneStyleFrom(contentStyle);
                            floatStyle.setDataFormat(df.getFormat("#0.00"));
                            if (value instanceof Float) {
                                cell.setCellValue((Float) value);
                            } else {
                                cell.setCellValue((Double) value);
                            }
                            cell.setCellStyle(floatStyle);
                        } else {
                            cell.setCellValue(new XSSFRichTextString(value.toString()));
                        }
                    }
                }
            }


            // 新建一个sheet页
            String hiddenSheetName = "hiddenSheet";
            XSSFSheet hiddenSheet = wb.createSheet(hiddenSheetName);

            // 写入下拉数据到新的sheet页中
            for (int i = 0; i < choices.length; i++) {
                Cell cell = hiddenSheet.createRow(i).createCell(0);
                cell.setCellValue(choices[i]);
            }

            // 获取新sheet页内容
            String strFormula = hiddenSheetName + "!$A$1:$A$65535";
            XSSFDataValidationConstraint constraint0 = new XSSFDataValidationConstraint(DataValidationConstraint.ValidationType.LIST, strFormula);
            CellRangeAddressList regions = new CellRangeAddressList(0, 500, colNum, colNum);
            // 数据有效性对象
            DataValidationHelper help = new XSSFDataValidationHelper(sheet);
            DataValidation validation0 = help.createValidation(constraint0, regions);
            validation0.setShowErrorBox(true);
            sheet.addValidationData(validation0);

            // 将新建的sheet页隐藏掉
            wb.setSheetHidden(1, true);

            /* 添加下拉项 */
            DataValidationHelper helper = sheet.getDataValidationHelper();

            DataValidationConstraint constraint2 = helper.createExplicitListConstraint(new String[]{"人工", "自动化"});
            CellRangeAddressList addressList = new CellRangeAddressList(0, 200, 5, 5);
            DataValidation validation = helper.createValidation(constraint2, addressList);
            // 输入非法数据时，弹窗警告框
            validation.setShowErrorBox(true);
            sheet.addValidationData(validation);

            DataValidationConstraint constraint3 = helper.createExplicitListConstraint(new String[]{"未安装", "已安装", "已采集"});
            addressList = new CellRangeAddressList(0, 200, 6, 6);
            validation = helper.createValidation(constraint3, addressList);
            // 输入非法数据时，弹窗警告框
            validation.setShowErrorBox(true);
            sheet.addValidationData(validation);

            DataValidationConstraint constraint4 = helper.createExplicitListConstraint(new String[]{"正常", "维护改造", "停测", "损坏", "报废", "一般性检查"});
            addressList = new CellRangeAddressList(0, 200, 3, 3);
            validation = helper.createValidation(constraint4, addressList);
            // 输入非法数据时，弹窗警告框
            validation.setShowErrorBox(true);
            sheet.addValidationData(validation);

            /* 调整列宽自适应 */
            for (int i = 0; i < titles.length; i++) {
                sheet.autoSizeColumn(i);
                if (sheet.getColumnWidth(i) < 3000) {
                    sheet.setColumnWidth(i, 3000);
                }
                // 对太宽的列做限制
                if (sheet.getColumnWidth(i) > 8000) {
                    sheet.setColumnWidth(i, 8000);
                }
                if ("时间".equals(titles[i])) {
                    // 日期列设置24个字符
                    sheet.setColumnWidth(i, 24 * 256);
                }
            }
        });


        /* 输出到客户端 */
        if (needRemoveBytes) {
            System.out.println("去掉了10字节");
            try {
                ByteArrayOutputStream bos = new ByteArrayOutputStream();
                wb.write(bos);
                byte[] srcBytes = bos.toByteArray();
                byte[] resBytes = new byte[srcBytes.length - 10];
                System.arraycopy(srcBytes, 10, resBytes, 0, resBytes.length);
                os.write(resBytes);
                os.flush();
            } catch (IOException e) {
                e.printStackTrace();
            } finally {
                os.close();
            }
        } else {
            wb.write(os);
            os.flush();
            os.close();
        }
    }

    /**
     * 设置二级级联下拉框数据
     *
     * @param wb        表格对象
     * @param typeName  要渲染的sheet名称
     * @param values    级联下拉数据
     * @param fatherCol 父级下拉区域
     * @param sonCol    子级下拉区域
     */
    public static void setSecondCascadeDropDownBox(XSSFWorkbook wb, String typeName, Map<String, String[]> values, Integer fatherCol, Integer sonCol) {
        String hiddenSheetName = "hiddenSecond";
        //获取所有sheet页个数
        int sheetTotal = wb.getNumberOfSheets();
        //处理下拉数据
        if (values != null && values.size() != 0) {
            //新建一个sheet页
            XSSFSheet hiddenSheet = wb.getSheet(hiddenSheetName);
            if (hiddenSheet == null) {
                hiddenSheet = wb.createSheet(hiddenSheetName);
                sheetTotal++;
            }
            // 获取数据起始行
            int startRowNum = hiddenSheet.getLastRowNum() + 1;
            int endRowNum = startRowNum;
            Set<String> keySet = values.keySet();
            for (String key : keySet) {
                XSSFRow fRow = hiddenSheet.createRow(endRowNum++);
                fRow.createCell(0).setCellValue(key);
                String[] sons = values.get(key);
                for (int i = 1; i <= sons.length; i++) {
                    fRow.createCell(i).setCellValue(sons[i - 1]);
                }
                // 添加名称管理器
                String range = getRange(1, endRowNum, sons.length);
                Name name = wb.createName();

                //key不可重复
//                if (key.contains("#")) {
//                    key = key.replace("#", "");
//                }
                key = key.replace("#", "").replace('-', '_');
                key = "_" + key;

                name.setNameName(key);
                String formula = hiddenSheetName + "!" + range;
                name.setRefersToFormula(formula);
            }
            //将数据字典sheet页隐藏掉
            wb.setSheetHidden(sheetTotal - 1, true);

            // 设置父级下拉
            //获取新sheet页内容
            String mainFormula = hiddenSheetName + "!$A$" + ++startRowNum + ":$A$" + endRowNum;
            XSSFSheet mainSheet = wb.getSheet(typeName);
            // 设置下拉列表值绑定到主sheet页具体哪个单元格起作用
            mainSheet.addValidationData(SetDataValidation(wb, mainFormula, 1, 65535, fatherCol, fatherCol));

            // 设置子级下拉
            // 当前列为子级下拉框的内容受父级哪一列的影响
//            String indirectFormula = "INDIRECT($" + decimalToTwentyHex(fatherCol + 1) + "2)";
            String formulaSec = "INDIRECT(CONCATENATE(\"_\",SUBSTITUTE(SUBSTITUTE($" + decimalToTwentyHex(fatherCol + 1) + "2,\"#\",\"\"),\"-\",\"_\")))";

            String s = "";
            System.out.println(formulaSec);
//            String indirectFormula = "INDIRECT($" + decimalToTwentyHex(fatherCol + 1) + "2)";
            mainSheet.addValidationData(SetDataValidation(wb, formulaSec, 1, 65535, sonCol, sonCol));
        }
    }

    /**
     * 计算formula
     *
     * @param offset   偏移量，如果给0，表示从A列开始，1，就是从B列
     * @param rowId    第几行
     * @param colCount 一共多少列
     * @return 如果给入参 1,1,10. 表示从B1-K1。最终返回 $B$1:$K$1
     */
    public static String getRange(int offset, int rowId, int colCount) {
        char start = (char) ('A' + offset);
        if (colCount <= 25) {
            char end = (char) (start + colCount - 1);
            return "$" + start + "$" + rowId + ":$" + end + "$" + rowId;
        } else {
            char endPrefix = 'A';
            char endSuffix = 'A';
            if ((colCount - 25) / 26 == 0 || colCount == 51) {// 26-51之间，包括边界（仅两次字母表计算）
                if ((colCount - 25) % 26 == 0) {// 边界值
                    endSuffix = (char) ('A' + 25);
                } else {
                    endSuffix = (char) ('A' + (colCount - 25) % 26 - 1);
                }
            } else {// 51以上
                if ((colCount - 25) % 26 == 0) {
                    endSuffix = (char) ('A' + 25);
                    endPrefix = (char) (endPrefix + (colCount - 25) / 26 - 1);
                } else {
                    endSuffix = (char) ('A' + (colCount - 25) % 26 - 1);
                    endPrefix = (char) (endPrefix + (colCount - 25) / 26);
                }
            }
            return "$" + start + "$" + rowId + ":$" + endPrefix + endSuffix + "$" + rowId;
        }
    }

    /**
     * 返回类型 DataValidation
     *
     * @param wb         表格对象
     * @param strFormula formula
     * @param firstRow   起始行
     * @param endRow     终止行
     * @param firstCol   起始列
     * @param endCol     终止列
     * @return 返回类型 DataValidation
     */
    public static DataValidation SetDataValidation(Workbook wb, String strFormula, int firstRow, int endRow, int firstCol, int endCol) {
        CellRangeAddressList regions = new CellRangeAddressList(firstRow, endRow, firstCol, endCol);
        DataValidationHelper dvHelper = new XSSFDataValidationHelper((XSSFSheet) wb.getSheet("typelist"));
        DataValidationConstraint formulaListConstraint = dvHelper.createFormulaListConstraint(strFormula);
        return dvHelper.createValidation(formulaListConstraint, regions);
    }

    /**
     * 十进制转二十六进制
     */
    public static String decimalToTwentyHex(int decimalNum) {
        StringBuilder result = new StringBuilder();
        while (decimalNum > 0) {
            int remainder = decimalNum % 26;
            result.append((char) (remainder + 64));//大写A的ASCII码值为65
            decimalNum = decimalNum / 26;
        }
        return result.reverse().toString();
    }


    /**
     * 导出带有下拉列表的Excel
     */
    public static void exportChoiceExcelSpeWithSecond(String name, String[] titles, String[] keys, Map<String, List<Map<String, Object>>> sheetMaps,
                                                      HttpServletResponse response, boolean needRemoveBytes, String dateFormat, boolean needDesc, Map<Integer, String[]> choiceMap,
                                                      Map<String, String[]> values, Integer fatherCol, Integer sonCol) throws IOException {
        /* 获取输出流 */
        ServletOutputStream os = response.getOutputStream();
        response.setHeader("Content-disposition", "attachment; filename=" + new String((name + ".xlsx").getBytes(StandardCharsets.UTF_8), StandardCharsets.ISO_8859_1));
        response.setContentType("application/x-download");

        /* 创建sheet */
        XSSFWorkbook wb = new XSSFWorkbook();

        /* 创建内容、表头样式 */
        CellStyle descStyle = createBoldBigBackgroundCellStyle(wb);
        CellStyle titleStyle = createBoldCellStyle(wb);
        CellStyle contentStyle = createNormalCellStyle(wb);

        /* 写入多Sheet */
        sheetMaps.forEach((sheetName, valueMaps) -> {
            XSSFSheet sheet = wb.createSheet(sheetName.replace("/", ""));

            int start = 0;
            if (needDesc) {
                start = 1;
                /* 写描述行（合并前2行） */
                for (int i = 0; i < 2; i++) {
                    XSSFRow row = sheet.createRow((short) i);
                    for (int j = 0; j < titles.length; j++) {
                        XSSFCell cell = row.createCell((short) j);
                        cell.setCellStyle(descStyle);
                        if (i == 0 && j == 0) {
                            cell.setCellValue(new XSSFRichTextString(name));
                        }
                    }
                }
                sheet.addMergedRegion(new CellRangeAddress(0, 0, 0, titles.length - 1));
            }

            /* 写表头行 */
            XSSFRow row = sheet.createRow((short) start);
            for (int i = 0; i < titles.length; i++) {
                XSSFCell cell = row.createCell((short) i);
                cell.setCellStyle(titleStyle);
                cell.setCellValue(new XSSFRichTextString(titles[i]));
            }

            /* 写内容行 */
            for (int i = 0; i < valueMaps.size(); i++) {
                row = sheet.createRow((short) (i + start + 1));
                Map<String, Object> map = valueMaps.get(i);
                for (int j = 0; j < keys.length; j++) {
                    XSSFCell cell = row.createCell((short) (j));
                    cell.setCellStyle(contentStyle);
                    Object value = map.get(keys[j]);
                    if (value == null) {
                        cell.setCellValue(new XSSFRichTextString(""));
                    } else {
                        XSSFDataFormat df = wb.createDataFormat();
                        if (value instanceof Date) {
                            /* 日期类型 */
                            CellStyle dateStyle = wb.createCellStyle();
                            dateStyle.cloneStyleFrom(contentStyle);
                            DataFormat format = wb.createDataFormat();
                            dateStyle.setDataFormat(format.getFormat(dateFormat));
                            cell.setCellValue((Date) value);
                            cell.setCellStyle(dateStyle);
                        } else if (value instanceof Integer) {
                            /* 整数类型 */
                            CellStyle numStyle = wb.createCellStyle();
                            numStyle.cloneStyleFrom(contentStyle);
                            numStyle.setDataFormat(df.getFormat("#0"));
                            cell.setCellValue((Integer) value);
                            cell.setCellStyle(numStyle);
                        } else if (value instanceof Float || value instanceof Double) {
                            /* 小数类型 */
                            CellStyle floatStyle = wb.createCellStyle();
                            floatStyle.cloneStyleFrom(contentStyle);
                            floatStyle.setDataFormat(df.getFormat("#0.00"));
                            if (value instanceof Float) {
                                cell.setCellValue((Float) value);
                            } else {
                                cell.setCellValue((Double) value);
                            }
                            cell.setCellStyle(floatStyle);
                        } else {
                            cell.setCellValue(new XSSFRichTextString(value.toString()));
                        }
                    }
                }
            }

            setSecondCascadeDropDownBox(wb, sheetName, values, fatherCol, sonCol);

            AtomicInteger hiddenSheetIndex = new AtomicInteger(1);
            choiceMap.forEach((colNum, choices) -> {
                // 新建一个sheet页
                String hiddenSheetName = "hiddenSheet" + hiddenSheetIndex;
                XSSFSheet hiddenSheet = wb.createSheet(hiddenSheetName);

                // 写入下拉数据到新的sheet页中
                for (int i = 0; i < choices.length; i++) {
                    Cell cell = hiddenSheet.createRow(i).createCell(0);
                    cell.setCellValue(choices[i]);
                }

                // 获取新sheet页内容
                String strFormula = hiddenSheetName + "!$A$1:$A$65535";
                XSSFDataValidationConstraint constraint0 = new XSSFDataValidationConstraint(DataValidationConstraint.ValidationType.LIST, strFormula);
                CellRangeAddressList regions = new CellRangeAddressList(0, 500, colNum, colNum);
                // 数据有效性对象
                DataValidationHelper help = new XSSFDataValidationHelper(sheet);
                DataValidation validation0 = help.createValidation(constraint0, regions);
                validation0.setShowErrorBox(true);
                sheet.addValidationData(validation0);

                // 将新建的sheet页隐藏掉
                wb.setSheetHidden(hiddenSheetIndex.getAndIncrement(), true);
            });

            /* 调整列宽自适应 */
            for (int i = 0; i < titles.length; i++) {
                sheet.autoSizeColumn(i);
                if (sheet.getColumnWidth(i) < 5000) {
                    sheet.setColumnWidth(i, 5000);
                }
                // 对太宽的列做限制
                if (sheet.getColumnWidth(i) > 9000) {
                    sheet.setColumnWidth(i, 9000);
                }
                if ("时间".equals(titles[i])) {
                    // 日期列设置24个字符
                    sheet.setColumnWidth(i, 24 * 256);
                }
            }
        });


        /* 输出到客户端 */
        if (needRemoveBytes) {
            System.out.println("去掉了10字节");
            try {
                ByteArrayOutputStream bos = new ByteArrayOutputStream();
                wb.write(bos);
                byte[] srcBytes = bos.toByteArray();
                byte[] resBytes = new byte[srcBytes.length - 10];
                System.arraycopy(srcBytes, 10, resBytes, 0, resBytes.length);
                os.write(resBytes);
                os.flush();
            } catch (IOException e) {
                e.printStackTrace();
            } finally {
                os.close();
            }
        } else {
            wb.write(os);
            os.flush();
            os.close();
        }
    }

    /**
     * 导出带有下拉列表的Excel
     */
    public static void exportChoiceExcelSpe(String name, String[] titles, String[] keys, Map<String, List<Map<String, Object>>> sheetMaps,
                                            HttpServletResponse response, boolean needRemoveBytes, String dateFormat, boolean needDesc, Map<Integer, String[]> choiceMap) throws IOException {
        /* 获取输出流 */
        ServletOutputStream os = response.getOutputStream();
        response.setHeader("Content-disposition", "attachment; filename=" + new String((name + ".xlsx").getBytes(StandardCharsets.UTF_8), StandardCharsets.ISO_8859_1));
        response.setContentType("application/x-download");

        /* 创建sheet */
        XSSFWorkbook wb = new XSSFWorkbook();

        /* 创建内容、表头样式 */
        CellStyle descStyle = createBoldBigBackgroundCellStyle(wb);
        CellStyle titleStyle = createBoldCellStyle(wb);
        CellStyle contentStyle = createNormalCellStyle(wb);

        /* 写入多Sheet */
        sheetMaps.forEach((sheetName, valueMaps) -> {
            XSSFSheet sheet = wb.createSheet(sheetName.replace("/", ""));

            int start = 0;
            if (needDesc) {
                start = 1;
                /* 写描述行（合并前2行） */
                for (int i = 0; i < 2; i++) {
                    XSSFRow row = sheet.createRow((short) i);
                    for (int j = 0; j < titles.length; j++) {
                        XSSFCell cell = row.createCell((short) j);
                        cell.setCellStyle(descStyle);
                        if (i == 0 && j == 0) {
                            cell.setCellValue(new XSSFRichTextString(name));
                        }
                    }
                }
                sheet.addMergedRegion(new CellRangeAddress(0, 0, 0, titles.length - 1));
            }

            /* 写表头行 */
            XSSFRow row = sheet.createRow((short) start);
            for (int i = 0; i < titles.length; i++) {
                XSSFCell cell = row.createCell((short) i);
                cell.setCellStyle(titleStyle);
                cell.setCellValue(new XSSFRichTextString(titles[i]));
            }

            /* 写内容行 */
            for (int i = 0; i < valueMaps.size(); i++) {
                row = sheet.createRow((short) (i + start + 1));
                Map<String, Object> map = valueMaps.get(i);
                for (int j = 0; j < keys.length; j++) {
                    XSSFCell cell = row.createCell((short) (j));
                    cell.setCellStyle(contentStyle);
                    Object value = map.get(keys[j]);
                    if (value == null) {
                        cell.setCellValue(new XSSFRichTextString(""));
                    } else {
                        XSSFDataFormat df = wb.createDataFormat();
                        if (value instanceof Date) {
                            /* 日期类型 */
                            CellStyle dateStyle = wb.createCellStyle();
                            dateStyle.cloneStyleFrom(contentStyle);
                            DataFormat format = wb.createDataFormat();
                            dateStyle.setDataFormat(format.getFormat(dateFormat));
                            cell.setCellValue((Date) value);
                            cell.setCellStyle(dateStyle);
                        } else if (value instanceof Integer) {
                            /* 整数类型 */
                            CellStyle numStyle = wb.createCellStyle();
                            numStyle.cloneStyleFrom(contentStyle);
                            numStyle.setDataFormat(df.getFormat("#0"));
                            cell.setCellValue((Integer) value);
                            cell.setCellStyle(numStyle);
                        } else if (value instanceof Float || value instanceof Double) {
                            /* 小数类型 */
                            CellStyle floatStyle = wb.createCellStyle();
                            floatStyle.cloneStyleFrom(contentStyle);
                            floatStyle.setDataFormat(df.getFormat("#0.00"));
                            if (value instanceof Float) {
                                cell.setCellValue((Float) value);
                            } else {
                                cell.setCellValue((Double) value);
                            }
                            cell.setCellStyle(floatStyle);
                        } else {
                            cell.setCellValue(new XSSFRichTextString(value.toString()));
                        }
                    }
                }
            }

            AtomicInteger hiddenSheetIndex = new AtomicInteger(1);
            choiceMap.forEach((colNum, choices) -> {
                // 新建一个sheet页
                String hiddenSheetName = "hiddenSheet" + hiddenSheetIndex;
                XSSFSheet hiddenSheet = wb.createSheet(hiddenSheetName);

                // 写入下拉数据到新的sheet页中
                for (int i = 0; i < choices.length; i++) {
                    Cell cell = hiddenSheet.createRow(i).createCell(0);
                    cell.setCellValue(choices[i]);
                }

                // 获取新sheet页内容
                String strFormula = hiddenSheetName + "!$A$1:$A$65535";
                XSSFDataValidationConstraint constraint0 = new XSSFDataValidationConstraint(DataValidationConstraint.ValidationType.LIST, strFormula);
                CellRangeAddressList regions = new CellRangeAddressList(0, 500, colNum, colNum);
                // 数据有效性对象
                DataValidationHelper help = new XSSFDataValidationHelper(sheet);
                DataValidation validation0 = help.createValidation(constraint0, regions);
                validation0.setShowErrorBox(true);
                sheet.addValidationData(validation0);

                // 将新建的sheet页隐藏掉
                wb.setSheetHidden(hiddenSheetIndex.getAndIncrement(), true);
            });

            /* 调整列宽自适应 */
            for (int i = 0; i < titles.length; i++) {
                sheet.autoSizeColumn(i);
                if (sheet.getColumnWidth(i) < 3000) {
                    sheet.setColumnWidth(i, 3000);
                }
                // 对太宽的列做限制
                if (sheet.getColumnWidth(i) > 8000) {
                    sheet.setColumnWidth(i, 8000);
                }
                if ("时间".equals(titles[i])) {
                    // 日期列设置24个字符
                    sheet.setColumnWidth(i, 24 * 256);
                }
            }
        });


        /* 输出到客户端 */
        if (needRemoveBytes) {
            System.out.println("去掉了10字节");
            try {
                ByteArrayOutputStream bos = new ByteArrayOutputStream();
                wb.write(bos);
                byte[] srcBytes = bos.toByteArray();
                byte[] resBytes = new byte[srcBytes.length - 10];
                System.arraycopy(srcBytes, 10, resBytes, 0, resBytes.length);
                os.write(resBytes);
                os.flush();
            } catch (IOException e) {
                e.printStackTrace();
            } finally {
                os.close();
            }
        } else {
            wb.write(os);
            os.flush();
            os.close();
        }
    }

    /**
     * 创建普通单元格样式
     */
    private static CellStyle createNormalCellStyle(XSSFWorkbook wb) {
        CellStyle baseStyle = getBaseCellStyle(wb);
        XSSFFont baseFont = getBaseFont(wb);

        baseStyle.setFont(baseFont);
        return baseStyle;
    }

    /**
     * 创建加粗单元格样式
     */
    private static CellStyle createBoldCellStyle(XSSFWorkbook wb) {
        CellStyle baseStyle = getBaseCellStyle(wb);
        XSSFFont baseFont = getBaseFont(wb);
        baseFont.setBold(true);

        baseStyle.setFont(baseFont);
        return baseStyle;
    }

    /**
     * 创建加粗、大字体且有背景色单元格样式
     */
    private static CellStyle createBoldBigBackgroundCellStyle(XSSFWorkbook wb) {
        CellStyle baseStyle = getBaseCellStyle(wb);
        XSSFFont baseFont = getBaseFont(wb);

        baseFont.setBold(true);
        baseFont.setFontHeightInPoints((short) 14);

        /* 填充背景色 */
        baseStyle.setFillPattern(FillPatternType.SOLID_FOREGROUND);
        XSSFColor bgColor = new XSSFColor(new java.awt.Color(240, 240, 240), new DefaultIndexedColorMap());
        ((XSSFCellStyle) baseStyle).setFillForegroundColor(bgColor);

        baseStyle.setFont(baseFont);
        return baseStyle;
    }

    /**
     * 获取基础单元格样式
     */
    private static CellStyle getBaseCellStyle(XSSFWorkbook wb) {
        CellStyle style = wb.createCellStyle();

        /* 内容居中 */
        style.setAlignment(HorizontalAlignment.CENTER);
        style.setVerticalAlignment(VerticalAlignment.CENTER);

        /* 细边框 */
        style.setBorderBottom(BorderStyle.THIN);
        style.setBorderLeft(BorderStyle.THIN);
        style.setBorderTop(BorderStyle.THIN);
        style.setBorderRight(BorderStyle.THIN);
        return style;
    }

    /**
     * 获取基础字体
     */
    private static XSSFFont getBaseFont(XSSFWorkbook wb) {
        XSSFFont font = wb.createFont();
        font.setFontName("微软雅黑");
        font.setFontHeightInPoints((short) 10);
        return font;
    }

}
