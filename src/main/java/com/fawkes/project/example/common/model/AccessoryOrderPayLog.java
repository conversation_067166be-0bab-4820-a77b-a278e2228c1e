package com.fawkes.project.example.common.model;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import com.fawkes.core.base.model.BaseEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * 配件订单支付记录
 *
 * <AUTHOR>
 */
@Data
@NoArgsConstructor
@ToString
public class AccessoryOrderPayLog extends BaseEntity implements Serializable {
    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "订单主键id")
    @JsonSerialize(using = ToStringSerializer.class)
    private Long orderId;

    @ApiModelProperty(value = "支付金额")
    private BigDecimal amount;

    public AccessoryOrderPayLog(Long orderId, BigDecimal amount) {
        this.orderId = orderId;
        this.amount = amount;
    }
}
