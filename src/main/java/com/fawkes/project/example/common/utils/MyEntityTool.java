package com.fawkes.project.example.common.utils;

import com.fawkes.core.base.model.BaseEntity;
import com.fawkes.core.enums.DeleteFlagEnum;
import com.fawkes.core.utils.http.HttpHeaderTool;
import com.fawkes.core.utils.id.IdTool;
import org.apache.commons.lang3.reflect.FieldUtils;
import org.springframework.util.CollectionUtils;

import java.sql.Timestamp;
import java.util.Collection;
import java.util.Objects;

public class MyEntityTool {
    private static final String ID = "id";
    private static final String CREATE_BY = "createBy";
    private static final String CREATE_DATE = "createDate";
    private static final String UPDATE_BY = "updateBy";
    private static final String UPDATE_DATE = "updateDate";
    private static final String DELETE_FLAG = "deleteFlag";
    private static final String PROCESS_STATE = "processState";

    /**
     * @param entity 要插入Db的entity集
     * @Description: 插入数据时封装 创建者 更新者 创建时间 更新时间 数据可用标志 ID
     * @return:
     * @auther: zhanghutao
     * @date: 2019-5-5 13:53
     */
    public static void insertEntity(Object entity) throws IllegalAccessException {
        if (entity != null) {
            String userName = HttpHeaderTool.getHeaderValue(HttpHeaderTool.HeaderEnum.AUTH_USERNAME);
            Timestamp timestamp = new Timestamp(System.currentTimeMillis());
            FieldUtils.writeField(entity, ID, IdTool.getId(), Boolean.TRUE);
            FieldUtils.writeField(entity, CREATE_BY, userName, Boolean.TRUE);
            FieldUtils.writeField(entity, CREATE_DATE, timestamp, Boolean.TRUE);
            FieldUtils.writeField(entity, UPDATE_BY, userName, Boolean.TRUE);
            FieldUtils.writeField(entity, UPDATE_DATE, timestamp, Boolean.TRUE);
            FieldUtils.writeField(entity, DELETE_FLAG, DeleteFlagEnum.DATA_OK.getFlag(), Boolean.TRUE);
        }
    }

    /**
     * @param collection 要批量插入Db的entity集合
     * @Description: 批量插入数据时封装 创建者 更新者 创建时间 更新时间 数据可用标志 ID
     * @return:
     * @auther: zhanghutao
     * @date: 2019-5-5 13:53
     */
    public static void insertEntity(Collection<Object> collection) throws IllegalAccessException {
        if (!CollectionUtils.isEmpty(collection)) {
            for (Object entity : collection) {
                insertEntity(entity);
            }
        }
    }

    /**
     * @Description: 更新数据时封装  更新者 更新时间
     * @param: entity 要更新Db的entity
     * @return:
     * @auther: zhanghutao
     * @date: 2019-5-5 13:54
     */
    public static void updateEntity(Object entity) throws IllegalAccessException {
        if (entity != null) {
            String userName = HttpHeaderTool.getHeaderValue(HttpHeaderTool.HeaderEnum.AUTH_USERNAME);
            Timestamp timestamp = new Timestamp(System.currentTimeMillis());
            FieldUtils.writeField(entity, UPDATE_BY, userName, Boolean.TRUE);
            FieldUtils.writeField(entity, UPDATE_DATE, timestamp, Boolean.TRUE);
        }
    }

    /**
     * @param collection 要批量更新Db的entity集合
     * @Description: 更新数据时封装  更新者 更新时间
     * @return:
     * @auther: zhanghutao
     * @date: 2019-5-5 13:54
     */
    public static void updateEntity(Collection<Object> collection) throws IllegalAccessException {
        if (!CollectionUtils.isEmpty(collection)) {
            for (Object entity : collection) {
                updateEntity(entity);
            }
        }
    }

    /**
     * @desc: 更新流程状态
     * @author: li_y29
     * @create: 2020-6-11
     */
    public static void updateProcessState(Object entity, String processState) throws IllegalAccessException {
        if (entity != null) {
            FieldUtils.writeField(entity, PROCESS_STATE, processState, Boolean.TRUE);
            Timestamp timestamp = new Timestamp(System.currentTimeMillis());
            FieldUtils.writeField(entity, UPDATE_DATE, timestamp, Boolean.TRUE);
        }
    }

    /**
     * 复制创建/删除信息
     *
     * @param source
     * @param target
     */
    public static void keepCrateAndDeleteFlag(BaseEntity source, BaseEntity target) {
        if (Objects.isNull(source) || Objects.isNull(target)) {
            return;
        }
        target.setId(source.getId());
        target.setDeleteFlag(source.getDeleteFlag());
        target.setCreateBy(source.getCreateBy());
        target.setCreateDate(source.getCreateDate());
    }

    /**
     * 获取更新人、更新时间
     *
     * @return
     */
    public static BaseEntity getUpdateInfo() {
        BaseEntity entity = new BaseEntity();
        String userName = HttpHeaderTool.getHeaderValue(HttpHeaderTool.HeaderEnum.AUTH_USERNAME);
        Timestamp timestamp = new Timestamp(System.currentTimeMillis());
        entity.setUpdateBy(userName);
        entity.setUpdateDate(timestamp);
        return entity;
    }
}
