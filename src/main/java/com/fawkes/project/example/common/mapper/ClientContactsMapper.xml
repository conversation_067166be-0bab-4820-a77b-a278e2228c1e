<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.fawkes.project.example.common.mapper.ClientContactsMapper">
    <resultMap id="ClientContacts" type="com.fawkes.project.example.common.model.ClientContacts"
               extends="com.fawkes.project.example.common.mapper.BaseMapper.BaseEntity">
        <result column="client_id" jdbcType="BIGINT" property="clientId"/>
        <result column="contacts_type" jdbcType="INTEGER" property="contactsType"/>
        <result column="contacts_name" jdbcType="VARCHAR" property="contactsName"/>
        <result column="phone" jdbcType="VARCHAR" property="phone"/>
        <result column="post" jdbcType="VARCHAR" property="post"/>
    </resultMap>
    <sql id="Column_List_ClientContacts">
        id
        , create_by, create_date, update_by, update_date, delete_flag, client_id, contacts_type, contacts_name, phone, post
    </sql>

    <select id="selectByClientIds" resultMap="ClientContacts">
        SELECT
        <include refid="Column_List_ClientContacts"/>
        FROM client_contacts
        WHERE delete_flag = 0 AND client_id IN
        <foreach collection="clientIds" item="clientId" open="(" separator="," close=")">#{clientId}</foreach>
    </select>

    <insert id="insertBatch" parameterType="com.fawkes.project.example.common.model.ClientContacts">
        insert into client_contacts (id, create_by, create_date,
        update_by, update_date, delete_flag,
        client_id, contacts_type, contacts_name,
        phone, post)
        VALUES
        <foreach collection="list" item="item" separator=",">
            (#{item.id,jdbcType=BIGINT}, #{item.createBy,jdbcType=VARCHAR}, #{item.createDate,jdbcType=TIMESTAMP},
            #{item.updateBy,jdbcType=VARCHAR}, #{item.updateDate,jdbcType=TIMESTAMP},
            #{item.deleteFlag,jdbcType=INTEGER},
            #{item.clientId,jdbcType=BIGINT}, #{item.contactsType,jdbcType=INTEGER},
            #{item.contactsName,jdbcType=VARCHAR},
            #{item.phone,jdbcType=VARCHAR}, #{item.post,jdbcType=VARCHAR})
        </foreach>
    </insert>

    <update id="updateBatch" parameterType="com.fawkes.project.example.common.model.ClientContacts">
        <foreach collection="list" item="item" separator=";">
            update client_contacts
            set create_by = #{item.createBy,jdbcType=VARCHAR},
            create_date = #{item.createDate,jdbcType=TIMESTAMP},
            update_by = #{item.updateBy,jdbcType=VARCHAR},
            update_date = #{item.updateDate,jdbcType=TIMESTAMP},
            delete_flag = #{item.deleteFlag,jdbcType=INTEGER},
            client_id = #{item.clientId,jdbcType=BIGINT},
            contacts_type = #{item.contactsType,jdbcType=INTEGER},
            contacts_name = #{item.contactsName,jdbcType=VARCHAR},
            phone = #{item.phone,jdbcType=VARCHAR},
            post = #{item.post,jdbcType=VARCHAR}
            where id = #{item.id,jdbcType=BIGINT}
        </foreach>
    </update>
</mapper>