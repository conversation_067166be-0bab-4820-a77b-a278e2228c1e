package com.fawkes.project.example.common.mapper;

import com.fawkes.project.example.common.model.ProductCraftCompose;
import com.fawkes.project.example.domain.vo.ProductCraftComposeVO;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface ProductCraftComposeMapper {
    /**
     * 批量插入
     *
     * @param composeList
     * @return
     */
    boolean insertBatch(@Param("list") List<? extends ProductCraftCompose> composeList);

    /**
     * 根据所属工艺查询
     *
     * @param craftId
     * @return
     */
    List<ProductCraftComposeVO> selectAllByCraftId(@Param("craftId") Long craftId);

    /**
     * 批量更新
     *
     * @param composeList
     * @return
     */
    boolean updateBatch(@Param("list") List<? extends ProductCraftCompose> composeList);
}