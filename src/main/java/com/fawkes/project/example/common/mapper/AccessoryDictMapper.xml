<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.fawkes.project.example.common.mapper.AccessoryDictMapper">
    <resultMap id="AccessoryDict" type="com.fawkes.project.example.common.model.AccessoryDict"
               extends="com.fawkes.project.example.common.mapper.BaseMapper.BaseEntity">
        <result column="dict_code" jdbcType="VARCHAR" property="dictCode"/>
        <result column="dict_name" jdbcType="VARCHAR" property="dictName"/>
        <result column="dict_remark" jdbcType="VARCHAR" property="dictRemark"/>
        <result column="dict_parent" jdbcType="BIGINT" property="dictParent"/>
    </resultMap>
    <resultMap id="AccessoryDictDTO" type="com.fawkes.project.example.domain.dto.AccessoryDictDTO"
               extends="AccessoryDict"/>


    <select id="isExistedDictCode" parameterType="com.fawkes.project.example.common.model.AccessoryDict"
            resultType="java.lang.Boolean">
        SELECT count( * ) FROM accessory_dict
        WHERE delete_flag = 0
        AND dict_code = #{dict.dictCode}
        <if test="dict.dictParent == null">AND dict_parent IS NULL</if>
        <if test="dict.dictParent != null">AND dict_parent = #{dict.dictParent}</if>
        <if test="dict.id != null">AND id != #{dict.id}</if>
    </select>

    <select id="isExistedDictName" parameterType="com.fawkes.project.example.common.model.AccessoryDict"
            resultType="java.lang.Boolean">
        SELECT count( * ) FROM accessory_dict
        WHERE delete_flag = 0
        AND dict_name = #{dict.dictName}
        <if test="dict.dictParent == null">AND dict_parent IS NULL</if>
        <if test="dict.dictParent != null">AND dict_parent = #{dict.dictParent}</if>
        <if test="dict.id != null">AND id != #{dict.id}</if>
    </select>

    <select id="isUsedAccessoryDictById" parameterType="java.lang.Long" resultType="java.lang.Boolean">
        SELECT count(*)
        FROM accessory a
                 LEFT JOIN accessory_dict b ON (a.category_id = b.id OR a.ref_id = b.id)
        WHERE a.delete_flag = 0
          AND b.delete_flag = 0
          AND b.id = #{accessoryId,jdbcType=BIGINT}
    </select>

    <select id="selectAccessoryDictType" resultMap="AccessoryDictDTO">
        SELECT id, dict_code, dict_name, dict_remark, dict_parent FROM accessory_dict
        WHERE ISNULL(dict_parent) AND delete_flag = 0
        <if test="typeName != null and typeName != ''">
            AND dict_name LIKE CONCAT('%', #{typeName}, '%')
        </if>
        <if test="typeCode != null and typeCode != ''">
            AND dict_code LIKE CONCAT('%', #{typeCode}, '%')
        </if>
        ORDER BY create_date DESC, dict_name ASC
    </select>

    <select id="selectByDictParentIds" resultMap="AccessoryDict">
        SELECT id, dict_code, dict_name, dict_remark, dict_parent FROM accessory_dict
        WHERE delete_flag = 0
        AND dict_parent IN
        <foreach collection="dictParentIds" item="id" separator=", " open="(" close=")">#{id}</foreach>
        ORDER BY create_date ASC
    </select>

    <select id="selectById" resultMap="AccessoryDict">
        SELECT id, dict_code, dict_name, dict_remark, dict_parent
        FROM accessory_dict
        WHERE id = #{id,jdbcType=BIGINT}
    </select>


    <insert id="insert" parameterType="com.fawkes.project.example.common.model.AccessoryDict">
        insert into accessory_dict (id, create_by, create_date,
                                    update_by, update_date, delete_flag,
                                    dict_code, dict_name, dict_remark,
                                    dict_parent)
        values (#{id,jdbcType=BIGINT}, #{createBy,jdbcType=VARCHAR}, #{createDate,jdbcType=TIMESTAMP},
                #{updateBy,jdbcType=VARCHAR}, #{updateDate,jdbcType=TIMESTAMP}, #{deleteFlag,jdbcType=INTEGER},
                #{dictCode,jdbcType=VARCHAR}, #{dictName,jdbcType=VARCHAR}, #{dictRemark,jdbcType=VARCHAR},
                #{dictParent,jdbcType=BIGINT})
    </insert>

    <insert id="insertBatch" parameterType="com.fawkes.project.example.common.model.AccessoryDict">
        insert into accessory_dict (id, create_by, create_date,
        update_by, update_date, delete_flag,
        dict_code, dict_name, dict_remark,
        dict_parent)
        VALUES
        <foreach collection="list" item="item" separator=",">
            (#{item.id,jdbcType=BIGINT}, #{item.createBy,jdbcType=VARCHAR}, #{item.createDate,jdbcType=TIMESTAMP},
            #{item.updateBy,jdbcType=VARCHAR}, #{item.updateDate,jdbcType=TIMESTAMP},
            #{item.deleteFlag,jdbcType=INTEGER},
            #{item.dictCode,jdbcType=VARCHAR}, #{item.dictName,jdbcType=VARCHAR}, #{item.dictRemark,jdbcType=VARCHAR},
            #{item.dictParent,jdbcType=BIGINT})
        </foreach>
    </insert>


    <update id="updateByPrimaryKey" parameterType="com.fawkes.project.example.common.model.AccessoryDict">
        update accessory_dict
        set update_by = #{updateBy,jdbcType=VARCHAR},
        update_date = #{updateDate,jdbcType=TIMESTAMP},
        dict_code = #{dictCode,jdbcType=VARCHAR},
        dict_name = #{dictName,jdbcType=VARCHAR},
        dict_remark = #{dictRemark,jdbcType=VARCHAR}
        <if test="dictParent != null">,dict_parent = #{dictParent,jdbcType=BIGINT}</if>
        where id = #{id,jdbcType=BIGINT}
    </update>

    <update id="deleteById" parameterType="com.fawkes.project.example.common.model.AccessoryDict">
        update accessory_dict
        set update_by   = #{updateInfo.updateBy,jdbcType=VARCHAR},
            update_date = #{updateInfo.updateDate,jdbcType=TIMESTAMP},
            delete_flag = -1
        where id = #{id,jdbcType=BIGINT}
    </update>
</mapper>