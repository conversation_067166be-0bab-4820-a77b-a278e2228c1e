<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.fawkes.project.example.common.mapper.AccessoryOrderGoodsMapper">
    <resultMap id="AccessoryOrderGoods" type="com.fawkes.project.example.common.model.AccessoryOrderGoods"
               extends="com.fawkes.project.example.common.mapper.BaseMapper.BaseEntity">
        <result column="order_id" jdbcType="BIGINT" property="orderId"/>
        <result column="accessory_id" jdbcType="BIGINT" property="accessoryId"/>
        <result column="purchase_quantity" jdbcType="INTEGER" property="purchaseQuantity"/>
        <result column="arrival_quantity" jdbcType="INTEGER" property="arrivalQuantity"/>
        <result column="purchase_price" jdbcType="DECIMAL" property="purchasePrice"/>
    </resultMap>
    <resultMap id="AccessoryOrderGoodsVO" type="com.fawkes.project.example.domain.vo.AccessoryOrderGoodsVO"
               extends="AccessoryOrderGoods">
        <result column="accessory_code" jdbcType="VARCHAR" property="accessoryCode"/>
        <result column="accessory_name" jdbcType="VARCHAR" property="accessoryName"/>
        <result column="accessory_type" jdbcType="VARCHAR" property="accessoryType"/>
        <result column="accessory_unit" jdbcType="VARCHAR" property="accessoryUnit"/>
        <result column="accessory_spec" jdbcType="VARCHAR" property="accessorySpec"/>
        <result column="accessory_pic" jdbcType="VARCHAR" property="accessoryPic"/>
        <result column="accessory_supplier" jdbcType="VARCHAR" property="accessorySupplier"/>
        <result column="accessory_cur_price" jdbcType="DECIMAL" property="accessoryCurPrice"/>
        <result column="order_no" jdbcType="DECIMAL" property="orderNo"/>
        <result column="order_name" jdbcType="DECIMAL" property="orderName"/>
        <result column="order_date" jdbcType="DECIMAL" property="orderDate"/>
    </resultMap>
    <resultMap id="AccessoryPriceMap" type="com.fawkes.project.example.domain.dto.AccessoryPriceDTO">
        <id column="accessory_id" jdbcType="BIGINT" property="accessoryId"/>
        <result column="total_quantity" jdbcType="INTEGER" property="totalQuantity"/>
        <result column="total_price" jdbcType="DECIMAL" property="totalPrice"/>
    </resultMap>
    <sql id="Column_List_AccessoryOrderGoods">
        id
        , create_by, create_date, update_by, update_date, delete_flag, order_id, accessory_id, purchase_quantity, arrival_quantity, purchase_price
    </sql>

    <select id="selectAccessoryOrderGoodsByOrderId" resultMap="AccessoryOrderGoodsVO">
        SELECT a.id,
               a.create_by,
               a.create_date,
               a.update_by,
               a.update_date,
               a.delete_flag,
               a.order_id,
               a.accessory_id,
               IFNULL(a.purchase_quantity, 0)        AS purchase_quantity,
               IFNULL(a.arrival_quantity, 0)         AS arrival_quantity,
               IFNULL(a.purchase_price, 0)           AS purchase_price,
               CONCAT(c.dict_name, '-', d.dict_name) as accessory_name,
               CONCAT(c.dict_code, '-', d.dict_code) as accessory_code,
               b.accessory_type,
               b.accessory_unit,
               b.accessory_spec,
               b.accessory_pic,
               b.accessory_supplier,
               IFNULL(b.cur_price, 0)                as accessory_cur_price
        from accessory_order_goods a
                 LEFT JOIN accessory b on a.accessory_id = b.id
                 LEFT JOIN accessory_dict c on b.category_id = c.id
                 LEFT JOIN accessory_dict d on b.ref_id = d.id
        where a.delete_flag = 0
          and a.order_id = #{orderId}
        ORDER BY update_date DESC
    </select>

    <select id="selectByOrderId" resultMap="AccessoryOrderGoods">
        select
        <include refid="Column_List_AccessoryOrderGoods"/>
        from accessory_order_goods where delete_flag = 0 and order_id = #{orderId}
    </select>

    <select id="selectByAccessoryId" resultMap="AccessoryOrderGoodsVO">
        SELECT b.*,
               a.order_no,
               a.order_name,
               a.order_date
        FROM accessory_order a
                 LEFT JOIN accessory_order_goods b ON b.order_id = a.id
        WHERE a.delete_flag = 0
          AND b.delete_flag = 0
          AND b.accessory_id = #{accessoryId}
        ORDER BY a.order_date DESC
    </select>

    <select id="selectAccessoryPrice" resultMap="AccessoryPriceMap">
        SELECT
        a.id AS accessory_id,
        IFNULL( SUM( b.purchase_quantity ), 0 ) AS total_quantity,
        IFNULL( SUM( b.purchase_price ), 0 ) AS total_price
        FROM
        accessory a
        LEFT JOIN accessory_order_goods b ON b.accessory_id = a.id AND b.delete_flag = 0
        <where>
            a.delete_flag = 0
            <if test="accessoryIds != null">
                AND a.id IN
                <foreach collection="accessoryIds" item="accessoryId" separator=", " open="(" close=")">#{accessoryId}
                </foreach>
            </if>
        </where>
        GROUP BY
        a.id
    </select>

    <insert id="insertBatch" parameterType="com.fawkes.project.example.common.model.AccessoryOrderGoods">
        insert into accessory_order_goods (id, create_by, create_date,update_by, update_date, delete_flag,
        order_id, accessory_id, purchase_quantity, arrival_quantity, purchase_price)
        VALUES
        <foreach collection="list" item="item" separator=",">
            (#{item.id,jdbcType=BIGINT}, #{item.createBy,jdbcType=VARCHAR},
            #{item.createDate,jdbcType=TIMESTAMP},#{item.updateBy,jdbcType=VARCHAR},
            #{item.updateDate,jdbcType=TIMESTAMP},#{item.deleteFlag,jdbcType=INTEGER},
            #{item.orderId,jdbcType=BIGINT},
            #{item.accessoryId,jdbcType=BIGINT},#{item.purchaseQuantity,jdbcType=INTEGER},#{item.arrivalQuantity,jdbcType=INTEGER},
            #{item.purchasePrice,jdbcType=DECIMAL})
        </foreach>
    </insert>

    <update id="deleteByOrderId">
        update accessory_order_goods
        set delete_flag = -1,
            update_by   = #{updateInfo.updateBy},
            update_date = #{updateInfo.updateDate}
        where order_id = #{orderId}
    </update>

    <update id="updateBatch" parameterType="com.fawkes.project.example.common.model.AccessoryOrderGoods">
        <foreach collection="list" item="item" separator=";">
            update accessory_order_goods
            set update_by = #{item.updateBy,jdbcType=VARCHAR},
            update_date = #{item.updateDate,jdbcType=TIMESTAMP},
            delete_flag = #{item.deleteFlag,jdbcType=INTEGER},
            order_id = #{item.orderId,jdbcType=BIGINT},
            accessory_id = #{item.accessoryId,jdbcType=BIGINT},
            purchase_quantity = #{item.purchaseQuantity,jdbcType=INTEGER},
            arrival_quantity = #{item.arrivalQuantity,jdbcType=INTEGER},
            purchase_price = #{item.purchasePrice,jdbcType=DECIMAL}
            where id = #{item.id,jdbcType=BIGINT}
        </foreach>
    </update>
</mapper>
