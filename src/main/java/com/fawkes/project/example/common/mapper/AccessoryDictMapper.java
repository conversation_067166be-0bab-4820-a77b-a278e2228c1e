package com.fawkes.project.example.common.mapper;

import com.fawkes.core.base.model.BaseEntity;
import com.fawkes.project.example.common.model.AccessoryDict;
import com.fawkes.project.example.domain.dto.AccessoryDictDTO;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <AUTHOR>
 */
public interface AccessoryDictMapper {

    /**
     * 校验字典编码是否已存在
     *
     * @param dict
     * @return
     */
    Boolean isExistedDictCode(@Param("dict") AccessoryDict dict);

    /**
     * 校验字典名称是否已存在
     *
     * @param dict
     * @return
     */
    Boolean isExistedDictName(@Param("dict") AccessoryDict dict);

    /**
     * 零件字典是否已被使用
     *
     * @param id
     * @return
     */
    Boolean isUsedAccessoryDictById(Long id);

    /**
     * 查询配件种类
     *
     * @param typeName
     * @param typeCode
     * @return
     */
    List<AccessoryDictDTO> selectAccessoryDictType(@Param("typeName") String typeName, @Param("typeCode") String typeCode);

    /**
     * 查询配件编号
     *
     * @param dictParentIds - 配件种类id
     * @return
     */
    List<AccessoryDict> selectByDictParentIds(@Param("dictParentIds") List<Long> dictParentIds);

    /**
     * 根据id查询
     *
     * @param id
     * @return
     */
    AccessoryDict selectById(Long id);

    /**
     * 新增
     *
     * @param record
     * @return
     */
    int insert(AccessoryDict record);

    /**
     * 批量新增
     *
     * @param list
     * @return
     * @deprecated 测试使用
     */
    int insertBatch(List<AccessoryDict> list);

    /**
     * 更新
     *
     * @param record
     * @return
     */
    int updateByPrimaryKey(AccessoryDict record);

    /**
     * 逻辑删除
     *
     * @param deleteById
     * @param updateInfo
     * @return
     */
    int deleteById(@Param("id") Long deleteById, @Param("updateInfo") BaseEntity updateInfo);
}