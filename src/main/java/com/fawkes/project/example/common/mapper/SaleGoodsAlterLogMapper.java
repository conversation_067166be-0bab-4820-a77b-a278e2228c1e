package com.fawkes.project.example.common.mapper;

import com.fawkes.project.example.common.model.SaleGoodsAlterLog;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface SaleGoodsAlterLogMapper {

    /**
     * 根据变更记录id查询变更记录
     *
     * @param alterId
     * @return
     */
    List<SaleGoodsAlterLog> selectByAlterId(@Param("alterId") Long alterId);

    /**
     * 批量插入
     *
     * @param logList
     * @return
     */
    int insertBatch(@Param("list") List<? extends SaleGoodsAlterLog> logList);
}