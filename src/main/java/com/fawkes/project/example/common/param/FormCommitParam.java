package com.fawkes.project.example.common.param;

import com.alibaba.fastjson.JSONObject;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * 表单保存参数
 *
 * <AUTHOR>
 * @date 2019-12-02
 */
@Data
public class FormCommitParam implements Serializable {

    @ApiModelProperty("数据对象名称，也就是数据库对象实例类名，例如：SysUser")
    private String entityName;

    @ApiModelProperty("对象Json")
    private JSONObject entityObject;

    @ApiModelProperty("对象明细列表")
    private List<FormDetailParam> detailParamList;

    @ApiModelProperty("发起流程参数")
    private FormProcessParam formProcessParam;
}
