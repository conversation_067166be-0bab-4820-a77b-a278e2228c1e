package com.fawkes.project.example.common.model;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import com.fawkes.core.base.model.BaseEntity;
import com.fawkes.project.example.common.constants.CommonConstants;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;

import java.io.Serializable;

/**
 * <AUTHOR>
 */
@Data
@ToString
@NoArgsConstructor
public class BizMsg extends BaseEntity implements Serializable {
    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "接收人id")
    @JsonSerialize(using = ToStringSerializer.class)
    private Long toUserId;

    @ApiModelProperty(value = "消息标题")
    private String title;

    @ApiModelProperty(value = "消息内容")
    private String content;

    @ApiModelProperty(value = "消息状态: 0 - 未读, 1 - 已读")
    private Integer status;

    public BizMsg(Long toUserId, String title, String content) {
        this.toUserId = toUserId;
        this.title = title;
        this.content = content;
        this.status = CommonConstants.BIZ_MSG_STATUS_UNREAD;
    }
}
