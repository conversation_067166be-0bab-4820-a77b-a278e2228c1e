package com.fawkes.project.example.common.model;

import com.fawkes.core.base.model.BaseEntity;
import com.fawkes.core.utils.EntityTool;
import com.fawkes.core.utils.http.HttpHeaderTool;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;

/**
 * <AUTHOR>
 */
@Data
@ToString
@NoArgsConstructor
public class BizLog extends BaseEntity {

    private String operateIP;

    private String module;

    private String content;

    public BizLog(String module, String content) {
        this.module = module;
        this.content = content;
        this.operateIP = HttpHeaderTool.getHeaderValue(HttpHeaderTool.HeaderEnum.IP_ADDRESS);
        EntityTool.insertEntity(this);
    }
}
