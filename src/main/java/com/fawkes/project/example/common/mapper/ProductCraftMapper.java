package com.fawkes.project.example.common.mapper;

import com.fawkes.core.base.model.BaseEntity;
import com.fawkes.project.example.common.model.ProductCraft;
import com.fawkes.project.example.domain.vo.ProductCraftVO;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface ProductCraftMapper {

    /**
     * 判断工艺是否已存在
     *
     * @param craft
     * @return
     */
    Boolean isExistedCraftCode(@Param("craft") ProductCraft craft);

    /**
     * 查询产品下已被使用的工艺
     *
     * @param productIds
     * @return
     */
    List<ProductCraft> selectUsedCraftByProductIds(@Param("productIds") List<Long> productIds);

    /**
     * 根据id查询工艺
     *
     * @param id
     * @return
     */
    ProductCraftVO selectById(Long id);

    /**
     * 查询工艺的指导成本
     *
     * @param ids
     * @return
     */
    List<ProductCraftVO> selectByIds(@Param("ids") List<Long> ids);

    /**
     * 根据产品id查询工艺
     *
     * @param productIds
     * @return
     */
    List<ProductCraftVO> selectByProductIds(@Param("productIds") List<Long> productIds, @Param("applyStatus") Boolean applyStatus);

    /**
     * 根据产品id查询激活的工艺
     *
     * @param productIds
     * @return
     */
    List<ProductCraftVO> selectActiveByProductIds(@Param("productIds") List<Long> productIds);

    /**
     * 判断工艺是否已被使用
     *
     * @param id
     * @return
     */
    boolean judgeUsedCraftById(Long id);

    /**
     * 判断工艺是否已被使用
     *
     * @param ids
     * @return
     */
    List<ProductCraftVO> judgeUsedCraftByIds(@Param("craftIds") List<Long> ids);

    /**
     * 判断产品是否已有激活的工艺
     *
     * @param productId
     * @return
     */
    Boolean hasActiveCraftByProductId(@Param("productId") Long productId);

    /**
     * 根据id查询工艺
     *
     * @param id
     * @return
     */
    ProductCraft selectByPrimaryKey(Long id);

    /**
     * 根据产品id查询所有工艺
     *
     * @param productId
     * @return
     */
    List<String> selectAllCraftCodeByProductId(@Param("productId") Long productId);

    /**
     * 弃用产品下的工艺
     *
     * @param productId
     * @param updateInfo
     * @return
     */
    int disableBatchByProductId(@Param("productId") Long productId, @Param("updateInfo") BaseEntity updateInfo);

    /**
     * 新增工艺
     *
     * @param record
     * @return
     */
    int insert(ProductCraft record);

    /**
     * 删除工艺
     *
     * @param craftId
     * @param updateInfo
     * @return
     */
    int deleteById(@Param("craftId") Long craftId, @Param("updateInfo") BaseEntity updateInfo);

    /**
     * 批量删除工艺
     *
     * @param productIds
     * @param updateInfo
     * @return
     */
    int deleteByProductIds(@Param("productIds") List<Long> productIds, @Param("updateInfo") BaseEntity updateInfo);

    /**
     * 更新工艺
     *
     * @param record
     * @return
     */
    int updateByPrimaryKey(ProductCraft record);

    /**
     * 批量更新
     *
     * @param craftList
     * @return
     */
    boolean updateBatch(@Param("list") List<? extends ProductCraft> craftList);
}