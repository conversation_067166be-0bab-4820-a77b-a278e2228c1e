package com.fawkes.project.example.common.config;

import com.github.benmanes.caffeine.cache.Expiry;

/**
 * 自定义过期策略
 */
public class CaffeineExpiry implements Expiry<String, Object> {

    @Override
    public long expireAfterCreate(String key, Object value, long currentTime) {
        return 0;
    }

    @Override
    public long expireAfterUpdate(String key, Object value, long currentTime, long currentDuration) {
        return currentDuration;
    }

    @Override
    public long expireAfterRead(String key, Object value, long currentTime, long currentDuration) {
        return currentDuration;
    }

}