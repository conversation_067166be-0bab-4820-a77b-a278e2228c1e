package com.fawkes.project.example.common.mapper;

import com.fawkes.project.example.common.model.ProductCalib;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface ProductCalibMapper {
    /**
     * 批量插入
     *
     * @param productCalibList
     * @return
     */
    boolean insertBatch(@Param("list") List<ProductCalib> productCalibList);

    /**
     * 查询产品下的参数率定
     *
     * @param productIds
     * @return
     */
    List<ProductCalib> selectByProductIds(@Param("productIds") List<Long> productIds);

    /**
     * 批量更新
     *
     * @param productCalibList
     * @return
     */
    boolean updateBatch(@Param("list") List<ProductCalib> productCalibList);
}