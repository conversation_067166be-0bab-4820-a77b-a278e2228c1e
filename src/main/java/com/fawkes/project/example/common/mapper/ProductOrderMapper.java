package com.fawkes.project.example.common.mapper;

import com.fawkes.project.example.common.model.*;
import com.fawkes.project.example.domain.dto.ProductSaleQuantityDTO;
import com.fawkes.project.example.domain.param.CheckQo;
import com.fawkes.project.example.domain.param.ProductCraftQo;
import com.fawkes.project.example.domain.param.ProductOrderQo;
import com.fawkes.project.example.domain.param.ProductStatusParam;
import com.fawkes.project.example.domain.vo.*;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface ProductOrderMapper {

    /**
     * 判断订单批次是否已存在
     *
     * @param order
     * @return
     */
    boolean isExistedOrderBatch(@Param("order") ProductOrder order);

    /**
     * 产品订单列表
     */
    List<ProductOrder> list(@Param("qo") ProductOrderQo qo);

    /**
     * 新增产品订单
     */
    void add(@Param("order") ProductOrder productOrder);

    /**
     * 修改产品订单
     */
    void update(@Param("order") ProductOrder productOrder);

    /**
     * 获取某年最大批次
     */
    Integer getMaxBatch(@Param("year") int year);

    /**
     * 由工艺及数量获取配件使用情况
     */
    List<Accessory> accessoryUseInfo(@Param("qos") List<ProductCraftQo> qos);

    /**
     * 删除产品订单
     */
    void del(@Param("ids") Long[] ids);

    /**
     * 批量保存具体产品
     */
    void addProductObjects(@Param("productObjects") List<ProductObject> productObjects);

    /**
     * 检验明细
     */
    List<CheckDetailVo> getCheckDetail(@Param("orderId") Long orderId, @Param("year") Integer year);

    /**
     * 产品检验
     */
    void productCheck(@Param("checkId") Long checkId, @Param("qo") CheckQo qo);

    List<CheckCalib> getCalibByProducts(@Param("vos") List<CheckDetailVo> vos);

    /**
     * 获取率定参数
     */
    List<CheckCalib> getCalibValByObjs(@Param("vos") List<CheckDetailVo> vos);

    /**
     * 删除率定参数
     */
    void delCalib(@Param("calib") CheckCalib calibs);

    /**
     * 设置率定参数
     */
    void addCalib(@Param("calibs") List<CheckCalib> calibs);


    List<ProductObject> getObjsByOrder(@Param("orderIds") List<Long> orderIds, @Param("year") Integer year);

    /**
     * 由产品ID获取产品信息
     */
    List<ProductObject> getObjsByIds(@Param("ids") List<Long> ids);

    /**
     * 添加配件损耗情况
     */
    void addAccessoryLoss(@Param("orderId") Long orderId, @Param("losses") List<ProductOrderLoss> losses);

    /**
     * 删除配件损耗情况
     */
    void delAccessoryLoss(@Param("orderId") Long orderId);

    /**
     * 获取订单配件使用情况（详情）
     *
     * @param orderId
     * @return
     */
    List<AccessoryUseInfoVo> getAccessoryLossByOrderId(@Param("orderId") Long orderId);

    /**
     * 获取订单配件使用情况（编辑）
     *
     * @param orderId
     * @return
     */
    List<ProductOrderLoss> getAccessoryLossInfoByOrderId(@Param("orderId") Long orderId);

    /**
     * 由订单ID获取订单基本信息
     */
    ProductOrderDetailVo getById(@Param("orderId") Long orderId);

    /**
     * 由订单ID获取订单对象
     *
     * @param orderId
     * @return
     */
    ProductOrder getProductOrderById(@Param("orderId") Long orderId);

    List<AccessoryUseInfoVo> getAccessoryUseInfoByOrderId(@Param("orderId") Long orderId);

    /**
     * 获取生产批次数量
     */
    Integer getBatchNum(@Param("isCurYear") Boolean isCurYear);

    /**
     * 获取生产产品数量
     */
    Integer getProductNum(@Param("isCurYear") Boolean isCurYear);

    /**
     * 删除该订单下所有具体产品
     */
    void delObjsByOrderIds(@Param("orderIds") Long[] orderIds);


    /**
     * 获取工艺的产品种类
     *
     * @param craftId
     * @return
     */
    String getCategoryCodeByCraft(@Param("craftId") Long craftId);

    /**
     * 获取产品最大编号
     *
     * @param objNoPrefix
     * @return
     */
    String getObjMaxNoByPrefix(@Param("objNoPrefix") String objNoPrefix);

    /**
     * 产品库存统计
     *
     * @param applyStatus
     */
    List<ProductObject> productInventory(Integer applyStatus);

    /**
     * 根据产品ID获取产品对象
     *
     * @param productId
     * @return
     */
    List<ProductObjVO> selectObjsByProductId(@Param("productId") Long productId);

    /**
     * 获取产品生产订单
     *
     * @param orderIds
     * @return
     */
    List<ProductOrder> selectOrderByIds(@Param("orderIds") List<Long> orderIds);

    /**
     * 获取产品生产订单
     *
     * @param orderId
     * @return
     */
    ProductOrder selectOrderById(@Param("orderId") Long orderId);

    /**
     * 获取产品损耗订单
     *
     * @param orderIds
     * @return
     */
    List<ProductOrderLoss> selectProductOrderLossByOrderIds(@Param("orderIds") List<Long> orderIds);

    /**
     * 获取产品状态列表
     *
     * @param param
     * @return
     */
    List<ProductStatusVO> selectProductStatusList(@Param("param") ProductStatusParam param);

    /**
     * 查询生产订单的产品明细
     *
     * @param orderId
     * @return
     */
    List<ProductCraftQo> getProductCraftByOrderId(@Param("orderId") Long orderId);

    /**
     * 查询生产订单已使用的产品
     *
     * @param orderId
     * @return
     */
    List<ProductObjUsedVO> getUsedProductInOder(@Param("orderId") Long orderId);

    /**
     * 获取产品的销售数量
     *
     * @param productIds
     * @return
     */
    List<ProductSaleQuantityDTO> getProductSaleQuantity(@Param("year") Integer year, @Param("productIds") List<Long> productIds);

    /**
     * 获取产品检验责任人选项
     *
     * @return
     */
    List<String> getAllProductCheckUser();
}