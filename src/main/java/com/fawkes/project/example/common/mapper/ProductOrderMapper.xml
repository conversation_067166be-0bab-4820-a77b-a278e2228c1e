<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.fawkes.project.example.common.mapper.ProductOrderMapper">
    <resultMap id="ProductObjVO" type="com.fawkes.project.example.domain.vo.ProductObjVO"
               extends="com.fawkes.project.example.common.mapper.BaseMapper.BaseEntity">
        <result column="order_id" jdbcType="BIGINT" property="orderId"/>
        <result column="craft_id" jdbcType="BIGINT" property="craftId"/>
        <result column="object_no" jdbcType="VARCHAR" property="objectNo"/>
        <result column="status" jdbcType="INTEGER" property="status"/>
        <result column="sale_order_id" jdbcType="BIGINT" property="saleOrderId"/>
        <result column="use_order_id" jdbcType="BIGINT" property="useOrderId"/>
        <result column="return_type" jdbcType="VARCHAR" property="returnType"/>
    </resultMap>
    <resultMap id="ProductOrder" type="com.fawkes.project.example.common.model.ProductOrder"
               extends="com.fawkes.project.example.common.mapper.BaseMapper.BaseEntity">
        <result column="order_no" jdbcType="VARCHAR" property="orderNo"/>
        <result column="order_time" jdbcType="DATE" property="orderTime"/>
        <result column="produce_user_name" jdbcType="VARCHAR" property="produceUserName"/>
        <result column="check_user_id" jdbcType="BIGINT" property="checkUserId"/>
        <result column="produce_user_id" jdbcType="BIGINT" property="produceUserId"/>
        <result column="order_year" jdbcType="INTEGER" property="orderYear"/>
        <result column="order_batch" jdbcType="VARCHAR" property="orderBatch"/>
        <result column="expect_check_finish_time" jdbcType="DATE" property="expectCheckFinishTime"/>
    </resultMap>
    <resultMap id="ProductOrderLoss" type="com.fawkes.project.example.common.model.ProductOrderLoss"
               extends="com.fawkes.project.example.common.mapper.BaseMapper.BaseEntity">
        <result column="order_id" jdbcType="BIGINT" property="orderId"/>
        <result column="accessory_id" jdbcType="BIGINT" property="accessoryId"/>
        <result column="loss_quantity" jdbcType="INTEGER" property="lossQuantity"/>
        <result column="loss_user_id" jdbcType="BIGINT" property="lossUserId"/>
        <result column="loss_user_name" jdbcType="VARCHAR" property="lossUserName"/>
        <result column="accessory_no" jdbcType="VARCHAR" property="accessoryNo"/>
        <result column="accessory_name" jdbcType="VARCHAR" property="accessoryName"/>
        <result column="accessory_type" jdbcType="VARCHAR" property="accessoryType"/>
        <result column="accessory_spec" jdbcType="VARCHAR" property="accessorySpec"/>
    </resultMap>
    <resultMap id="ProductStatusVO" type="com.fawkes.project.example.domain.vo.ProductStatusVO">
        <result column="objectNo" jdbcType="VARCHAR" property="objectNo"/>
        <result column="productMode" jdbcType="VARCHAR" property="productMode"/>
        <result column="productName" jdbcType="VARCHAR" property="productName"/>
        <result column="useStatus" jdbcType="VARCHAR" property="useStatus"/>
        <result column="status" jdbcType="INTEGER" property="status"/>
    </resultMap>
    <resultMap id="ProductCraftQo" type="com.fawkes.project.example.domain.param.ProductCraftQo">
        <result column="craft_id" jdbcType="BIGINT" property="craftId"/>
        <result column="num" jdbcType="INTEGER" property="num"/>
        <result column="product_name" jdbcType="VARCHAR" property="productName"/>
        <result column="product_model" jdbcType="VARCHAR" property="productModel"/>
        <result column="craft_code" jdbcType="VARCHAR" property="craftCode"/>
    </resultMap>
    <resultMap id="ProductObjUsedVO" type="com.fawkes.project.example.domain.vo.ProductObjUsedVO">
        <result column="id" jdbcType="BIGINT" property="id"/>
        <result column="object_no" jdbcType="VARCHAR" property="objectNo"/>
        <result column="order_type" jdbcType="VARCHAR" property="orderType"/>
        <result column="order_name" jdbcType="VARCHAR" property="orderName"/>
    </resultMap>
    <resultMap id="ProductSaleQuantityDTO" type="com.fawkes.project.example.domain.dto.ProductSaleQuantityDTO">
        <result column="product_id" jdbcType="BIGINT" property="productId"/>
        <result column="quantity" jdbcType="VARCHAR" property="quantity"/>
    </resultMap>

    <select id="isExistedOrderBatch" resultType="boolean">
        SELECT COUNT( * ) FROM product_order
        WHERE delete_flag = 0 AND order_year = #{order.orderYear} AND order_batch = #{order.orderBatch}
        <if test="order.id != null">AND id != #{order.id}</if>
    </select>

    <select id="list" resultType="com.fawkes.project.example.common.model.ProductOrder">
        SELECT
        a.id AS id,
        a.create_by AS createBy,
        a.create_date AS createDate,
        a.update_by AS updateBy,
        a.update_date AS updateDate,
        a.order_no AS orderNo,
        a.order_time AS orderTime,
        a.order_year AS orderYear,
        a.order_batch AS orderBatch,
        a.produce_user_id AS produceUserId,
        a.produce_user_name AS produceUserName,
        a.check_user_id AS checkUserId,
        a.check_user_name AS checkUserName,
        a.expect_check_finish_time AS expectCheckFinishTime,

        CASE
        WHEN SUM(CASE WHEN b.status = 0 THEN 1 ELSE 0 END) = COUNT(*) THEN 0
        WHEN SUM(CASE WHEN b.status != 0 THEN 1 ELSE 0 END) = 0 THEN 0
        WHEN SUM(CASE WHEN b.status = 0 THEN 1 ELSE 0 END) > 0 THEN 1
        ELSE 2
        END AS checkStatus

        FROM
        product_order a
        LEFT JOIN product_object b ON a.id = b.order_id
        <where>
            a.delete_flag = 0
            <if test="qo.orderYear != null and qo.orderYear != ''">and order_year = #{qo.orderYear}</if>
            <if test="qo.orderNo != null and qo.orderNo != ''">and order_no like CONCAT('%', #{qo.orderNo}, '%')</if>
            <if test="qo.orderBatch != null and qo.orderBatch != ''">and order_batch like CONCAT('%',
                #{qo.orderBatch}, '%')
            </if>
            <if test="qo.produceUserName != null and qo.produceUserName != ''">and produce_user_name like CONCAT('%',
                #{qo.produceUserName}, '%')
            </if>
            <if test="qo.checkUserName != null and qo.checkUserName != ''">and check_user_name like CONCAT('%',
                #{qo.checkUserName}, '%')
            </if>
            <if test="qo.checkUserId != null">and check_user_id = #{qo.checkUserId}</if>
            <if test="qo.orderStartTime != null and qo.orderStartTime != ''">
                and order_time >= #{qo.orderStartTime}
            </if>
            <if test="qo.orderEndTime != null and qo.orderEndTime != ''">
                <![CDATA[ AND order_time <= #{qo.orderEndTime} ]]>
            </if>

            <!-- 根据产品编号筛选 -->
            <if test="qo.objectNo != null and qo.objectNo != ''">
                and b.object_no like CONCAT('%', #{qo.objectNo}, '%')
            </if>
            <!-- 根据订单检验状态筛选 -->
            <if test="qo.checkStatus != null">
                AND a.id IN (
                SELECT order_id
                FROM product_object
                GROUP BY order_id
                HAVING
                CASE
                WHEN SUM(CASE WHEN status = 0 THEN 1 ELSE 0 END) = COUNT(*) THEN 0
                WHEN SUM(CASE WHEN status != 0 THEN 1 ELSE 0 END) = 0 THEN 0
                WHEN SUM(CASE WHEN status = 0 THEN 1 ELSE 0 END) > 0 THEN 1
                ELSE 2
                END = #{qo.checkStatus}
                )
            </if>
        </where>
        GROUP BY a.id,
        a.id,
        a.create_by,
        a.create_date,
        a.update_by,
        a.update_date,
        a.order_no,
        a.order_time,
        a.order_year,
        a.order_batch,
        a.produce_user_id,
        a.produce_user_name,
        a.check_user_id,
        a.check_user_name,
        a.expect_check_finish_time,
        b.status
        ORDER BY a.update_date DESC
    </select>

    <insert id="add" parameterType="com.fawkes.project.example.common.model.ProductOrder">
        INSERT INTO product_order (id, create_by, create_date, update_by, update_date, delete_flag, order_no,
                                   order_time, order_year, order_batch, produce_user_id, produce_user_name,
                                   check_user_id, check_user_name, expect_check_finish_time)
        VALUES (#{order.id},
                #{order.createBy},
                #{order.createDate},
                #{order.updateBy},
                #{order.updateDate},
                #{order.deleteFlag},
                #{order.orderNo},
                #{order.orderTime},
                #{order.orderYear},
                #{order.orderBatch},
                #{order.produceUserId},
                #{order.produceUserName},
                #{order.checkUserId},
                #{order.checkUserName},
                #{order.expectCheckFinishTime})
    </insert>

    <update id="update" parameterType="com.fawkes.project.example.common.model.ProductOrder">
        UPDATE
            product_order
        SET create_by                = #{order.createBy},
            create_date              = #{order.createDate},
            update_by                = #{order.updateBy},
            update_date              = #{order.updateDate},
            delete_flag              = #{order.deleteFlag},
            order_no                 = #{order.orderNo},
            order_time               = #{order.orderTime},
            order_year               = #{order.orderYear},
            order_batch              = #{order.orderBatch},
            produce_user_id          = #{order.produceUserId},
            produce_user_name        = #{order.produceUserName},
            check_user_id            = #{order.checkUserId},
            check_user_name          = #{order.checkUserName},
            expect_check_finish_time = #{order.expectCheckFinishTime}
        WHERE id = #{order.id}
    </update>

    <select id="getMaxBatch" parameterType="int" resultType="int">
        SELECT max(order_batch)
        FROM product_order
        WHERE order_year = #{year}
    </select>

    <select id="accessoryUseInfo" resultType="com.fawkes.project.example.common.model.Accessory">
        SELECT
        a.craft_id AS craftId,
        a.accessory_id AS accessoryId,
        a.quantity,
        b.accessory_type AS accessoryType,
        b.accessory_spec AS accessorySpec,
        CONCAT( c.dict_name, '-', d.dict_name ) AS accessoryName,
        CONCAT( c.dict_code, '-', d.dict_code ) AS accessoryNo
        FROM
        product_craft_compose a
        LEFT JOIN accessory b ON a.accessory_id = b.id
        LEFT JOIN accessory_dict c ON b.category_id = c.id
        LEFT JOIN accessory_dict d ON b.ref_id = d.id
        WHERE
        a.delete_flag = 0
        and b.delete_flag = 0
        and a.craft_id IN
        <foreach collection="qos" item="qo" separator=", " open="(" close=")">
            #{qo.craftId}
        </foreach>
    </select>

    <update id="del">
        UPDATE
        product_order
        SET delete_flag = -1
        WHERE
        id IN
        <foreach collection="ids" item="id" separator=", " open="(" close=")">
            #{id}
        </foreach>
    </update>

    <insert id="addProductObjects" parameterType="com.fawkes.project.example.common.model.ProductObject">
        INSERT INTO
        product_object (id, create_by, create_date, update_by, update_date, delete_flag,
        object_no, order_id, craft_id, status, unqualified_reason, unqualified_pic)
        VALUES
        <foreach collection="productObjects" item="obj" separator=",">
            (
            #{obj.id},
            #{obj.createBy},
            #{obj.createDate},
            #{obj.updateBy},
            #{obj.updateDate},
            #{obj.deleteFlag},
            #{obj.objectNo},
            #{obj.orderId},
            #{obj.craftId},
            #{obj.status},
            #{obj.unqualifiedReason},
            #{obj.unqualifiedPic}
            )
        </foreach>
    </insert>

    <select id="getCheckDetail" resultType="com.fawkes.project.example.domain.vo.CheckDetailVo">
        SELECT
        a.id AS objectId,
        b.product_id AS productId,
        a.object_no AS objectNo,
        c.name AS productName,
        c.model AS productModel,
        a.status,
        b.craft_code AS craftCode,
        a.actual_finish_time AS actualFinishTime,
        a.unqualified_reason AS unqualifiedReason,
        a.unqualified_pic AS unqualifiedPic,
        a.attach,
        d.expect_check_finish_time AS expectCheckFinishTime,
        d.order_no AS orderNo
        FROM
        product_object a
        LEFT JOIN product_craft b ON a.craft_id = b.id
        LEFT JOIN product c ON b.product_id = c.id
        LEFT JOIN product_order d ON d.id = a.order_id
        <if test="orderId != null">
            WHERE
            a.order_id = #{orderId}
        </if>
        <if test="year != null">
            WHERE YEAR ( d.create_date ) = #{year}
        </if>
    </select>

    <update id="productCheck" parameterType="com.fawkes.project.example.domain.param.CheckQo">
        UPDATE product_object
        <set>
            <if test="qo.status != null">
                status = #{qo.status},
                actual_finish_time = NOW(),
            </if>
            <if test="qo.unqualifiedReason != null and qo.unqualifiedReason != ''">
                unqualified_reason = #{qo.unqualifiedReason},
            </if>
            <if test="qo.unqualifiedPic != null and qo.unqualifiedPic != ''">
                unqualified_pic = #{qo.unqualifiedPic},
            </if>
            <if test="qo.attach != null and qo.attach != ''">
                attach = #{qo.attach},
            </if>
        </set>
        WHERE
        id = #{checkId}
    </update>

    <select id="getCalibByProducts" resultType="com.fawkes.project.example.common.model.CheckCalib">
        SELECT
        id,
        id AS calibId,
        product_id AS productId,
        calib_code AS calibCode
        FROM
        product_calib
        <if test="vos != null and vos.size() > 0">
            WHERE
            product_id IN
            <foreach collection="vos" item="vo" separator=", " open="(" close=")">
                #{vo.productId}
            </foreach>
        </if>
    </select>

    <select id="getCalibValByObjs" resultType="com.fawkes.project.example.common.model.CheckCalib">
        SELECT
        a.object_id AS objectId,
        a.calib_id AS calibId,
        a.calib_value AS calibValue,
        b.calib_code AS calibCode
        FROM
        product_order_check_calib a
        LEFT JOIN product_calib b ON a.calib_id = b.id
        WHERE
        a.object_id IN
        <foreach collection="vos" item="vo" separator=", " open="(" close=")">
            #{vo.objectId}
        </foreach>
    </select>

    <delete id="delCalib" parameterType="com.fawkes.project.example.common.model.CheckCalib">
        DELETE
        FROM product_order_check_calib
        WHERE object_id = #{calib.objectId}
          AND calib_id = #{calib.calibId}
    </delete>

    <insert id="addCalib" parameterType="com.fawkes.project.example.common.model.CheckCalib">
        INSERT INTO
        product_order_check_calib (id, create_by, create_date, update_by, update_date, delete_flag,
        object_id, calib_id, calib_value)
        VALUES
        <foreach collection="calibs" item="obj" separator=",">
            (
            #{obj.id},
            #{obj.createBy},
            #{obj.createDate},
            #{obj.updateBy},
            #{obj.updateDate},
            #{obj.deleteFlag},
            #{obj.objectId},
            #{obj.calibId},
            #{obj.calibValue}
            )
        </foreach>
    </insert>

    <select id="getObjsByOrder" resultType="com.fawkes.project.example.common.model.ProductObject">
        SELECT
        a.order_id AS orderId,
        a.status,
        a.actual_finish_time AS actualFinishTime,
        a.id AS objectId,
        a.object_no AS objectNo,
        a.sale_order_id as saleOrderId,
        b.craft_code AS craftCode,
        c.model AS productModel,
        c.name AS productName
        FROM
        product_object a
        LEFT JOIN product_craft b on a.craft_id = b.id
        LEFT JOIN product c on b.product_id = c.id
        <if test="orderIds != null and orderIds.size() > 0">
            WHERE
            a.order_id IN
            <foreach collection="orderIds" item="orderId" separator=", " open="(" close=")">
                #{orderId}
            </foreach>
        </if>
        <if test="year != null">
            WHERE YEAR ( a.create_date ) = #{year}
        </if>
    </select>

    <select id="getObjsByIds" resultType="com.fawkes.project.example.common.model.ProductObject">
        SELECT
        a.id AS objectId,
        a.object_no AS objectNo,
        a.actual_finish_time AS checkFinishTime,
        c.name AS productName,
        c.model AS productModel,
        cert_type AS certType,
        d.produce_user_name AS produceUserName
        FROM
        product_object a
        LEFT JOIN product_craft b ON a.craft_id = b.id
        LEFT JOIN product c ON b.product_id = c.id
        LEFT JOIN product_order d on a.order_id = d.id
        WHERE
        a.id IN
        <foreach collection="ids" item="id" separator=", " open="(" close=")">
            #{id}
        </foreach>
    </select>

    <insert id="addAccessoryLoss" parameterType="com.fawkes.project.example.common.model.ProductOrderLoss">
        INSERT INTO
        product_order_loss (id, create_by, create_date, update_by, update_date, delete_flag,
        order_id, accessory_id, loss_quantity, loss_user_id, loss_user_name)
        VALUES
        <foreach collection="losses" item="loss" separator=",">
            (
            #{loss.id},
            #{loss.createBy},
            #{loss.createDate},
            #{loss.updateBy},
            #{loss.updateDate},
            #{loss.deleteFlag},
            #{orderId},
            #{loss.accessoryId},
            #{loss.lossQuantity},
            #{loss.lossUserId},
            #{loss.lossUserName}
            )
        </foreach>
    </insert>

    <delete id="delAccessoryLoss">
        DELETE
        FROM product_order_loss
        WHERE order_id = #{orderId}
    </delete>

    <select id="getAccessoryLossByOrderId" resultType="com.fawkes.project.example.domain.vo.AccessoryUseInfoVo">
        SELECT a.loss_quantity                       AS quantity,
               a.loss_user_name                      AS lossUserName,
               CONCAT(c.dict_code, '-', d.dict_code) AS accessoryNo,
               CONCAT(c.dict_name, '-', d.dict_name) AS accessoryName,
               b.accessory_type                      AS accessoryType,
               b.accessory_spec                      AS accessorySpec
        FROM product_order_loss a
                 LEFT JOIN accessory b ON a.accessory_id = b.id
                 LEFT JOIN accessory_dict c ON b.category_id = c.id
                 LEFT JOIN accessory_dict d ON b.ref_id = d.id
        WHERE a.order_id = #{orderId}
    </select>

    <select id="getAccessoryLossInfoByOrderId" resultMap="ProductOrderLoss">
        SELECT a.*,
               CONCAT(c.dict_code, '-', d.dict_code) AS accessory_no,
               CONCAT(c.dict_name, '-', d.dict_name) AS accessory_name,
               b.accessory_type                      AS accessory_type,
               b.accessory_spec                      AS accessory_spec
        FROM product_order_loss a
                 LEFT JOIN accessory b ON a.accessory_id = b.id
                 LEFT JOIN accessory_dict c ON b.category_id = c.id
                 LEFT JOIN accessory_dict d ON b.ref_id = d.id
        WHERE a.order_id = #{orderId}
    </select>

    <select id="getAccessoryUseInfoByOrderId" resultType="com.fawkes.project.example.domain.vo.AccessoryUseInfoVo">
        SELECT b.accessory_id                        AS accessoryId,
               CONCAT(c.dict_code, '-', d.dict_code) AS accessoryNo,
               CONCAT(c.dict_name, '-', d.dict_name) AS accessoryName,
               f.accessory_type                      AS accessoryType,
               f.accessory_spec                      AS accessorySpec
        FROM product_object a
                 INNER JOIN product_craft_compose b ON a.craft_id = b.craft_id
                 LEFT JOIN accessory f ON b.accessory_id = f.id
                 LEFT JOIN accessory_dict c ON f.category_id = c.id
                 LEFT JOIN accessory_dict d ON f.ref_id = d.id
        WHERE a.order_id = #{orderId}
    </select>

    <select id="getBatchNum" resultType="int">
        SELECT
        count( * )
        FROM
        product_order
        WHERE
        delete_flag = 0 AND order_batch IS NOT NULL
        <if test="isCurYear == true">
            AND order_year = YEAR ( CURDATE( ) )
        </if>
    </select>

    <select id="getProductNum" resultType="int">
        SELECT
        count( a.id )
        FROM
        product_object a
        LEFT JOIN product_order b ON a.order_id = b.id
        WHERE
        b.order_batch IS NOT NULL
        <if test="isCurYear == true">
            AND b.order_year = YEAR ( CURDATE( ) )
        </if>
    </select>

    <select id="getById" resultType="com.fawkes.project.example.domain.vo.ProductOrderDetailVo">
        SELECT order_no                 AS orderNo,
               order_time               AS orderTime,
               order_year               AS orderYear,
               order_batch              AS orderBatch,
               produce_user_name        AS produceUserName,
               check_user_name          AS checkUserName,
               expect_check_finish_time AS expectCheckFinishTime
        FROM product_order
        WHERE id = #{orderId}
    </select>

    <select id="getProductOrderById" resultMap="ProductOrder">
        select *
        FROM product_order
        WHERE id = #{orderId}
    </select>

    <delete id="delObjsByOrderIds">
        DELETE
        FROM
        product_object
        WHERE
        order_id IN
        <foreach collection="orderIds" item="orderId" separator=", " open="(" close=")">
            #{orderId}
        </foreach>
    </delete>

    <select id="getCategoryCodeByCraft" resultType="string">
        SELECT b.category_code AS ss
        FROM product_craft a
                 LEFT JOIN product b ON a.product_id = b.id
        WHERE a.id = #{craftId}
    </select>

    <select id="getObjMaxNoByPrefix" resultType="string">
        SELECT MAX(object_no) AS max_code
        FROM product_object
        WHERE delete_flag = 0
          AND object_no LIKE CONCAT(#{objNoPrefix}, '%');
    </select>


    <select id="productInventory" resultType="com.fawkes.project.example.common.model.ProductObject">
        SELECT
        a.object_no AS objectNo,
        a.status,
        a.out_date AS outDate,
        CASE
        WHEN ( a.sale_order_id IS NOT NULL OR a.use_order_id IS NOT NULL ) THEN
        1 ELSE 0
        END AS outStatus,
        c.name AS productName,
        c.model AS productModel,
        c.id AS productId
        FROM
        product_object a
        LEFT JOIN product_craft b ON a.craft_id = b.id
        LEFT JOIN product c ON b.product_id = c.id
        <where>
            1 = 1
            <if test="applyStatus != null">
                AND b.apply_status = #{applyStatus}
            </if>
        </where>
    </select>

    <select id="selectObjsByProductId" resultMap="ProductObjVO">
        SELECT a.*
        FROM product_object a
                 LEFT JOIN product_craft b ON b.id = a.craft_id
        WHERE a.delete_flag = 0
          AND b.product_id = #{productId}
    </select>

    <select id="selectOrderByIds" resultMap="ProductOrder">
        SELECT * FROM product_order
        WHERE id IN
        <foreach collection="orderIds" item="orderId" separator="," open="(" close=")">#{orderId}</foreach>
    </select>

    <select id="selectOrderById" resultMap="ProductOrder">
        SELECT *
        FROM product_order
        WHERE id = #{orderId}
    </select>

    <select id="selectProductOrderLossByOrderIds" resultMap="ProductOrderLoss">
        select * from product_order_loss
        WHERE order_id IN
        <foreach collection="orderIds" item="orderId" separator="," open="(" close=")">#{orderId}</foreach>
    </select>

    <select id="selectProductStatusList" parameterType="com.fawkes.project.example.domain.param.ProductStatusParam"
            resultMap="ProductStatusVO">
        SELECT
        d.model AS productModel,
        b.object_no as objectNo,
        d.name AS productName,
        b.status,
        CASE
        WHEN (ISNULL( b.sale_order_id ) AND ISNULL( b.use_order_id )) THEN '未使用'
        ELSE '已使用'
        END AS useStatus
        FROM
        product_order a
        LEFT JOIN product_object b ON a.id = b.order_id
        AND b.delete_flag = 0
        LEFT JOIN product_craft c ON b.craft_id = c.id
        LEFT JOIN product d ON d.id = c.product_id
        WHERE
        a.delete_flag = 0 AND b.delete_flag = 0
        <if test="param.productName != null and param.productName != ''">
            AND d.name LIKE CONCAT( '%', #{param.productName}, '%' )
        </if>
        <if test="param.productModel != null and param.productModel != ''">
            AND d.model LIKE CONCAT( '%', #{param.productModel}, '%' )
        </if>
        <if test="param.objectNo != null and param.objectNo != ''">
            AND b.object_no LIKE CONCAT( '%', #{param.objectNo}, '%' )
        </if>
        <if test="param.useStatus != null and param.useStatus == '未使用'">
            AND (ISNULL( b.sale_order_id ) AND ISNULL( b.use_order_id ))
        </if>
        <if test="param.useStatus != null and param.useStatus == '已使用'">
            AND (b.sale_order_id is not null OR b.use_order_id is not null)
        </if>
        <if test="param.status != null">
            AND b.status = #{param.status}
        </if>
        Order by b.create_date DESC, b.object_no ASC
    </select>

    <select id="getProductCraftByOrderId" resultMap="ProductCraftQo">
        SELECT a.craft_id,
               b.craft_code,
               c.model  as product_model,
               c.name as product_name,
               COUNT(*) AS num
        FROM product_object a
                 LEFT JOIN product_craft b on a.craft_id = b.id
                 LEFT JOIN product c on b.product_id = c.id
        WHERE a.order_id = #{orderId,jdbcType=BIGINT}
        GROUP BY a.craft_id
    </select>

    <select id="getUsedProductInOder" resultMap="ProductObjUsedVO">
        SELECT a.id,
               a.object_no,
               CASE

                   WHEN a.sale_order_id IS NOT NULL THEN
                       'type'
                   WHEN a.use_order_id IS NOT NULL THEN
                       'use'
                   ELSE ''
                   END AS order_type,
               CASE

                   WHEN a.sale_order_id IS NOT NULL THEN
                       b.order_name
                   WHEN a.use_order_id IS NOT NULL THEN
                       c.order_name
                   ELSE ''
                   END AS order_name
        FROM product_object a
                 LEFT JOIN sale_order b ON b.id = a.sale_order_id
                 LEFT JOIN use_order c ON a.use_order_id = c.id
        WHERE a.delete_flag = 0
          AND a.order_id = #{orderId,jdbcType=BIGINT}
          AND (
                a.sale_order_id IS NOT NULL
                OR a.use_order_id IS NOT NULL)
    </select>

    <select id="getProductSaleQuantity" resultMap="ProductSaleQuantityDTO">
        SELECT
        c.product_id,
        IFNULL( sum( b.sale_quantity ), 0 ) AS quantity
        FROM
        sale_order a
        LEFT JOIN sale_order_goods b ON b.order_id = a.id
        LEFT JOIN product_craft c ON c.id = b.craft_id
        <where>
            a.delete_flag = 0 AND b.delete_flag = 0
            <if test="year != null and year != 0">
                AND YEAR ( a.sign_date ) = #{year}
            </if>
            AND c.product_id IN
            <foreach collection="productIds" item="productId" separator="," open="(" close=")">#{productId}</foreach>
            GROUP BY
            c.product_id
        </where>
    </select>

    <select id="getAllProductCheckUser" resultType="string">
        SELECT DISTINCT
        check_user_name
        FROM
        product_order
        <where>
            delete_flag = 0
            AND check_user_name IS NOT NULL
            <![CDATA[ AND check_user_name <> '' ]]>
        </where>
    </select>

</mapper>