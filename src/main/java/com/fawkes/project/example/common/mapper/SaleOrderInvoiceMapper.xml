<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.fawkes.project.example.common.mapper.SaleOrderInvoiceMapper">
    <resultMap id="SaleOrderInvoice" type="com.fawkes.project.example.common.model.SaleOrderInvoice"
               extends="com.fawkes.project.example.common.mapper.BaseMapper.BaseEntity">
        <result column="invoice_no" jdbcType="VARCHAR" property="invoiceNo"/>
        <result column="operator_id" jdbcType="BIGINT" property="operatorId"/>
        <result column="operator_name" jdbcType="VARCHAR" property="operatorName"/>
        <result column="invoice_date" jdbcType="DATE" property="invoiceDate"/>
        <result column="pay_date" jdbcType="DATE" property="payDate"/>
        <result column="invoice_amount" jdbcType="DECIMAL" property="invoiceAmount"/>
        <result column="tax_rate" jdbcType="DECIMAL" property="taxRate"/>
        <result column="invoice_type" jdbcType="CHAR" property="invoiceType"/>
        <result column="order_id" jdbcType="BIGINT" property="orderId"/>
        <result column="invoice_enclosure" jdbcType="LONGVARCHAR" property="invoiceEnclosure"/>
    </resultMap>
    <sql id="Column_List_SaleOrderInvoice">
        id
        , create_by, create_date, update_by, update_date, delete_flag, invoice_no, operator_id,
    operator_name, invoice_date, pay_date, invoice_amount, tax_rate, invoice_type, order_id, invoice_enclosure
    </sql>

    <select id="isExistedInvoiceNo" resultType="java.lang.Boolean">
        SELECT COUNT(*)
        FROM sale_order_invoice WHERE delete_flag = 0 AND order_id = #{invoice.orderId} AND TRIM( invoice_no ) = TRIM(
        #{invoice.invoiceNo} )
        <if test="invoice.id != null">AND id != #{invoice.id}</if>
    </select>

    <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="SaleOrderInvoice">
        select
        <include refid="Column_List_SaleOrderInvoice"/>
        from sale_order_invoice where id = #{id,jdbcType=BIGINT}
    </select>

    <select id="selectByOrderId" parameterType="java.lang.Long" resultMap="SaleOrderInvoice">
        select
        <include refid="Column_List_SaleOrderInvoice"/>
        from sale_order_invoice where delete_flag = 0 AND order_id = #{orderId,jdbcType=BIGINT}
    </select>

    <insert id="insert" parameterType="com.fawkes.project.example.common.model.SaleOrderInvoice">
        insert into sale_order_invoice (id, create_by, create_date,
                                        update_by, update_date, delete_flag,
                                        invoice_no, operator_id, operator_name,
                                        invoice_date, pay_date, invoice_amount,
                                        tax_rate, invoice_type, order_id,
                                        invoice_enclosure)
        values (#{id,jdbcType=BIGINT}, #{createBy,jdbcType=VARCHAR}, #{createDate,jdbcType=TIMESTAMP},
                #{updateBy,jdbcType=VARCHAR}, #{updateDate,jdbcType=TIMESTAMP}, #{deleteFlag,jdbcType=INTEGER},
                TRIM(#{invoiceNo,jdbcType=VARCHAR}), #{operatorId,jdbcType=BIGINT}, #{operatorName,jdbcType=VARCHAR},
                #{invoiceDate,jdbcType=DATE}, #{payDate,jdbcType=DATE}, #{invoiceAmount,jdbcType=DECIMAL},
                #{taxRate,jdbcType=DECIMAL}, #{invoiceType,jdbcType=CHAR}, #{orderId,jdbcType=BIGINT},
                #{invoiceEnclosure,jdbcType=LONGVARCHAR})
    </insert>

    <update id="updateByPrimaryKey">
        update sale_order_invoice
        set update_by         = #{invoice.updateBy,jdbcType=VARCHAR},
            update_date       = #{invoice.updateDate,jdbcType=TIMESTAMP},
            invoice_no        = #{invoice.invoiceNo,jdbcType=VARCHAR},
            operator_id       = #{invoice.operatorId,jdbcType=BIGINT},
            operator_name     = #{invoice.operatorName,jdbcType=VARCHAR},
            invoice_date      = #{invoice.invoiceDate,jdbcType=DATE},
            pay_date          = #{invoice.payDate,jdbcType=DATE},
            invoice_amount    = #{invoice.invoiceAmount,jdbcType=DECIMAL},
            tax_rate          = #{invoice.taxRate,jdbcType=DECIMAL},
            invoice_type      = #{invoice.invoiceType,jdbcType=CHAR},
            order_id          = #{invoice.orderId,jdbcType=BIGINT},
            invoice_enclosure = #{invoice.invoiceEnclosure,jdbcType=LONGVARCHAR}
        where id = #{invoice.id,jdbcType=BIGINT}
    </update>

    <update id="deleteByPrimaryKey">
        update sale_order_invoice
        set update_by   = #{updateInfo.updateBy,jdbcType=VARCHAR},
            update_date = #{updateInfo.updateDate,jdbcType=TIMESTAMP},
            delete_flag = -1
        where id = #{id,jdbcType=BIGINT}
    </update>
</mapper>