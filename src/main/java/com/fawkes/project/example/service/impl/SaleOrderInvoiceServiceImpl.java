package com.fawkes.project.example.service.impl;

import com.fawkes.core.base.api.ApiResponseBody;
import com.fawkes.core.utils.EntityTool;
import com.fawkes.project.example.common.exception.BizDmException;
import com.fawkes.project.example.common.mapper.SaleOrderInvoiceMapper;
import com.fawkes.project.example.common.model.SaleOrderInvoice;
import com.fawkes.project.example.common.utils.MyEntityTool;
import com.github.pagehelper.PageInfo;
import com.google.common.base.Strings;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR>
 */
@Service
@Slf4j
public class SaleOrderInvoiceServiceImpl {
    @Resource
    SaleOrderInvoiceMapper saleOrderInvoiceMapper;

    public ApiResponseBody page(String orderId) {
        List<SaleOrderInvoice> invoiceList = saleOrderInvoiceMapper.selectByOrderId(Long.parseLong(orderId));
        return ApiResponseBody.defaultSuccess(new PageInfo<>(invoiceList));
    }

    @Transactional(rollbackFor = Exception.class)
    public ApiResponseBody add(SaleOrderInvoice orderInvoice) {
        checkInvoice(orderInvoice);
        EntityTool.insertEntity(orderInvoice);
        saleOrderInvoiceMapper.insert(orderInvoice);
        return ApiResponseBody.defaultSuccess();
    }

    public ApiResponseBody query(String id) {
        return ApiResponseBody.defaultSuccess(saleOrderInvoiceMapper.selectByPrimaryKey(Long.parseLong(id)));
    }

    @Transactional(rollbackFor = Exception.class)
    public ApiResponseBody delete(String id) {
        saleOrderInvoiceMapper.deleteByPrimaryKey(Long.parseLong(id), MyEntityTool.getUpdateInfo());
        return ApiResponseBody.defaultSuccess();
    }

    @Transactional(rollbackFor = Exception.class)
    public ApiResponseBody update(SaleOrderInvoice orderInvoice) {
        checkInvoice(orderInvoice);
        EntityTool.updateEntity(orderInvoice);
        saleOrderInvoiceMapper.updateByPrimaryKey(orderInvoice);
        return ApiResponseBody.defaultSuccess();
    }

    private void checkInvoice(SaleOrderInvoice orderInvoice) {
        if (Strings.isNullOrEmpty(orderInvoice.getInvoiceNo())) {
            throw new BizDmException("请输入发票编号");
        }
        if (Objects.isNull(orderInvoice.getOrderId())) {
            throw new BizDmException("请选择销售订单");
        }
        if (saleOrderInvoiceMapper.isExistedInvoiceNo(orderInvoice)) {
            throw new BizDmException("发票编号已存在");
        }
    }
}
