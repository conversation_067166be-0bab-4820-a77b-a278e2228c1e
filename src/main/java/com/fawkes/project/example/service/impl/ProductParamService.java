package com.fawkes.project.example.service.impl;

import com.fawkes.core.base.api.ApiResponseBody;
import com.fawkes.core.utils.EntityTool;
import com.fawkes.project.example.common.exception.BizDmException;
import com.fawkes.project.example.common.mapper.ProductParamMapper;
import com.fawkes.project.example.common.model.ProductParam;
import com.fawkes.project.example.common.utils.MyEntityTool;
import com.fawkes.project.example.domain.dto.ProductParamDTO;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * 产品参数管理
 *
 * <AUTHOR>
 */
@Service
@Slf4j
public class ProductParamService {
    @Resource
    ProductParamMapper productParamMapper;

    @Transactional(rollbackFor = Exception.class)
    public ApiResponseBody add(ProductParam productParam) {
        checkParamName(productParam);
        EntityTool.insertEntity(productParam);
        productParamMapper.insert(productParam);
        return ApiResponseBody.defaultSuccess();
    }

    public ApiResponseBody<ProductParamDTO> query(String id) {
        ProductParamDTO productParamDTO = productParamMapper.selectByPrimaryKey(Long.parseLong(id));
        if (!Objects.isNull(productParamDTO)) {
            productParamDTO.setChildren(productParamMapper.selectByParamParentIds(Collections.singletonList(productParamDTO.getId())));
        }
        return ApiResponseBody.defaultSuccess(productParamDTO);
    }

    @Transactional(rollbackFor = Exception.class)
    public ApiResponseBody delete(String id) {
        return ApiResponseBody.defaultSuccess(productParamMapper.deleteById(Long.parseLong(id), MyEntityTool.getUpdateInfo()));
    }

    @Transactional(rollbackFor = Exception.class)
    public ApiResponseBody update(ProductParam productParam) {
        checkParamName(productParam);
        EntityTool.updateEntity(productParam);
        productParamMapper.updateByPrimaryKey(productParam);
        return ApiResponseBody.defaultSuccess();
    }

    public ApiResponseBody<PageInfo<ProductParamDTO>> page(Integer pageNum, Integer pageSize, String name) {
        PageHelper.startPage(pageNum, pageSize);
        List<ProductParamDTO> productParamDTOList = productParamMapper.selectProductParamList(name);
        if (!CollectionUtils.isEmpty(productParamDTOList)) {
            List<ProductParam> productParamList = productParamMapper.selectByParamParentIds(productParamDTOList.stream().map(ProductParamDTO::getId).collect(Collectors.toList()));
            if (!CollectionUtils.isEmpty(productParamList)) {
                Map<Long, List<ProductParam>> paramMap = productParamList.stream().collect(Collectors.groupingBy(ProductParam::getParamParent));
                productParamDTOList.forEach(e -> e.setChildren(paramMap.get(e.getId())));
            }
        }
        return ApiResponseBody.defaultSuccess(new PageInfo<>(productParamDTOList));
    }

    private void checkParamName(ProductParam param) {
        if (productParamMapper.isExistedParamName(param)) {
            throw new BizDmException("产品参数已存在!");
        }
    }
}
