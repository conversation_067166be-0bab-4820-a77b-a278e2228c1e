package com.fawkes.project.example.service.impl;

import com.fawkes.core.base.api.ApiResponseBody;
import com.fawkes.core.utils.EntityTool;
import com.fawkes.project.example.common.exception.BizDmException;
import com.fawkes.project.example.common.mapper.AccessoryMapper;
import com.fawkes.project.example.common.model.Accessory;
import com.fawkes.project.example.common.utils.MyEntityTool;
import com.fawkes.project.example.domain.vo.AccessoryInventoryVO;
import com.fawkes.project.example.domain.vo.AccessoryVO;
import com.fawkes.project.example.domain.vo.LabelValueExtraVO;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 配件管理
 *
 * <AUTHOR>
 */
@Service
@Slf4j
public class AccessoryService {
    @Resource
    AccessoryMapper accessoryMapper;

    public ApiResponseBody add(Accessory accessory) {
        checkAccessory(accessory);
        EntityTool.insertEntity(accessory);
        accessoryMapper.insert(accessory);
        return ApiResponseBody.defaultSuccess();
    }

    public ApiResponseBody<Accessory> query(String id) {
        Accessory accessory = accessoryMapper.selectByPrimaryKey(Long.parseLong(id));
        log.info("---> [query]: " + accessory);
        return ApiResponseBody.defaultSuccess(accessory);
    }

    @Transactional(rollbackFor = Exception.class)
    public ApiResponseBody delete(String id) {
        // 已使用的配件不可删除：1. 配件入库明细；2. 工艺
        if (accessoryMapper.isUsedInAccessoryOrderById(Long.parseLong(id)) || accessoryMapper.isUsedInCraftById(Long.parseLong(id))) {
            throw new BizDmException("配件已使用！");
        }
        accessoryMapper.deleteById(Long.parseLong(id), MyEntityTool.getUpdateInfo());
        return ApiResponseBody.defaultSuccess();
    }

    @Transactional(rollbackFor = Exception.class)
    public ApiResponseBody update(Accessory accessory) {
        checkAccessory(accessory);
        // 禁止修改零件种类、零件编号、单价
        EntityTool.updateEntity(accessory);
        accessoryMapper.updateByPrimaryKey(accessory);
        return ApiResponseBody.defaultSuccess();
    }

    public ApiResponseBody<PageInfo<AccessoryVO>> page(Integer pageNum, Integer pageSize, String accessoryCode, String accessoryName, String accessoryType) {
        PageHelper.startPage(pageNum, pageSize);
        List<AccessoryVO> accessoryVOList = accessoryMapper.selectAccessoryList(accessoryCode, accessoryName, accessoryType);
        return ApiResponseBody.defaultSuccess(new PageInfo<>(accessoryVOList));
    }

    public ApiResponseBody<PageInfo<AccessoryInventoryVO>> inventoryPage(Integer pageNum, Integer pageSize, String accessoryCode, String accessoryName, String accessoryType, String accessorySupplier) {
        PageHelper.startPage(pageNum, pageSize);
        List<AccessoryInventoryVO> accessoryInventoryVOList = accessoryMapper.selectAccessoryInventoryList(accessoryCode, accessoryName, accessoryType, accessorySupplier);
        if (!CollectionUtils.isEmpty(accessoryInventoryVOList)) {
            // 获取配件使用情况
            Map<Long, Integer> accessoryUseMap = accessoryMapper.getAccessoryUseList().stream().collect(Collectors.toMap(AccessoryInventoryVO::getAccessoryId, AccessoryInventoryVO::getTotalUseQuantity, (key1, key2) -> key1));
            Map<Long, Integer> accessoryLossMap = accessoryMapper.getAccessoryLossList().stream().collect(Collectors.toMap(AccessoryInventoryVO::getAccessoryId, AccessoryInventoryVO::getTotalLossQuantity, (key1, key2) -> key1));
            accessoryInventoryVOList.forEach(e -> {
                Integer useNum = accessoryUseMap.get(e.getId());
                Integer lossNum = accessoryLossMap.get(e.getId());
                if (useNum == null) {
                    useNum = 0;
                }
                if (lossNum == null) {
                    lossNum = 0;
                }
                if (e.getTotalArrivalQuantity() == null) {
                    e.setTotalArrivalQuantity(0);
                }
                e.setTotalUseQuantity(useNum);
                e.setTotalLossQuantity(lossNum);
                e.setInventoryQuantity(e.getTotalArrivalQuantity() < (useNum + lossNum) ? 0 : e.getTotalArrivalQuantity() - (useNum + lossNum));
            });
        }
        return ApiResponseBody.defaultSuccess(new PageInfo<>(accessoryInventoryVOList));
    }

    public ApiResponseBody<List<LabelValueExtraVO>> inventoryTree() {
        List<LabelValueExtraVO> list = new ArrayList<>();
        List<AccessoryInventoryVO> accessoryInventoryVOList = accessoryMapper.selectAccessoryInventoryList(null, null, null, null);
        if (!CollectionUtils.isEmpty(accessoryInventoryVOList)) {
            // 获取配件使用情况
            Map<Long, Integer> accessoryUseMap = accessoryMapper.getAccessoryUseList().stream().collect(Collectors.toMap(AccessoryInventoryVO::getAccessoryId, AccessoryInventoryVO::getTotalUseQuantity, (key1, key2) -> key1));
            Map<Long, Integer> accessoryLossMap = accessoryMapper.getAccessoryLossList().stream().collect(Collectors.toMap(AccessoryInventoryVO::getAccessoryId, AccessoryInventoryVO::getTotalLossQuantity, (key1, key2) -> key1));
            accessoryInventoryVOList.forEach(e -> {
                Integer useNum = accessoryUseMap.get(e.getId());
                Integer lossNum = accessoryLossMap.get(e.getId());
                if (useNum == null) {
                    useNum = 0;
                }
                if (lossNum == null) {
                    lossNum = 0;
                }
                if (e.getTotalArrivalQuantity() == null) {
                    e.setTotalArrivalQuantity(0);
                }
                e.setTotalUseQuantity(useNum);
                e.setTotalLossQuantity(lossNum);
                e.setInventoryQuantity(e.getTotalArrivalQuantity() < (useNum + lossNum) ? 0 : e.getTotalArrivalQuantity() - (useNum + lossNum));
            });

            // 配件层级关系
            Map<String, List<AccessoryInventoryVO>> accessoryInventoryMap = accessoryInventoryVOList.stream().collect(Collectors.groupingBy(AccessoryInventoryVO::getCategoryCode));
            accessoryInventoryMap.forEach((key, value) -> {
                LabelValueExtraVO labelValueExtraVO = new LabelValueExtraVO(value.get(0).getCategoryName());
                Map<String, Integer> valueMap = new HashMap<>(2);
                valueMap.put("inventoryQuantity", value.stream().map(AccessoryInventoryVO::getInventoryQuantity).filter(inventoryQuantity -> !Objects.isNull(inventoryQuantity)).reduce(Integer::sum).get());
                valueMap.put("totalNotArrivalQuantity", value.stream().map(AccessoryInventoryVO::getTotalNotArrivalQuantity).filter(totalNotArrivalQuantity -> !Objects.isNull(totalNotArrivalQuantity)).reduce(Integer::sum).get());
                labelValueExtraVO.setValue(valueMap);

                List<LabelValueExtraVO> childList = new ArrayList<>();
                value.forEach(e -> {
                    LabelValueExtraVO child = new LabelValueExtraVO(e.getAccessoryName());
                    Map<String, Integer> childValueMap = new HashMap<>(2);
                    childValueMap.put("inventoryQuantity", e.getInventoryQuantity());
                    childValueMap.put("totalNotArrivalQuantity", e.getTotalNotArrivalQuantity());
                    child.setValue(childValueMap);
                    childList.add(child);
                });
                labelValueExtraVO.setExtra(childList);

                list.add(labelValueExtraVO);
            });
        }
        return ApiResponseBody.defaultSuccess(list);
    }

    private void checkAccessory(Accessory accessory) {
        if (Objects.isNull(accessory.getCategoryId())) {
            throw new BizDmException("配件种类不能为空");
        }
        if (Objects.isNull(accessory.getRefId())) {
            throw new BizDmException("配件编号不能为空");
        }
        if (accessoryMapper.isExistedAccessory(accessory)) {
            throw new BizDmException("配件已存在");
        }
    }
}
