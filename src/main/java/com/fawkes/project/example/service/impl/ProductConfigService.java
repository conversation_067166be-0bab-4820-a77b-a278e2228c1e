package com.fawkes.project.example.service.impl;

import com.fawkes.core.base.api.ApiResponseBody;
import com.fawkes.core.enums.DeleteFlagEnum;
import com.fawkes.core.exception.BusinessException;
import com.fawkes.core.utils.EntityTool;
import com.fawkes.project.example.common.exception.BizDmException;
import com.fawkes.project.example.common.mapper.ProductCalibMapper;
import com.fawkes.project.example.common.mapper.ProductCraftMapper;
import com.fawkes.project.example.common.mapper.ProductMapper;
import com.fawkes.project.example.common.model.Product;
import com.fawkes.project.example.common.model.ProductCalib;
import com.fawkes.project.example.common.model.ProductCraft;
import com.fawkes.project.example.common.utils.MyEntityTool;
import com.fawkes.project.example.common.utils.OrderTool;
import com.fawkes.project.example.domain.dto.ProductDTO;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.google.common.base.Strings;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 产品类型配置
 *
 * <AUTHOR>
 */
@Service
@Slf4j
public class ProductConfigService {
    @Resource
    ProductMapper productMapper;
    @Resource
    ProductCalibMapper productCalibMapper;
    @Resource
    ProductCraftMapper productCraftMapper;

    @Transactional(rollbackFor = Exception.class)
    public ApiResponseBody add(ProductDTO productDTO) {
        checkNameAndModel(productDTO);
        EntityTool.insertEntity(productDTO);
        productMapper.insert(productDTO);
        if (!CollectionUtils.isEmpty(productDTO.getCalibList())) {
            productDTO.getCalibList().forEach(e -> e.setProductId(productDTO.getId()));
            EntityTool.insertEntity(productDTO.getCalibList());
            productCalibMapper.insertBatch(productDTO.getCalibList());
        }
        return ApiResponseBody.defaultSuccess();
    }

    public ApiResponseBody<ProductDTO> query(String id) {
        ProductDTO productDTO = productMapper.selectById(Long.parseLong(id));
        if (!Objects.isNull(productDTO)) {
            productDTO.setCalibList(productCalibMapper.selectByProductIds(Collections.singletonList(productDTO.getId())));
        }
        return ApiResponseBody.defaultSuccess(productDTO);
    }

    @Transactional(rollbackFor = Exception.class)
    public ApiResponseBody delete(String id) {
        List<ProductCraft> usedCraftList = productCraftMapper.selectUsedCraftByProductIds(Collections.singletonList(Long.parseLong(id)));
        if (!CollectionUtils.isEmpty(usedCraftList)) {
            List<String> inUsedCraftCodeList = usedCraftList.stream().map(ProductCraft::getCraftCode).collect(Collectors.toList());
            throw new BizDmException("下属工艺已被使用: " + String.join(",", inUsedCraftCodeList));
        }
        return ApiResponseBody.defaultSuccess(productMapper.deleteById(Long.parseLong(id), MyEntityTool.getUpdateInfo()));
    }

    @Transactional(rollbackFor = Exception.class)
    public ApiResponseBody update(ProductDTO productDTO) {
        checkNameAndModel(productDTO);
        Product oldProduct = productMapper.selectById(productDTO.getId());
        EntityTool.updateEntity(productDTO);
        productMapper.updateByPrimaryKey(productDTO);
        // 应用状态：启用 --> 弃用
        if (OrderTool.isChangedValue(oldProduct.getApplyStatus(), productDTO.getApplyStatus()) && productDTO.getApplyStatus().equals(false)) {
            List<ProductCraft> usedCraftList = productCraftMapper.selectUsedCraftByProductIds(Collections.singletonList(productDTO.getId()));
            if (!CollectionUtils.isEmpty(usedCraftList)) {
                List<String> inUsedCraftCodeList = usedCraftList.stream().map(ProductCraft::getCraftCode).collect(Collectors.toList());
                throw new BizDmException(String.format("工艺已被使用: %s", String.join(",", inUsedCraftCodeList)));
            }
            productCraftMapper.disableBatchByProductId(productDTO.getId(), MyEntityTool.getUpdateInfo());
        }
        // 更新参数率定
        List<ProductCalib> toAddProductCalibList = new ArrayList<>();
        List<ProductCalib> oldProductCalibList = productCalibMapper.selectByProductIds(Collections.singletonList(productDTO.getId()));
        if (Objects.isNull(oldProductCalibList)) {
            oldProductCalibList = new ArrayList<>();
        }
        oldProductCalibList.forEach(calib -> calib.setDeleteFlag(DeleteFlagEnum.DATA_WARN.getFlag()));
        if (!CollectionUtils.isEmpty(productDTO.getCalibList())) {
            for (ProductCalib calib : productDTO.getCalibList()) {
                Optional<ProductCalib> calibOptional = oldProductCalibList.stream().filter(e -> e.getId().equals(calib.getId()) || e.getCalibCode().equals(calib.getCalibCode())).findFirst();
                if (calibOptional.isPresent()) {
                    ProductCalib productCalib = calibOptional.get();
                    productCalib.setDeleteFlag(DeleteFlagEnum.DATA_OK.getFlag());
                    productCalib.setCalibCode(calib.getCalibCode());
                    productCalib.setCalibRemark(calib.getCalibRemark());
                } else {
                    calib.setProductId(productDTO.getId());
                    toAddProductCalibList.add(calib);
                }
            }
        }
        if (!CollectionUtils.isEmpty(toAddProductCalibList)) {
            EntityTool.insertEntity(toAddProductCalibList);
            productCalibMapper.insertBatch(toAddProductCalibList);
        }
        if (!CollectionUtils.isEmpty(oldProductCalibList)) {
            EntityTool.updateEntity(oldProductCalibList);
            productCalibMapper.updateBatch(oldProductCalibList);
        }
        return ApiResponseBody.defaultSuccess();
    }

    public ApiResponseBody<PageInfo<ProductDTO>> page(Integer pageNum, Integer pageSize, String name) {
        PageHelper.startPage(pageNum, pageSize);
        List<ProductDTO> productDTOList = productMapper.list(name);
        if (!CollectionUtils.isEmpty(productDTOList)) {
            List<ProductCalib> calibList = productCalibMapper.selectByProductIds(productDTOList.stream().map(Product::getId).collect(Collectors.toList()));
            if (!CollectionUtils.isEmpty(calibList)) {
                Map<Long, List<ProductCalib>> calibMap = calibList.stream().collect(Collectors.groupingBy(ProductCalib::getProductId));
                productDTOList.forEach(e -> e.setCalibList(calibMap.get(e.getId())));
            }
        }
        return ApiResponseBody.defaultSuccess(new PageInfo<>(productDTOList));
    }


    /**
     * 校验唯一性：产品名称、产品型号
     *
     * @param product
     * @return
     */
    private void checkNameAndModel(Product product) throws BusinessException {
        if (Strings.isNullOrEmpty(product.getName())) {
            throw new BizDmException("产品名称必填!");
        }
        if (Strings.isNullOrEmpty(product.getModel())) {
            throw new BizDmException("产品型号必填!");
        }
        if (Strings.isNullOrEmpty(product.getCertType())) {
            throw new BizDmException("合格证类型必填!");
        }
        if (Strings.isNullOrEmpty(product.getCategoryCode()) || 2 != product.getCategoryCode().length()) {
            throw new BizDmException("产品种类必填（两位）!");
        }
        if (productMapper.isExistedName(product)) {
            throw new BizDmException("产品名称已存在!");
        }
        if (productMapper.isExistedModel(product)) {
            throw new BizDmException("产品型号已存在!");
        }
    }
}
