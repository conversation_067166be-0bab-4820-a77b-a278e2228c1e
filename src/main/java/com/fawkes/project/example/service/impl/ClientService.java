package com.fawkes.project.example.service.impl;

import com.fawkes.core.base.api.ApiResponseBody;
import com.fawkes.core.base.model.BaseEntity;
import com.fawkes.core.enums.DeleteFlagEnum;
import com.fawkes.core.utils.EntityTool;
import com.fawkes.project.example.common.enums.ClientContactsEnum;
import com.fawkes.project.example.common.enums.ClientTypeEnum;
import com.fawkes.project.example.common.exception.BizDmException;
import com.fawkes.project.example.common.mapper.ClientContactsMapper;
import com.fawkes.project.example.common.mapper.ClientMapper;
import com.fawkes.project.example.common.mapper.SaleOrderMapper;
import com.fawkes.project.example.common.model.Client;
import com.fawkes.project.example.common.model.ClientContacts;
import com.fawkes.project.example.common.utils.MyEntityTool;
import com.fawkes.project.example.common.utils.OrderTool;
import com.fawkes.project.example.domain.param.ClientParam;
import com.fawkes.project.example.domain.param.SaleOrderPageParam;
import com.fawkes.project.example.domain.vo.*;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.google.common.base.Strings;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.time.Year;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 客户管理
 *
 * <AUTHOR>
 */
@Service
@Slf4j
public class ClientService {
    private final static List<String> INDUSTRY_TYPE = Arrays.asList("陆上风电", "海上风电", "光伏", "水利水电", "市政桥梁", "其他");
    @Resource
    ClientMapper clientMapper;
    @Resource
    ClientContactsMapper clientContactsMapper;
    @Resource
    SaleOrderMapper saleOrderMapper;

    @Transactional(rollbackFor = Exception.class)
    public ApiResponseBody add(ClientVO clientVO) {
        checkClientClientName(clientVO);
        clientVO.setClientNo(OrderTool.genClientNoFromPrev(clientVO.getClientType(), clientMapper.getMaxClientNoByClientType(clientVO.getClientType())));
        EntityTool.insertEntity(clientVO);
        clientMapper.insert(clientVO);
        //  更新联系人
        List<ClientContacts> contactsList = new ArrayList<>();
        if (!CollectionUtils.isEmpty(clientVO.getContactsList())) {
            clientVO.getContactsList().forEach(e -> {
                e.setContactsType(ClientContactsEnum.TYPE_CONTACTS.getFlag());
                e.setClientId(clientVO.getId());
                contactsList.add(e);
            });
        }
        if (!CollectionUtils.isEmpty(clientVO.getUserInChargeList())) {
            clientVO.getUserInChargeList().forEach(e -> {
                e.setContactsType(ClientContactsEnum.TYPE_IN_CHARGE.getFlag());
                e.setClientId(clientVO.getId());
                contactsList.add(e);
            });
        }
        if (!CollectionUtils.isEmpty(contactsList)) {
            EntityTool.insertEntity(contactsList);
            clientContactsMapper.insertBatch(contactsList);
        }
        return ApiResponseBody.defaultSuccess();
    }

    @Transactional(rollbackFor = Exception.class)
    public ApiResponseBody deleteBatch(String ids) {
        clientMapper.deleteBatch(Arrays.stream(ids.split(",")).map(Long::parseLong).collect(Collectors.toList()), MyEntityTool.getUpdateInfo());
        return ApiResponseBody.defaultSuccess();
    }

    public ApiResponseBody<ClientVO> query(String id) {
        ClientVO clientVO = new ClientVO();
        Client client = clientMapper.selectByPrimaryKey(Long.parseLong(id));
        BeanUtils.copyProperties(client, clientVO);
        List<ClientContacts> contactsList = clientContactsMapper.selectByClientIds(Collections.singletonList(clientVO.getId()));
        if (!CollectionUtils.isEmpty(contactsList)) {
            clientVO.setContactsList(contactsList.stream().filter(e -> e.getContactsType().equals(ClientContactsEnum.TYPE_CONTACTS.getFlag())).collect(Collectors.toList()));
            clientVO.setUserInChargeList(contactsList.stream().filter(e -> e.getContactsType().equals(ClientContactsEnum.TYPE_IN_CHARGE.getFlag())).collect(Collectors.toList()));
        }
        return ApiResponseBody.defaultSuccess(clientVO);
    }

    @Transactional(rollbackFor = Exception.class)
    public ApiResponseBody update(ClientVO clientVO) {
        checkClientClientName(clientVO);
        EntityTool.updateEntity(clientVO);
        clientMapper.updateByPrimaryKey(clientVO);

        List<ClientContacts> oldContactsList = clientContactsMapper.selectByClientIds(Collections.singletonList(clientVO.getId()));
        List<ClientContacts> toAddContactsList = new ArrayList<>();
        oldContactsList.forEach(e -> e.setDeleteFlag(DeleteFlagEnum.DATA_WARN.getFlag()));
        updateContactsList(clientVO.getContactsList(), oldContactsList, toAddContactsList, clientVO, ClientContactsEnum.TYPE_CONTACTS.getFlag());
        updateContactsList(clientVO.getUserInChargeList(), oldContactsList, toAddContactsList, clientVO, ClientContactsEnum.TYPE_IN_CHARGE.getFlag());
        if (!CollectionUtils.isEmpty(toAddContactsList)) {
            EntityTool.insertEntity(toAddContactsList);
            clientContactsMapper.insertBatch(toAddContactsList);
        }
        if (!CollectionUtils.isEmpty(oldContactsList)) {
            EntityTool.updateEntity(oldContactsList);
            clientContactsMapper.updateBatch(oldContactsList);
        }
        return ApiResponseBody.defaultSuccess();
    }

    public ApiResponseBody<PageInfo<ClientVO>> page(ClientParam param) {
        PageHelper.startPage(param.getPageNum(), param.getPageSize());
        List<ClientVO> clientVOList = clientMapper.selectClientList(param);
        if (!CollectionUtils.isEmpty(clientVOList)) {
            List<Long> clientIds = clientVOList.stream().map(BaseEntity::getId).collect(Collectors.toList());
            // 销售订单
            Map<Long, List<SaleOrderVO>> clientSaleOrderMap = new HashMap<>();
            List<SaleOrderVO> saleOrderList = saleOrderMapper.selectByClientIds(clientIds);
            if (!CollectionUtils.isEmpty(saleOrderList)) {
                clientSaleOrderMap = saleOrderList.stream().collect(Collectors.groupingBy(SaleOrderVO::getClientId));
            }
            BigDecimal totalSaleAmount = saleOrderMapper.getTotalSaleAmount();
            // 联系人
            Map<Long, Map<Integer, List<ClientContacts>>> contactsMap = new HashMap<>();
            List<ClientContacts> contactsList = clientContactsMapper.selectByClientIds(clientIds);
            if (!CollectionUtils.isEmpty(contactsList)) {
                contactsMap = contactsList.stream().collect(Collectors.groupingBy(ClientContacts::getClientId, Collectors.groupingBy(ClientContacts::getContactsType)));
            }
            for (ClientVO clientVO : clientVOList) {
                List<SaleOrderVO> orderVOList = clientSaleOrderMap.get(clientVO.getId());
                if (!CollectionUtils.isEmpty(orderVOList)) {
                    clientVO.setIndustryList(orderVOList.stream().map(SaleOrderVO::getIndustry).filter(Objects::nonNull).distinct().collect(Collectors.toList()));
                    clientVO.setOrderQuantity(orderVOList.size());
                    clientVO.setSaleAmount(orderVOList.stream().map(SaleOrderVO::getSalePrice).reduce(BigDecimal::add).get());
                    clientVO.setSaleAmountPercent(OrderTool.bdPercent(clientVO.getSaleAmount(), totalSaleAmount));
                }
                Map<Integer, List<ClientContacts>> clientContactsMap = contactsMap.get(clientVO.getId());
                if (!Objects.isNull(clientContactsMap)) {
                    clientVO.setContactsList(clientContactsMap.get(ClientContactsEnum.TYPE_CONTACTS.getFlag()));
                    clientVO.setUserInChargeList(clientContactsMap.get(ClientContactsEnum.TYPE_IN_CHARGE.getFlag()));
                }
            }
        }
        PageInfo<ClientVO> pageInfo = new PageInfo<>(clientVOList);
        return ApiResponseBody.defaultSuccess(pageInfo);
    }

    /**
     * 统计客户类型、行业类型
     *
     * @return
     */
    public ApiResponseBody statClient() {
        Map<String, Map<String, Integer>> statMap = new HashMap<>(2);
        statMap.put("industry", statByIndustryType());
        statMap.put("clientType", statByClientType());
        return ApiResponseBody.defaultSuccess(statMap);
    }

    /**
     * 按年统计销售金额
     *
     * @param searchYear
     * @return
     */
    public ApiResponseBody statSaleAmount(Year searchYear) {
        List<StatSaleVO<BigDecimal>> statList = new ArrayList<>();
        statList.add(new StatSaleVO<>(ClientTypeEnum.YNKH.getFlag(), ClientTypeEnum.YNKH.getDesc(), new BigDecimal("0")));
        statList.add(new StatSaleVO<>(ClientTypeEnum.JTKH.getFlag(), ClientTypeEnum.JTKH.getDesc(), new BigDecimal("0")));
        statList.add(new StatSaleVO<>(ClientTypeEnum.SCKH.getFlag(), ClientTypeEnum.SCKH.getDesc(), new BigDecimal("0")));
        statList.add(new StatSaleVO<>(ClientTypeEnum.CPDLS.getFlag(), ClientTypeEnum.CPDLS.getDesc(), new BigDecimal("0")));

        // 查询所有订单
        List<SaleOrderVO> orderList = saleOrderMapper.listByYear(searchYear);
        if (!CollectionUtils.isEmpty(orderList)) {
            // 查询所有客户
            Map<String, List<SaleOrderVO>> clientOrderMap = orderList.stream().collect(Collectors.groupingBy(SaleOrderVO::getClientType));
            clientOrderMap.keySet().forEach(key -> {
                StatSaleVO statSaleVO = statList.stream().filter(e -> e.getClientType().equals(key)).findFirst().get();
                statSaleVO.setTotalNum(clientOrderMap.get(key).stream().map(SaleOrderVO::getSalePrice).reduce(BigDecimal::add));
                List<StatSaleDetailVO<BigDecimal>> detailVOList = new ArrayList<>();
                clientOrderMap.get(key).stream()
                        .collect(Collectors.groupingBy(SaleOrderVO::getClientName))
                        .forEach((clientName, list) -> detailVOList.add(new StatSaleDetailVO<>(clientName, list.stream().map(SaleOrderVO::getSalePrice).reduce(BigDecimal::add).get())));
                statSaleVO.setChildren(detailVOList);
            });
        }
        return ApiResponseBody.defaultSuccess(statList);
    }

    /**
     * 按行业统计销售金额
     *
     * @param searchYear
     * @return
     */
    public ApiResponseBody statSaleAmountByIndustry(Year searchYear) {
        List<LabelValueVO> statList = new ArrayList<>();
        INDUSTRY_TYPE.forEach(e -> statList.add(new LabelValueVO(e, new BigDecimal("0"))));
        // 查询所有订单
        List<SaleOrderVO> orderList = saleOrderMapper.listByYear(searchYear);
        if (!CollectionUtils.isEmpty(orderList)) {
            // 查询所有客户
            Map<String, List<SaleOrderVO>> clientOrderMap = orderList.stream().filter(e -> !Strings.isNullOrEmpty(e.getIndustry())).collect(Collectors.groupingBy(SaleOrderVO::getIndustry));
            clientOrderMap.keySet().forEach(key -> {
                LabelValueVO vo = statList.stream().filter(e -> e.getLabel().equals(key)).findFirst().get();
                vo.setValue(clientOrderMap.get(key).stream().map(SaleOrderVO::getSalePrice).reduce(BigDecimal::add));
            });
        }
        return ApiResponseBody.defaultSuccess(statList);
    }

    /**
     * 按年统计销售数量
     * statSaleQuantity
     *
     * @param searchYear
     * @return
     */
    public ApiResponseBody statSaleQuantity(Year searchYear) {
        if (Objects.isNull(searchYear)) {
            throw new BizDmException("请选择年份");
        }
        List<StatSaleVO<Integer>> statQuantity = new ArrayList<>();
        statQuantity.add(new StatSaleVO<>(ClientTypeEnum.YNKH.getFlag(), ClientTypeEnum.YNKH.getDesc(), 0));
        statQuantity.add(new StatSaleVO<>(ClientTypeEnum.JTKH.getFlag(), ClientTypeEnum.JTKH.getDesc(), 0));
        statQuantity.add(new StatSaleVO<>(ClientTypeEnum.SCKH.getFlag(), ClientTypeEnum.SCKH.getDesc(), 0));
        statQuantity.add(new StatSaleVO<>(ClientTypeEnum.CPDLS.getFlag(), ClientTypeEnum.CPDLS.getDesc(), 0));

        // 查询所有订单
        List<SaleOrderVO> orderList = saleOrderMapper.listByYear(searchYear);
        if (!CollectionUtils.isEmpty(orderList)) {
            // 查询所有客户
            Map<String, List<SaleOrderVO>> clientOrderMap = orderList.stream().collect(Collectors.groupingBy(SaleOrderVO::getClientType));
            clientOrderMap.keySet().forEach(key -> {
                StatSaleVO statSaleVO = statQuantity.stream().filter(e -> e.getClientType().equals(key)).findFirst().get();
                statSaleVO.setTotalNum(clientOrderMap.get(key).size());
                List<StatSaleDetailVO<Integer>> detailVOList = new ArrayList<>();
                clientOrderMap.get(key).stream()
                        .collect(Collectors.groupingBy(SaleOrderVO::getClientName))
                        .forEach((clientName, list) -> detailVOList.add(new StatSaleDetailVO<>(clientName, list.size())));
                statSaleVO.setChildren(detailVOList);
            });
        }
        return ApiResponseBody.defaultSuccess(statQuantity);
    }

    public ApiResponseBody statClientByProvince(Year searchYear) {
        List<LabelValueVO> statByProvince = new ArrayList<>();
        List<SaleOrderVO> orderVOList = saleOrderMapper.listByYear(searchYear);
        if (!CollectionUtils.isEmpty(orderVOList)) {
            Map<String, List<SaleOrderVO>> provinceOrderMap = orderVOList.stream().filter(e -> !Strings.isNullOrEmpty(e.getClientProvince())).collect(Collectors.groupingBy(SaleOrderVO::getClientProvince));
            provinceOrderMap.keySet().forEach(key -> {
                statByProvince.add(new LabelValueVO(key, provinceOrderMap.get(key).size()));
            });
        }
        return ApiResponseBody.defaultSuccess(statByProvince);
    }

    public ApiResponseBody<StatClientOverallVO> statOverall(Year searchYear, String province) {
        StatClientOverallVO statClientOverallVO = new StatClientOverallVO();
        List<ClientVO> statClientList = new ArrayList<>();
        // 销售金额
        List<LabelValueExtraVO> saleAmountPercentByClientType = new ArrayList<>();
        ClientTypeEnum.flags().forEach(flag -> saleAmountPercentByClientType.add(new LabelValueExtraVO(flag, BigDecimal.ZERO, "")));
        List<LabelValueExtraVO> saleAmountPercentByIndustryType = new ArrayList<>();
        INDUSTRY_TYPE.forEach(flag -> saleAmountPercentByIndustryType.add(new LabelValueExtraVO(flag, BigDecimal.ZERO, "")));
        // 订单数量
        List<LabelValueExtraVO> saleQuantityPercentByClientType = new ArrayList<>();
        ClientTypeEnum.flags().forEach(flag -> saleQuantityPercentByClientType.add(new LabelValueExtraVO(flag, BigDecimal.ZERO, "0%")));
        List<LabelValueExtraVO> saleQuantityPercentByIndustryType = new ArrayList<>();
        INDUSTRY_TYPE.forEach(flag -> saleQuantityPercentByIndustryType.add(new LabelValueExtraVO(flag, BigDecimal.ZERO, "0%")));


        List<SaleOrderVO> orderVOList = saleOrderMapper.listByYearAndProvince(searchYear, province);
        if (!CollectionUtils.isEmpty(orderVOList)) {
            int totalQuantity = orderVOList.size();
            Map<String, List<SaleOrderVO>> clientTypeOrderMap = orderVOList.stream().filter(e -> !Strings.isNullOrEmpty(e.getClientType())).collect(Collectors.groupingBy(SaleOrderVO::getClientType));
            Map<String, List<SaleOrderVO>> industryOrderMap = orderVOList.stream().filter(e -> !Strings.isNullOrEmpty(e.getIndustry())).collect(Collectors.groupingBy(SaleOrderVO::getIndustry));

            // 客户类型
            clientTypeOrderMap.keySet().forEach(key -> {
                //  销售金额
                Optional<LabelValueExtraVO> optionalAmount = saleAmountPercentByClientType.stream().filter(e -> key.equals(e.getLabel())).findFirst();
                if (optionalAmount.isPresent()) {
                    BigDecimal clientTypeSaleAmount = clientTypeOrderMap.get(key).stream().map(SaleOrderVO::getSalePrice).reduce(BigDecimal::add).get();
                    optionalAmount.get().setValue(clientTypeSaleAmount);
                }
                //  订单数量
                Optional<LabelValueExtraVO> optionalQuantity = saleQuantityPercentByClientType.stream().filter(e -> key.equals(e.getLabel())).findFirst();
                if (optionalQuantity.isPresent()) {
                    int quantity = clientTypeOrderMap.get(key).size();
                    optionalQuantity.get().setValue(quantity);
                    optionalQuantity.get().setExtra(OrderTool.bdPercent(BigDecimal.valueOf(quantity), BigDecimal.valueOf(totalQuantity)));
                }
            });

            //  行业类型
            industryOrderMap.keySet().forEach(key -> {
                //  销售金额
                Optional<LabelValueExtraVO> optionalAmount = saleAmountPercentByIndustryType.stream().filter(e -> key.equals(e.getLabel())).findFirst();
                if (optionalAmount.isPresent()) {
                    BigDecimal clientTypeSaleAmount = industryOrderMap.get(key).stream().map(SaleOrderVO::getSalePrice).reduce(BigDecimal::add).get();
                    optionalAmount.get().setValue(clientTypeSaleAmount);
                }
                //  订单数量
                Optional<LabelValueExtraVO> optionalQuantity = saleQuantityPercentByIndustryType.stream().filter(e -> key.equals(e.getLabel())).findFirst();
                if (optionalQuantity.isPresent()) {
                    int quantity = industryOrderMap.get(key).size();
                    optionalQuantity.get().setValue(quantity);
                    optionalQuantity.get().setExtra(OrderTool.bdPercent(BigDecimal.valueOf(quantity), BigDecimal.valueOf(totalQuantity)));
                }
            });

            // 客户分布详情
            Map<String, List<SaleOrderVO>> clientNameOrderMap = orderVOList.stream().collect(Collectors.groupingBy(SaleOrderVO::getClientName));
            if (!CollectionUtils.isEmpty(clientNameOrderMap)) {
                statClientList = clientNameOrderMap.keySet().stream()
                        .map(e -> new ClientVO(e,
                                clientNameOrderMap.get(e).get(0).getClientType(),
                                clientNameOrderMap.get(e).stream().map(SaleOrderVO::getSalePrice).reduce(BigDecimal::add).get(),
                                clientNameOrderMap.get(e).stream().map(SaleOrderVO::getSignDate).filter(signDate -> !Objects.isNull(signDate)).max(Comparator.naturalOrder()).orElse(null)
                        ))
                        .collect(Collectors.toList());
            }
        }


        List<LabelValueExtraVO> statSaleAmountPercent = new ArrayList<>();
        statSaleAmountPercent.add(new LabelValueExtraVO("CLIENT_TYPE", "", saleAmountPercentByClientType));
        statSaleAmountPercent.add(new LabelValueExtraVO("INDUSTRY_TYPE", "", saleAmountPercentByIndustryType));

        List<LabelValueExtraVO> stateSaleQuantityPercent = new ArrayList<>();
        stateSaleQuantityPercent.add(new LabelValueExtraVO("CLIENT_TYPE", "", saleQuantityPercentByClientType));
        stateSaleQuantityPercent.add(new LabelValueExtraVO("INDUSTRY_TYPE", "", saleQuantityPercentByIndustryType));

        statClientOverallVO.setOrderAmountPercentList(statSaleAmountPercent);
        statClientOverallVO.setOrderQuantityPercentList(stateSaleQuantityPercent);
        statClientOverallVO.setClientList(statClientList);
        statClientOverallVO.setClientSaleAmountTop5(statClientList.stream().sorted((o1, o2) -> o2.getSaleAmount().compareTo(o1.getSaleAmount())).limit(5).collect(Collectors.toList()));
        return ApiResponseBody.defaultSuccess(statClientOverallVO);
    }

    /**
     * 按照按行业类型统计
     *
     * @return
     */
    private Map<String, Integer> statByIndustryType() {
        Map<String, Integer> industryStat = new HashMap<>(6);
        INDUSTRY_TYPE.forEach(e -> industryStat.put(e, 0));

        List<SaleOrderVO> saleOrderList = saleOrderMapper.list(SaleOrderPageParam.getQueryAllParam());
        if (!CollectionUtils.isEmpty(saleOrderList)) {
            Map<String, List<Long>> industryMap = saleOrderList.stream()
                    .collect(Collectors.groupingBy(SaleOrderVO::getIndustry, Collectors.mapping(SaleOrderVO::getClientId, Collectors.toList())));
            industryStat.keySet().forEach(key -> {
                if (industryMap.containsKey(key)) {
                    industryStat.put(key, industryMap.get(key).size());
                }
            });
        }
        return industryStat;
    }

    /**
     * 按照按客户类型统计
     *
     * @return
     */
    private Map<String, Integer> statByClientType() {
        Map<String, Integer> statMap = new HashMap<>(4);
        ClientTypeEnum.flags().forEach(e -> statMap.put(e, 0));
        List<ClientVO> clientList = clientMapper.selectClientList(ClientParam.getQueryAllParam());
        if (!CollectionUtils.isEmpty(clientList)) {
            Map<String, List<Client>> clientMap = clientList.stream().collect(Collectors.groupingBy(Client::getClientType));
            statMap.keySet().forEach(key -> {
                if (clientMap.containsKey(key)) {
                    statMap.put(key, clientMap.get(key).size());
                }
            });
        }
        return statMap;
    }

    private void checkClientClientName(Client client) {
        if (Strings.isNullOrEmpty(client.getClientType()) || ClientTypeEnum.containFlag(client.getClientType())) {
            throw new BizDmException("客户类型为空");
        }
        if (Strings.isNullOrEmpty(client.getClientName())) {
            throw new BizDmException("客户名称为空");
        }
        if (clientMapper.isExistedClientName(client)) {
            throw new BizDmException("客户已存在:" + client.getClientName());
        }
    }

    private void updateContactsList(List<ClientContacts> sourceList, List<ClientContacts> oldContactsList, List<ClientContacts> toAddContactsList, ClientVO clientVO, Integer contactsType) {
        if (!CollectionUtils.isEmpty(sourceList)) {
            sourceList.forEach(contacts -> {
                if (!Objects.isNull(contacts.getId())) {
                    Optional<ClientContacts> optional = oldContactsList.stream().filter(e -> e.getId().equals(contacts.getId())).findFirst();
                    if (optional.isPresent()) {
                        ClientContacts updateContacts = optional.get();
                        updateContacts.setDeleteFlag(DeleteFlagEnum.DATA_OK.getFlag());
                        updateContacts.setContactsName(contacts.getContactsName());
                        updateContacts.setPhone(contacts.getPhone());
                        updateContacts.setPost(contacts.getPost());
                    }
                } else {
                    contacts.setClientId(clientVO.getId());
                    contacts.setContactsType(contactsType);
                    toAddContactsList.add(contacts);
                }
            });
        }
    }
}
