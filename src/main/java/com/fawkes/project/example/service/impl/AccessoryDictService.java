package com.fawkes.project.example.service.impl;

import com.fawkes.core.base.api.ApiResponseBody;
import com.fawkes.core.utils.EntityTool;
import com.fawkes.project.example.common.exception.BizDmException;
import com.fawkes.project.example.common.mapper.AccessoryDictMapper;
import com.fawkes.project.example.common.model.AccessoryDict;
import com.fawkes.project.example.common.utils.MyEntityTool;
import com.fawkes.project.example.domain.dto.AccessoryDictDTO;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.google.common.base.Strings;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 配件字典管理
 *
 * <AUTHOR>
 */
@Service
@Slf4j
public class AccessoryDictService {
    @Resource
    private AccessoryDictMapper accessoryDictMapper;

    public ApiResponseBody add(AccessoryDict dict) {
        checkAccessoryDict(dict);
        EntityTool.insertEntity(dict);
        accessoryDictMapper.insert(dict);
        return ApiResponseBody.defaultSuccess();
    }

    @Transactional(rollbackFor = Exception.class)
    public ApiResponseBody delete(String id) {
        if (accessoryDictMapper.isUsedAccessoryDictById(Long.parseLong(id))) {
            throw new BizDmException("配件已被引用");
        }
        accessoryDictMapper.deleteById(Long.parseLong(id), MyEntityTool.getUpdateInfo());
        return ApiResponseBody.defaultSuccess();
    }

    @Transactional(rollbackFor = Exception.class)
    public ApiResponseBody update(AccessoryDict dict) {
        checkAccessoryDict(dict);
        EntityTool.updateEntity(dict);
        AccessoryDict record = accessoryDictMapper.selectById(dict.getId());
        dict.setDictParent(record.getDictParent());
        accessoryDictMapper.updateByPrimaryKey(dict);
        return ApiResponseBody.defaultSuccess();
    }

    public ApiResponseBody<PageInfo<AccessoryDictDTO>> page(String typeName, String typeCode, Integer pageNum, Integer pageSize) {
        PageHelper.startPage(pageNum, pageSize);
        List<AccessoryDictDTO> typeList = accessoryDictMapper.selectAccessoryDictType(typeName, typeCode);
        if (!CollectionUtils.isEmpty(typeList)) {
            List<Long> typeIds = typeList.stream().map(AccessoryDict::getId).collect(Collectors.toList());
            Map<Long, List<AccessoryDict>> refMap = accessoryDictMapper.selectByDictParentIds(typeIds).stream().collect(Collectors.groupingBy(AccessoryDict::getDictParent));
            typeList.forEach(dict -> dict.setChildren(refMap.get(dict.getId())));
        }
        return ApiResponseBody.defaultSuccess(new PageInfo<>(typeList));
    }

    private void checkAccessoryDict(AccessoryDict dict) {
        if (Strings.isNullOrEmpty(dict.getDictCode())) {
            throw new BizDmException("请输入编码");
        }
        if (Strings.isNullOrEmpty(dict.getDictName())) {
            throw new BizDmException("请输入名称");
        }
        if (accessoryDictMapper.isExistedDictCode(dict)) {
            throw new BizDmException("编码已存在");
        }
        if (accessoryDictMapper.isExistedDictName(dict)) {
            throw new BizDmException("名称已存在");
        }
    }
}
