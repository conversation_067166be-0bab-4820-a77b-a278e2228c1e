package com.fawkes.project.example.service.impl;

import com.fawkes.core.base.api.ApiResponseBody;
import com.fawkes.core.base.model.BaseEntity;
import com.fawkes.core.enums.DeleteFlagEnum;
import com.fawkes.core.utils.EntityTool;
import com.fawkes.project.example.common.constants.CommonConstants;
import com.fawkes.project.example.common.exception.BizDmException;
import com.fawkes.project.example.common.mapper.*;
import com.fawkes.project.example.common.model.*;
import com.fawkes.project.example.common.utils.MyEntityTool;
import com.fawkes.project.example.common.utils.OrderTool;
import com.fawkes.project.example.domain.dto.ProductDTO;
import com.fawkes.project.example.domain.dto.SaleOrderDTO;
import com.fawkes.project.example.domain.dto.SaleOrderUpdateDTO;
import com.fawkes.project.example.domain.param.SaleOrderPageParam;
import com.fawkes.project.example.domain.param.SaleOutConfirmQo;
import com.fawkes.project.example.domain.param.SaleReturnObj;
import com.fawkes.project.example.domain.param.SaleReturnParam;
import com.fawkes.project.example.domain.vo.*;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.google.common.base.Strings;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.sql.Timestamp;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.Year;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Service
@Slf4j
public class SaleOrderServiceImpl {
    private final String MODULE = "销售订单";
    @Resource
    SaleOrderMapper saleOrderMapper;
    @Resource
    SaleOrderGoodsMapper saleOrderGoodsMapper;
    @Resource
    SaleGoodsAlterMapper saleGoodsAlterMapper;
    @Resource
    SaleGoodsAlterLogMapper saleGoodsAlterLogMapper;
    @Resource
    ClientMapper clientMapper;
    @Resource
    ProductCraftMapper productCraftMapper;
    @Resource
    ProductParamMapper productParamMapper;
    @Resource
    BizLogService bizLogService;
    @Resource
    ProductOrderMapper productOrderMapper;
    @Resource
    BizMsgService bizMsgService;

    public ApiResponseBody<String> getLatestOrderNo() {
        LocalDate now = LocalDate.now();
        String orderNo = OrderTool.genSaleOrderNoFromPrev(now, saleOrderMapper.getMaxOrderNoInYear(String.valueOf(now.getYear())));
        return ApiResponseBody.defaultSuccess(orderNo);
    }

    @Transactional(rollbackFor = Exception.class)
    public ApiResponseBody add(SaleOrderDTO orderDTO) {
        checkOrder(orderDTO);
        EntityTool.insertEntity(orderDTO);
        saleOrderMapper.insert(orderDTO);
        if (!CollectionUtils.isEmpty(orderDTO.getGoodsList())) {
            orderDTO.getGoodsList().forEach(e -> e.setOrderId(orderDTO.getId()));
            EntityTool.insertEntity(orderDTO.getGoodsList());
            saleOrderGoodsMapper.insertBatch(orderDTO.getGoodsList());
        }
        bizLogService.log(MODULE, "新增销售订单: " + orderDTO.getOrderNo());
        bizMsgService.sendSaleOrderMsg("新增销售订单", orderDTO.getOrderNo(), orderDTO.getLiableUserId());
        return ApiResponseBody.defaultSuccess();
    }

    public ApiResponseBody query(String id) {
        SaleOrderDTO orderDTO = saleOrderMapper.selectById(Long.parseLong(id));
        orderDTO.setGoodsList(saleOrderGoodsMapper.listByOrderId(orderDTO.getId()));
        return ApiResponseBody.defaultSuccess(orderDTO);
    }

    @Transactional(rollbackFor = Exception.class)
    public ApiResponseBody deleteByIds(String ids) {
        List<Long> orderIds = Arrays.stream(ids.split(",")).map(Long::parseLong).collect(Collectors.toList());

        String orderNoStr = saleOrderMapper.selectByIds(orderIds)
                .stream()
                .map(SaleOrderVO::getOrderNo)
                .collect(Collectors.joining(","));
        bizLogService.log(MODULE, "删除销售订单: " + orderNoStr);

        BaseEntity entity = new BaseEntity();
        EntityTool.updateEntity(entity);
        saleOrderMapper.deleteByIds(orderIds, MyEntityTool.getUpdateInfo());
        saleOrderMapper.cleanOutBySaleOrderIds(orderIds);

        List<SaleOrderVO> saleOrderVOList = saleOrderMapper.selectByIds(orderIds);
        saleOrderVOList.forEach(e -> bizMsgService.sendSaleOrderMsg("删除销售订单", e.getOrderNo(), e.getLiableUserId()));

        return ApiResponseBody.defaultSuccess();
    }

    public ApiResponseBody preCheck(SaleOrderDTO orderDTO) {
        checkOrder(orderDTO);
        SaleOrder record = saleOrderMapper.selectById(orderDTO.getId());
        // 订单信息变更
        List<SaleGoodsAlterLog> alterLogList = getOrderInfoChangeLogs(record, orderDTO);
        // 销售明细变更
        List<SaleOrderGoodsVO> oldOrderGoodsList = saleOrderGoodsMapper.listByOrderId(orderDTO.getId());

        List<SaleOrderGoods> toAddList = new ArrayList<>();
        List<SaleOrderGoods> toUpdateList = new ArrayList<>();
        List<SaleOrderGoods> toDeleteList = new ArrayList<>();
        List<SaleGoodsAlterLog> goodsAlterLogs = getOrderGoodsChangeLogs(oldOrderGoodsList, orderDTO.getGoodsList(), toAddList, toUpdateList, toDeleteList);
        if (!CollectionUtils.isEmpty(goodsAlterLogs)) {
            alterLogList.addAll(goodsAlterLogs);
        }
        return ApiResponseBody.defaultSuccess(alterLogList);
    }

    @Transactional(rollbackFor = Exception.class)
    public ApiResponseBody update(SaleOrderUpdateDTO orderUpdateDTO) {
        if (Strings.isNullOrEmpty(orderUpdateDTO.getAlterReason())) {
            throw new BizDmException("请输入变更理由");
        }
        checkOrder(orderUpdateDTO);
        // 更新订单信息
        SaleOrder record = saleOrderMapper.selectById(orderUpdateDTO.getId());
        EntityTool.updateEntity(orderUpdateDTO);
        MyEntityTool.keepCrateAndDeleteFlag(record, orderUpdateDTO);
        saleOrderMapper.updateByPrimaryKeyWithBLOBs(orderUpdateDTO);

        // 更新订单明细
        List<SaleOrderGoodsVO> oldOrderGoodsList = saleOrderGoodsMapper.listByOrderId(orderUpdateDTO.getId());
        List<SaleOrderGoods> toAddList = new ArrayList<>();
        List<SaleOrderGoods> toUpdateList = new ArrayList<>();
        List<SaleOrderGoods> toDeleteList = new ArrayList<>();
        List<SaleGoodsAlterLog> alterLogList = getOrderInfoChangeLogs(record, orderUpdateDTO);
        List<SaleGoodsAlterLog> goodsAlterLogs = getOrderGoodsChangeLogs(oldOrderGoodsList, orderUpdateDTO.getGoodsList(), toAddList, toUpdateList, toDeleteList);
        if (!CollectionUtils.isEmpty(toAddList)) {
            toAddList.forEach(e -> e.setOrderId(orderUpdateDTO.getId()));
            EntityTool.insertEntity(toAddList);
            saleOrderGoodsMapper.insertBatch(toAddList);
        }
        if (!CollectionUtils.isEmpty(toUpdateList)) {
            toUpdateList.forEach(e -> e.setOrderId(orderUpdateDTO.getId()));
            EntityTool.updateEntity(toUpdateList);
            saleOrderGoodsMapper.updateBatch(toUpdateList);
        }
        if (!CollectionUtils.isEmpty(toDeleteList)) {
            toDeleteList.forEach(e -> e.setDeleteFlag(DeleteFlagEnum.DATA_WARN.getFlag()));
            EntityTool.updateEntity(toDeleteList);
            saleOrderGoodsMapper.deleteBatch(toDeleteList);
        }

        // 记录变更记录
        SaleGoodsAlter goodsAlter = new SaleGoodsAlter();
        goodsAlter.setOrderId(orderUpdateDTO.getId());
        goodsAlter.setAlterReason(orderUpdateDTO.getAlterReason());
        EntityTool.insertEntity(goodsAlter);
        saleGoodsAlterMapper.insert(goodsAlter);
        // 记录变更明细
        if (!CollectionUtils.isEmpty(goodsAlterLogs)) {
            alterLogList.addAll(goodsAlterLogs);
        }
        if (CollectionUtils.isEmpty(alterLogList)) {
            throw new BizDmException("无变更项");
        }
        EntityTool.insertEntity(alterLogList);
        alterLogList.forEach(log -> log.setAlterId(goodsAlter.getId()));
        saleGoodsAlterLogMapper.insertBatch(alterLogList);

        bizLogService.log(MODULE, "修改销售订单: " + alterLogList.stream().map(SaleGoodsAlterLog::getLogDesc).collect(Collectors.joining(";")));
        bizMsgService.sendSaleOrderMsg("修改销售订单", orderUpdateDTO.getOrderNo(), orderUpdateDTO.getLiableUserId());

        return ApiResponseBody.defaultSuccess();
    }

    /**
     * 销售订单树表分页查询
     *
     * @param param
     * @return
     */
    public ApiResponseBody<PageInfo<SaleOrderVO>> page(SaleOrderPageParam param) {
        PageHelper.startPage(param.getPageNum(), param.getPageSize());
        List<SaleOrderVO> orderVOList = saleOrderMapper.list(param);
        if (!CollectionUtils.isEmpty(orderVOList)) {
            List<Long> orderIds = orderVOList.stream().map(SaleOrderVO::getId).collect(Collectors.toList());
            Map<Long, List<SaleProductObjVO>> orderOutObjMap = saleOrderMapper.selectOutObjByOrderIds(orderIds).stream().collect(Collectors.groupingBy(SaleProductObjVO::getSaleOrderId));
            Map<Long, List<SaleProductObjVO>> orderReturnObjMap = saleOrderMapper.selectReturnObjByOrderIds(orderIds).stream().collect(Collectors.groupingBy(SaleProductObjVO::getSaleOrderId));
            // 所有的订单明细（二级树表）
            List<SaleOrderGoodsWithStatVO> goodsInAllOrderList = saleOrderGoodsMapper.listWithStatByOrderIds(orderIds);
            if (!CollectionUtils.isEmpty(goodsInAllOrderList)) {
                // 更新销售明细单价、指导成本
                List<Long> craftIds = goodsInAllOrderList.stream().map(SaleOrderGoodsWithStatVO::getCraftId).distinct().collect(Collectors.toList());
                List<ProductCraftVO> craftVOList = productCraftMapper.selectByIds(craftIds);
                goodsInAllOrderList.forEach(e -> {
                    e.setSaleUnitPrice(e.calcUnitSalePrice());
                    Optional<ProductCraftVO> optional = craftVOList.stream().filter(craft -> craft.getId().equals(e.getCraftId())).findFirst();
                    optional.ifPresent(craft -> e.setGuidCost(craft.getGuideCost()));
                });
                // 订单明细按订单分组（一级树表）
                Map<Long, List<SaleOrderGoodsWithStatVO>> goodsMap = goodsInAllOrderList.stream().collect((Collectors.groupingBy(SaleOrderGoodsWithStatVO::getOrderId)));
                // 订单
                for (SaleOrderVO orderVO : orderVOList) {
                    // 订单下的销售明细
                    List<SaleOrderGoodsWithStatVO> goodsInOrderList = goodsMap.get(orderVO.getId());
                    if (!CollectionUtils.isEmpty(goodsInOrderList)) {
                        for (SaleOrderGoodsWithStatVO goods : goodsInOrderList) {
                            // 工艺销售信息
                            List<SaleProductObjVO> outObjInOrderList = orderOutObjMap.get(orderVO.getId());
                            List<SaleProductObjVO> returnObjInOrderList = orderReturnObjMap.get(orderVO.getId());
                            if (!CollectionUtils.isEmpty(outObjInOrderList)) {
                                goods.setOutQuantity(outObjInOrderList.stream().filter(e -> e.getCraftId().equals(goods.getCraftId())).count());
                            }
                            if (!CollectionUtils.isEmpty(returnObjInOrderList)) {
                                goods.setRejectQuantity(returnObjInOrderList.stream().filter(e -> e.getCraftId().equals(goods.getCraftId()) && CommonConstants.RETURN_TYPE_REJECT.equals(e.getReturnType())).count());
                                goods.setExchangeQuantity(returnObjInOrderList.stream().filter(e -> e.getCraftId().equals(goods.getCraftId()) && CommonConstants.RETURN_TYPE_EXCHANGE.equals(e.getReturnType())).count());
                            }
                            goods.setUnOutQuantity(goods.getSaleQuantity() < goods.getOutQuantity() ? 0 : goods.getSaleQuantity() - goods.getOutQuantity());
                            goods.updateAmountAndProfit();
                        }
                    }
                    orderVO.setGoodsList(goodsInOrderList);
                    if (!CollectionUtils.isEmpty(goodsInOrderList)) {
                        for (SaleOrderGoodsWithStatVO goodsWithStat : goodsInOrderList) {
                            orderVO.setSaleQuantity(orderVO.getSaleQuantity() + goodsWithStat.getSaleQuantity());
                            orderVO.setOutQuantity(orderVO.getOutQuantity() + goodsWithStat.getOutQuantity());
                            orderVO.setRejectQuantity(orderVO.getRejectQuantity() + goodsWithStat.getRejectQuantity());
                            orderVO.setExchangeQuantity(orderVO.getExchangeQuantity() + goodsWithStat.getExchangeQuantity());
                            orderVO.setActualProfit(orderVO.getActualProfit().add(goodsWithStat.getActualProfit()));
                            orderVO.setActualSalesAmount(orderVO.getActualSalesAmount().add(goodsWithStat.getActualSalesAmount()));
                            orderVO.setOutCost(orderVO.getOutCost().add(goodsWithStat.getGuidCost().multiply(BigDecimal.valueOf(goodsWithStat.getOutQuantity()))));
                            orderVO.setSaleCost(orderVO.getSaleCost().add(goodsWithStat.getGuidCost().multiply(BigDecimal.valueOf(goodsWithStat.getSaleQuantity()))));
                            orderVO.setExpectedProfit(OrderTool.bdAdd(orderVO.getExpectedProfit(), goodsWithStat.getExpectedProfit()));
                        }
                    }
                    orderVO.setEarnedProfit(orderVO.getPaymentAmount().subtract(orderVO.getOutCost()));
                }
            }
        }
        PageInfo<SaleOrderVO> pageInfo = new PageInfo<>(orderVOList);
        return ApiResponseBody.defaultSuccess(pageInfo);
    }

    /**
     * 变更记录列表
     *
     * @param pageNum
     * @param pageSize
     * @param orderId
     * @return
     */
    public ApiResponseBody alterPage(Integer pageNum, Integer pageSize, String orderId) {
        PageHelper.startPage(pageNum, pageSize);
        return ApiResponseBody.defaultSuccess(new PageInfo<>(saleGoodsAlterMapper.selectByOrderId(Long.parseLong(orderId))));
    }

    /**
     * 变更记录详情
     *
     * @param id
     * @return
     */
    public ApiResponseBody alterInfo(String id) {
        return ApiResponseBody.defaultSuccess(saleGoodsAlterLogMapper.selectByAlterId(Long.parseLong(id)));
    }

    /**
     * 销售订单-统计总金额、客户数量、总单数
     *
     * @return
     */
    public ApiResponseBody<StatSaleOverallVO> statOverall() {
        StatSaleOverallVO overallVO = new StatSaleOverallVO();
        List<SaleOrder> orderList = saleOrderMapper.listAll();
        if (!CollectionUtils.isEmpty(orderList)) {
            overallVO.setOrderQuantity(orderList.size());
            overallVO.setClientQuantity(orderList.stream().collect(Collectors.groupingBy(SaleOrder::getClientId)).keySet().size());
            List<Long> orderIds = orderList.stream().map(SaleOrder::getId).collect(Collectors.toList());
            List<SaleOrderGoods> goodsList = saleOrderGoodsMapper.listByOrderIds(orderIds);
            if (!CollectionUtils.isEmpty(goodsList)) {
                BigDecimal orderAmount = new BigDecimal("0");
                for (SaleOrderGoods goods : goodsList) {
                    orderAmount = orderAmount.add(goods.getSalePrice());
                }
                overallVO.setOrderAmount(orderAmount);
            }
        }

        return ApiResponseBody.defaultSuccess(overallVO);
    }

    /**
     * 逐月统计收款量、销售额
     *
     * @param searchYear
     * @return
     */
    public ApiResponseBody<List<StatSaleOrderVO>> statSale(Year searchYear) {
        return ApiResponseBody.defaultSuccess(saleOrderMapper.statSaleOrderByYear(searchYear));
    }


    /****************************************** 出库 **************************************************/

    /**
     * 产品出库-查询工艺选项
     *
     * @param orderId
     * @return
     */
    public ApiResponseBody getCraftList(String orderId) {
        List<ProductDTO> productDTOList = new ArrayList<>();
        List<SaleOrderGoodsVO> goodsList = saleOrderGoodsMapper.listByOrderId(Long.parseLong(orderId));
        if (!CollectionUtils.isEmpty(goodsList)) {
            Map<Long, List<SaleOrderGoodsVO>> goodsMap = goodsList.stream().collect(Collectors.groupingBy(SaleOrderGoodsVO::getProductId));
            goodsMap.forEach((productId, children) -> {
                SaleOrderGoodsVO productVO = goodsList.stream().filter(e -> e.getProductId().equals(productId)).findFirst().get();
                ProductDTO productDTO = new ProductDTO();
                productDTO.setId(productVO.getProductId());
                productDTO.setName(productVO.getProductName());
                productDTO.setModel(productVO.getProductModel());
                if (!CollectionUtils.isEmpty(children)) {
                    List<ProductCraftVO> craftList = new ArrayList<>();
                    children.forEach(e -> {
                        ProductCraftVO craft = new ProductCraftVO();
                        craft.setId(e.getCraftId());
                        craft.setCraftCode(e.getCraftCode());
                        craftList.add(craft);
                    });
                    productDTO.setCraftList(craftList);
                }
                productDTOList.add(productDTO);
            });
        }
        return ApiResponseBody.defaultSuccess(productDTOList);
    }

    /**
     * 产品出库-查询可用库存
     *
     * @param orderId
     * @param craftId
     * @return
     */
    public ApiResponseBody getAvailableCraftInfo(String orderId, String craftId) {
        SaleOutCraftDetailVO craftDetailVO = saleOrderMapper.selectCraftDetail(Long.parseLong(orderId), Long.parseLong(craftId));
        if (!Objects.isNull(craftDetailVO)) {
            craftDetailVO.setObjList(saleOrderMapper.selectAvailableObj(Long.parseLong(craftId)));
        }
        return ApiResponseBody.defaultSuccess(craftDetailVO);
    }

    /**
     * 产品出库-出库确认
     *
     * @param qo
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    public ApiResponseBody outConfirm(SaleOutConfirmQo qo) {
        if (Strings.isNullOrEmpty(qo.getObjIds())) {
            throw new BizDmException("出库产品为空");
        }
        saleOrderMapper.outBatch(qo.getOrderId(),
                qo.getOutDate(),
                qo.getOrderId(),
                qo.getOutUserName(),
                qo.getOutRemark(),
                qo.getOutPic(),
                Arrays.stream(qo.getObjIds().split(",")).map(Long::parseLong).collect(Collectors.toList()));

        SaleOrder order = saleOrderMapper.selectById(qo.getOrderId());
        String objNoStr = productOrderMapper.getObjsByIds(Arrays.stream(qo.getObjIds().split(",")).map(Long::parseLong).collect(Collectors.toList()))
                .stream()
                .map(ProductObject::getObjectNo)
                .collect(Collectors.joining(","));
        bizLogService.log(MODULE, String.format("销售订单出库 %s : %s", order.getOrderNo(), objNoStr));
        return ApiResponseBody.defaultSuccess();
    }

    /**
     * 删除已出库产品id
     *
     * @param objId
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    public ApiResponseBody deleteOutByObjId(String objId) {
        saleOrderMapper.cleanOutByObjIds(Collections.singletonList(Long.parseLong(objId)));

        SaleProductObjVO objVO = saleOrderMapper.selectProductObjectById(Long.parseLong(objId));
        bizLogService.log(MODULE, "删除已出库产品: " + objVO.getObjectNo());
        return ApiResponseBody.defaultSuccess();
    }

    /**
     * 查询已出库列表
     *
     * @return
     */
    public ApiResponseBody<SaleOutDetailVO> outPage(String orderId) {
        SaleOutDetailVO outDetailVO = new SaleOutDetailVO();
        BigDecimal outTotalSalePrice = new BigDecimal("0");
        List<SaleGoodsVO> goodsVOList = new ArrayList<>();
        // 已出库产品对象
        List<SaleProductObjVO> objectList = saleOrderMapper.selectOutObjByOrderId(Long.parseLong(orderId));
        if (!CollectionUtils.isEmpty(objectList)) {
            // 销售明细 -- 销售数量
            List<SaleOrderGoodsVO> goodsList = saleOrderGoodsMapper.listByOrderId(Long.parseLong(orderId));
            Map<Long, List<SaleProductObjVO>> objMap = objectList.stream().collect(Collectors.groupingBy(SaleProductObjVO::getCraftId));
            goodsVOList = saleOrderMapper.selectOutGoodsByCraftIds(new ArrayList<>(objMap.keySet()));
            for (SaleGoodsVO goodsVO : goodsVOList) {
                goodsVO.setObjList(objMap.get(goodsVO.getCraftId()));
                goodsVO.setOutQuantity(objMap.get(goodsVO.getCraftId()).size());
                Optional<SaleOrderGoodsVO> optional = goodsList.stream().filter(e -> e.getCraftId().equals(goodsVO.getCraftId())).findFirst();
                if (optional.isPresent()) {
                    SaleOrderGoods goods = optional.get();
                    goodsVO.setUnitSalePrice(goods.getUnitSalePrice());
                    goodsVO.setSaleQuantity(goods.getSaleQuantity());
                    goodsVO.setUnOutQuantity(goods.getSaleQuantity() - objMap.get(goodsVO.getCraftId()).size());
                    // 已出库产品售价总额
                    outTotalSalePrice = outTotalSalePrice.add(goods.getUnitSalePrice().multiply(new BigDecimal(goodsVO.getOutQuantity())));
                }
            }
        }
        outDetailVO.setOutTotalSalePrice(outTotalSalePrice);
        outDetailVO.setOutList(goodsVOList);
        outDetailVO.setOutQuantity(CollectionUtils.isEmpty(goodsVOList) ? 0 : goodsVOList.stream().map(SaleGoodsVO::getOutQuantity).reduce(0, Integer::sum));
        return ApiResponseBody.defaultSuccess(outDetailVO);
    }

    /**
     * 查询产品对象信息
     *
     * @param objId
     * @return
     */
    public ApiResponseBody<SaleProductObjVO> queryObj(String objId) {
        SaleProductObjVO objVO = saleOrderMapper.selectProductObjectById(Long.parseLong(objId));
        return ApiResponseBody.defaultSuccess(objVO);
    }

    /**
     * 更新产品的出库信息
     *
     * @param objVO
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    public ApiResponseBody updateOutObj(SaleProductObjVO objVO) {
        SaleProductObjVO record = saleOrderMapper.selectProductObjectById(objVO.getId());
        record.setOutDate(objVO.getOutDate());
        record.setOutRemark(objVO.getOutRemark());
        record.setOutUserId(objVO.getOutUserId());
        record.setOutUserName(objVO.getOutUserName());
        record.setOutPic(objVO.getOutPic());
        saleOrderMapper.updateProductObjByPrimaryKey(record);
        return ApiResponseBody.defaultSuccess();
    }

    /****************************************** 退换货 **************************************************/

    /**
     * 批量退换货
     *
     * @param param
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    public ApiResponseBody returnBatch(SaleReturnParam param) {
        if (Objects.isNull(param.getOrderId())) {
            throw new BizDmException("请选择销售订单");
        }
        if (Strings.isNullOrEmpty(param.getReturnType())) {
            throw new BizDmException("请选择退换货类型");
        }
        if (CollectionUtils.isEmpty(param.getObjList())) {
            throw new BizDmException("请选择退换货产品");
        }
        // 退换货记录
        List<SaleOrderReturn> saleOrderReturnList = param.getObjList().stream().map(e -> new SaleOrderReturn(e, param)).collect(Collectors.toList());
        EntityTool.insertEntity(saleOrderReturnList);
        saleOrderMapper.returnBatch(saleOrderReturnList);
        // 产品归库
        saleOrderMapper.cleanOutByObjIds(saleOrderReturnList.stream().map(SaleOrderReturn::getObjId).collect(Collectors.toList()));
        // 换货
        List<Long> objIds = param.getObjList().stream().map(SaleReturnObj::getNewObjectId).collect(Collectors.toList());
        if (!CollectionUtils.isEmpty(objIds)) {
            saleOrderMapper.updateSaleOrderIdByIds(objIds, param.getOrderId(), MyEntityTool.getUpdateInfo());
        }
        // 更新产品的检验状态
        List<SaleReturnObj> unqualifiedObjList = param.getObjList().stream().filter(e -> CommonConstants.RETURN_STATUS_UNQUALIFIED.equals(e.getReturnStatus())).collect(Collectors.toList());
        if (!CollectionUtils.isEmpty(unqualifiedObjList)) {
            saleOrderMapper.updateBatchUnqualifiedObj(unqualifiedObjList);
        }

        List<Long> sourceObjIds = param.getObjList().stream().map(SaleReturnObj::getObjectId).collect(Collectors.toList());

        if (CommonConstants.RETURN_TYPE_REJECT.equals(param.getReturnType())) {
            String objNoStr = productOrderMapper.getObjsByIds(sourceObjIds).stream()
                    .map(ProductObject::getObjectNo)
                    .collect(Collectors.joining(","));
            bizLogService.log(MODULE, "退货： " + objNoStr);
        } else if (CommonConstants.RETURN_TYPE_EXCHANGE.equals(param.getReturnType())) {
            List<ProductObject> sourceObjList = productOrderMapper.getObjsByIds(sourceObjIds);
            List<ProductObject> newObjList = productOrderMapper.getObjsByIds(objIds);
            List<String> logs = new ArrayList<>();
            param.getObjList().forEach(e -> {
                ProductObject source = sourceObjList.stream().filter(obj -> obj.getObjectId().equals(e.getObjectId())).findFirst().get();
                ProductObject target = newObjList.stream().filter(obj -> obj.getObjectId().equals(e.getNewObjectId())).findFirst().get();
                logs.add(source.getObjectNo() + " -> " + target.getObjectNo());
            });
            bizLogService.log(MODULE, "换货： " + String.join(",", logs));
        }
        return ApiResponseBody.defaultSuccess();
    }

    /**
     * 删除退换货产品对象
     *
     * @param objId
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    public ApiResponseBody deleteReturn(String saleOrderReturnId) {
        saleOrderMapper.deleteReturn(Long.parseLong(saleOrderReturnId), MyEntityTool.getUpdateInfo());
        return ApiResponseBody.defaultSuccess();
    }

    /**
     * returns in saleOrder
     *
     * @param orderId
     * @return
     */
    public ApiResponseBody<SaleReturnDetailVO> returnPage(String orderId) {
        SaleReturnDetailVO detailVO = new SaleReturnDetailVO();

        List<SaleGoodsVO> goodsVOList = new ArrayList<>();
        // 退换货产品对象
        List<SaleProductObjVO> objectList = saleOrderMapper.selectReturnObjByOrderId(Long.parseLong(orderId));
        if (!CollectionUtils.isEmpty(objectList)) {
            // 二级树表：产品对象
            Map<Long, List<SaleProductObjVO>> objMap = objectList.stream().collect(Collectors.groupingBy(SaleProductObjVO::getCraftId));
            // 销售明细 -- 销售数量
            List<SaleOrderGoodsVO> goodsList = saleOrderGoodsMapper.listByOrderId(Long.parseLong(orderId));
            // 一级树表：工艺信息
            goodsVOList = saleOrderMapper.selectOutGoodsByCraftIds(new ArrayList<>(objMap.keySet()));
            for (SaleGoodsVO goodsVO : goodsVOList) {
                goodsVO.setObjList(objMap.get(goodsVO.getCraftId()));
                goodsVO.setOutQuantity(objMap.get(goodsVO.getCraftId()).size());
                Optional<SaleOrderGoodsVO> optional = goodsList.stream().filter(e -> e.getCraftId().equals(goodsVO.getCraftId())).findFirst();
                if (optional.isPresent()) {
                    SaleOrderGoods goods = optional.get();
                    goodsVO.setUnitSalePrice(goods.getUnitSalePrice());
                    goodsVO.setReturnQuantity(objMap.get(goodsVO.getCraftId()).size());
                    goodsVO.setObjList(objMap.get(goodsVO.getCraftId()));
                }
            }
        }
        detailVO.setReturnList(goodsVOList);

        if (!CollectionUtils.isEmpty(goodsVOList)) {
            goodsVOList.forEach(e -> {
                if (!CollectionUtils.isEmpty(e.getObjList())) {
                    List<SaleProductObjVO> rejectList = e.getObjList().stream().filter(obj -> CommonConstants.RETURN_TYPE_REJECT.equals(obj.getReturnType())).collect(Collectors.toList());
                    List<SaleProductObjVO> exchangeList = e.getObjList().stream().filter(obj -> CommonConstants.RETURN_TYPE_EXCHANGE.equals(obj.getReturnType())).collect(Collectors.toList());
                    if (!CollectionUtils.isEmpty(rejectList)) {
                        detailVO.setRejectAmount(detailVO.getRejectAmount().add(e.getUnitSalePrice().multiply(new BigDecimal(rejectList.size()))));
                        detailVO.setRejectQuantity(detailVO.getRejectQuantity() + rejectList.size());
                    }
                    if (!CollectionUtils.isEmpty(exchangeList)) {
                        detailVO.setExchangeAmount(detailVO.getExchangeAmount().add(e.getUnitSalePrice().multiply(new BigDecimal(exchangeList.size()))));
                        detailVO.setExchangeQuantity(detailVO.getExchangeQuantity() + exchangeList.size());
                    }
                }
            });
        }
        return ApiResponseBody.defaultSuccess(detailVO);
    }

    /**
     * 更新退换货产品的出库信息
     *
     * @param objVO
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    public ApiResponseBody updateReturnObj(SaleProductObjVO objVO) {
        SaleProductObjVO record = saleOrderMapper.selectProductObjectById(objVO.getId());
        record.setReturnDate(Timestamp.valueOf(LocalDateTime.now()));
        record.setReturnReason(objVO.getReturnReason());
        record.setReturnStatus(objVO.getReturnStatus());
        record.setReturnPic(objVO.getReturnPic());
        record.setStatus(CommonConstants.RETURN_STATUS_QUALIFIED.equals(objVO.getReturnStatus()) ? CommonConstants.OBJECT_STATUS_QUALIFIED : CommonConstants.OBJECT_STATUS_DEFECTIVE);
        if (CommonConstants.RETURN_TYPE_EXCHANGE.equals(objVO.getReturnType()) && !record.getReturnNewObjId().equals(objVO.getReturnNewObjId())) {
            // 解绑换货
            Long oldExchangeObjId = record.getReturnNewObjId();
            saleOrderMapper.updateSaleOrderIdByIds(Collections.singletonList(oldExchangeObjId), null, MyEntityTool.getUpdateInfo());
            // 绑定新货
            record.setReturnNewObjId(objVO.getReturnNewObjId());
            saleOrderMapper.updateSaleOrderIdByIds(Collections.singletonList(objVO.getReturnNewObjId()), record.getSaleOrderId(), MyEntityTool.getUpdateInfo());
        }
        saleOrderMapper.updateProductObjByPrimaryKey(record);
        return ApiResponseBody.defaultSuccess();
    }

    public ApiResponseBody<SaleProductObjVO> queryReturnObj(String saleOrderReturnId) {
        SaleProductObjVO objVO = saleOrderMapper.selectReturnObjBySaleOrderReturnId(Long.parseLong(saleOrderReturnId));
        return ApiResponseBody.defaultSuccess(objVO);
    }


    /***************************************** 工具方法 *******************************/
    /**
     * 更新合同状态
     * 已完成 = 销售数量 >= 出库数量
     *
     * @param saleOrderId
     * @deprecated 表单填写
     */
    private void updateSaleOrderStatus(Long saleOrderId) {
        String status = CommonConstants.CONTRACT_PROGRESS;
        List<SaleOrderGoodsVO> goodsList = saleOrderGoodsMapper.listByOrderId(saleOrderId);
        if (!CollectionUtils.isEmpty(goodsList)) {
            List<SaleProductObjVO> objVOList = saleOrderMapper.selectOutObjByOrderId(saleOrderId);
            if (!CollectionUtils.isEmpty(objVOList)) {
                status = CommonConstants.CONTRACT_FINISHED;
                Map<Long, List<SaleProductObjVO>> objMap = objVOList.stream().collect(Collectors.groupingBy(SaleProductObjVO::getCraftId));
                for (SaleOrderGoods goods : goodsList) {
                    if (goods.getSaleQuantity() > objMap.get(goods.getCraftId()).size()) {
                        status = CommonConstants.CONTRACT_PROGRESS;
                    }
                }
            }
        }
        saleOrderMapper.updateStatus(saleOrderId, status, CommonConstants.CONTRACT_FINISHED.equals(status) ? LocalDate.now() : null);
    }

    /**
     * 订单基本信息变更
     *
     * @param before
     * @param after
     * @return
     */
    private List<SaleGoodsAlterLog> getOrderInfoChangeLogs(SaleOrder before, SaleOrder after) {
        List<SaleGoodsAlterLog> logList = new ArrayList<>();
        if (OrderTool.isChangedValue(before.getOrderName(), after.getOrderName())) {
            logList.add(new SaleGoodsAlterLog("订单名称", before.getOrderName(), after.getOrderName()));
        }

        if (OrderTool.isChangedValue(before.getSignDate(), after.getSignDate())) {
            logList.add(new SaleGoodsAlterLog("签订日期", before.getSignDate().toString(), after.getSignDate().toString()));
        }

        if (OrderTool.isChangedValue(before.getPlannedDeliveryDate(), after.getPlannedDeliveryDate())) {
            logList.add(new SaleGoodsAlterLog("计划交付日期", before.getPlannedDeliveryDate().toString(), after.getPlannedDeliveryDate().toString()));
        }

        if (OrderTool.isChangedBigDecimal(before.getPaymentAmount(), after.getPaymentAmount())) {
            logList.add(new SaleGoodsAlterLog("累计支付金额", before.getPaymentAmount().toString(), after.getPaymentAmount().toString()));
        }

        if (OrderTool.isChangedValue(before.getClientId(), after.getClientId())) {
            SaleGoodsAlterLog log = new SaleGoodsAlterLog();
            log.setAlterItem("客户名称");
            if (!Objects.isNull(before.getClientId())) {
                Client clientBefore = clientMapper.selectByPrimaryKey(before.getClientId());
                log.setAlterSource(clientBefore.getClientName());
            }
            if (!Objects.isNull(after.getClientId())) {
                Client clientAfter = clientMapper.selectByPrimaryKey(after.getClientId());
                log.setAlterTarget(clientAfter.getClientName());
            }
            logList.add(log);
        }

        if (OrderTool.isChangedValue(before.getLiableUserName(), after.getLiableUserName())) {
            logList.add(new SaleGoodsAlterLog("责任人", before.getLiableUserName(), after.getLiableUserName()));
        }

        if (OrderTool.isChangedValue(before.getIndustry(), after.getIndustry())) {
            logList.add(new SaleGoodsAlterLog("行业类型", before.getIndustry(), after.getIndustry()));
        }

        if (OrderTool.isChangedValue(before.getProjectType(), after.getProjectType())) {
            logList.add(new SaleGoodsAlterLog("项目类型", before.getProjectType(), after.getProjectType()));
        }

        if (OrderTool.isChangedValue(before.getStatus(), after.getStatus())) {
            logList.add(new SaleGoodsAlterLog("合同状态", before.getStatus(), after.getStatus()));
        }

        if (OrderTool.isChangedValue(before.getOrderEnclosure(), after.getOrderEnclosure())) {
            SaleGoodsAlterLog log = new SaleGoodsAlterLog();
            log.setAlterItem("合同附件");
            if (!Strings.isNullOrEmpty(before.getOrderEnclosure())) {
                log.setAlterSource(getEnclosureWithoutToken(before.getOrderEnclosure()));
            }
            if (!Strings.isNullOrEmpty(after.getOrderEnclosure())) {
                log.setAlterTarget(getEnclosureWithoutToken(after.getOrderEnclosure()));
            }
            logList.add(log);
        }
        return logList;
    }

    private List<SaleGoodsAlterLog> getOrderGoodsChangeLogs(List<SaleOrderGoodsVO> beforeList, List<SaleOrderGoodsVO> afterList, List<SaleOrderGoods> toAddList, List<SaleOrderGoods> toUpdateList, List<SaleOrderGoods> toDeleteList) {
        if (CollectionUtils.isEmpty(beforeList) || CollectionUtils.isEmpty(afterList)) {
            throw new BizDmException("销售明细为空");
        }
        List<ProductCraftVO> craftVOList = productCraftMapper.selectByIds(afterList.stream().map(SaleOrderGoodsVO::getCraftId).collect(Collectors.toList()));
        List<ProductParam> paramList = productParamMapper.selectByIds(afterList.stream().map(SaleOrderGoodsVO::getParamId).collect(Collectors.toList()));
        Map<Long, String> craftProductNameMap = craftVOList.stream().collect(Collectors.toMap(ProductCraftVO::getId, ProductCraftVO::getProductName, (key1, key2) -> key1));
        Map<Long, String> craftCraftCodeMap = craftVOList.stream().collect(Collectors.toMap(ProductCraftVO::getId, ProductCraftVO::getCraftCode, (key1, key2) -> key1));
        Map<Long, String> paramParamNameMap = paramList.stream().collect(Collectors.toMap(ProductParam::getId, ProductParam::getParamName, (key1, key2) -> key1));

        List<SaleGoodsAlterLog> logList = new ArrayList<>();
        List<Long> usedCraftIds = new ArrayList<>();
        afterList.forEach(after -> {
            after.setProductName(craftProductNameMap.get(after.getCraftId()));
            after.setCraftCode(craftCraftCodeMap.get(after.getCraftId()));
            after.setParamName(paramParamNameMap.get(after.getParamId()));
            Optional<SaleOrderGoodsVO> optional = beforeList.stream().filter(e -> e.getId().equals(after.getId()) || e.getCraftId().equals(after.getCraftId())).findFirst();
            if (optional.isPresent()) {
                // 记录未删除的记录
                usedCraftIds.add(optional.get().getCraftId());
                if (isChangedOrderGoods(optional.get(), after)) {
                    MyEntityTool.keepCrateAndDeleteFlag(optional.get(), after);
                    logList.add(new SaleGoodsAlterLog("修改产品明细", optional.get().getDetail(), after.getDetail()));
                    toUpdateList.add(after);
                }
            } else {
                toAddList.add(after);
                logList.add(new SaleGoodsAlterLog("新增产品明细", "", after.getDetail()));
            }
        });
        // 待删除
        List<SaleOrderGoodsVO> unwantedList = beforeList.stream().filter(e -> !usedCraftIds.contains(e.getCraftId())).collect(Collectors.toList());
        if (!CollectionUtils.isEmpty(unwantedList)) {
            toDeleteList.addAll(unwantedList);
            unwantedList.forEach(e -> {
                logList.add(new SaleGoodsAlterLog("删除产品明细", e.getDetail(), ""));
            });
        }
        return logList;
    }

    /**
     * 比较销售明细是否变化
     *
     * @param before
     * @param after
     * @return
     */
    private boolean isChangedOrderGoods(SaleOrderGoods before, SaleOrderGoods after) {
        boolean isDiffCraft = OrderTool.isChangedValue(before.getCraftId(), after.getCraftId());
        boolean isDiffQuantity = OrderTool.isChangedValue(before.getSaleQuantity(), after.getSaleQuantity());
        boolean isDiffPrice = OrderTool.isChangedBigDecimal(before.getSalePrice(), after.getSalePrice());
        boolean isDiffParam = OrderTool.isChangedValue(before.getParamId(), after.getParamId());
        return isDiffCraft || isDiffQuantity || isDiffPrice || isDiffParam;
    }

    /**
     * 去除token信息，获取文件名
     *
     * @param enclosure token_filename,token_filename
     * @return enclosureWithoutToken filename,filename
     */
    private String getEnclosureWithoutToken(String enclosure) {
        return Arrays.stream(enclosure.split(","))
                .map(fileWithToken -> fileWithToken.split("\\?")[0])
                .collect(Collectors.joining(",", "", ""));
    }


    /**
     * 校验订单名称
     *
     * @param order
     */
    private void checkOrder(SaleOrder order) {
        if (Strings.isNullOrEmpty(order.getOrderNo())) {
            throw new BizDmException("订单编号为空");
        }
        if (Strings.isNullOrEmpty(order.getOrderName())) {
            throw new BizDmException("订单名称为空");
        }
        if (Strings.isNullOrEmpty(order.getStatus())) {
            throw new BizDmException("订单状态为空");
        }
        if (Objects.isNull(order.getPaymentAmount())) {
            throw new BizDmException("累计收款金额为空");
        }
        if (saleOrderMapper.isExistedOrderNo(order)) {
            throw new BizDmException("订单编号已存在");
        }
    }
}
