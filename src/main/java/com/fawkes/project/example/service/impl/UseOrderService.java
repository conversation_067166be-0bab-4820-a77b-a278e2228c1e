package com.fawkes.project.example.service.impl;

import com.fawkes.core.base.api.ApiResponseBody;
import com.fawkes.core.enums.DeleteFlagEnum;
import com.fawkes.core.utils.EntityTool;
import com.fawkes.project.example.common.constants.CommonConstants;
import com.fawkes.project.example.common.exception.BizDmException;
import com.fawkes.project.example.common.mapper.ProductCraftMapper;
import com.fawkes.project.example.common.mapper.ProductOrderMapper;
import com.fawkes.project.example.common.mapper.UseOrderMapper;
import com.fawkes.project.example.common.model.ProductObject;
import com.fawkes.project.example.common.model.UseOrder;
import com.fawkes.project.example.common.model.UseOrderGoods;
import com.fawkes.project.example.common.utils.OrderTool;
import com.fawkes.project.example.domain.param.UseOrderPageParam;
import com.fawkes.project.example.domain.vo.*;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.google.common.base.Strings;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.sql.Timestamp;
import java.time.LocalDate;
import java.time.Year;
import java.util.*;
import java.util.stream.Collectors;
import java.util.stream.IntStream;

/**
 * 设备领用管理
 *
 * <AUTHOR>
 */
@Service
@Slf4j
public class UseOrderService {
    private final String MODULE = "领用订单";
    @Resource
    UseOrderMapper useOrderMapper;
    @Resource
    ProductCraftMapper productCraftMapper;
    @Resource
    BizLogService bizLogService;
    @Resource
    ProductOrderMapper productOrderMapper;

    public ApiResponseBody getLatestOrderNo() {
        LocalDate now = LocalDate.now();
        return ApiResponseBody.defaultSuccess(OrderTool.genUseOrderNoFromPrev(now, useOrderMapper.getMaxUseOrderNo(now)));
    }

    public ApiResponseBody getObjList(String craftId) {
        return ApiResponseBody.defaultSuccess(useOrderMapper.listAvailableObjByCraftId(Long.parseLong(craftId)));
    }

    @Transactional(rollbackFor = Exception.class)
    public ApiResponseBody add(UseOrderVO orderVO) {
        checkOrder(orderVO);
        EntityTool.insertEntity(orderVO);
        useOrderMapper.insert(orderVO);
        if (!CollectionUtils.isEmpty(orderVO.getGoodsList())) {
            // 领用设备
            useOrderMapper.useBatch(orderVO.getId(), orderVO.getGoodsList().stream().map(UseOrderGoodsVO::getObjId).collect(Collectors.toList()));
            // 领用记录
            orderVO.getGoodsList().forEach(e -> e.setOrderId(orderVO.getId()));
            EntityTool.insertEntity(orderVO.getGoodsList());
            useOrderMapper.logBatch(orderVO.getGoodsList());
        }
        bizLogService.log(MODULE, "新增领用订单: " + orderVO.getOrderNo());
        return ApiResponseBody.defaultSuccess();
    }

    public ApiResponseBody<UseOrderVO> query(String orderId) {
        UseOrderVO orderVO = new UseOrderVO();
        UseOrder record = useOrderMapper.selectById(Long.parseLong(orderId));
        if (!Objects.isNull(record)) {
            BeanUtils.copyProperties(record, orderVO);
        }
        orderVO.setGoodsList(useOrderMapper.selectUseOrderGoodsByOrderIds(Collections.singletonList(Long.parseLong(orderId))));
        return ApiResponseBody.defaultSuccess(orderVO);
    }

    @Transactional(rollbackFor = Exception.class)
    public ApiResponseBody update(UseOrderVO orderVO) {
        checkOrder(orderVO);
        EntityTool.updateEntity(orderVO);
        useOrderMapper.update(orderVO);

        List<UseOrderGoodsVO> oldGoodsList = useOrderMapper.selectUseOrderGoodsByOrderIds(Collections.singletonList(orderVO.getId()));
        List<UseOrderGoods> toAddGoodsList = new ArrayList<>();
        List<UseOrderGoods> toReturnGoodsList = new ArrayList<>();
        List<UseOrderGoodsVO> toDeleteGoodsList = new ArrayList<>();
        if (!CollectionUtils.isEmpty(orderVO.getGoodsList())) {
            orderVO.getGoodsList().forEach(e -> {
                //  新增
                if (Objects.isNull(e.getId())) {
                    e.setOrderId(orderVO.getId());
                    toAddGoodsList.add(e);
                } else if (Objects.isNull(e.getUseReturnTime()) && !Strings.isNullOrEmpty(e.getReturnStatus())) {
                    //  新增归还
                    e.setUseReturnTime(new Timestamp(System.currentTimeMillis()));
                    toReturnGoodsList.add(e);
                }
            });
            List<Long> goodsIds = orderVO.getGoodsList().stream().map(UseOrderGoodsVO::getId).filter(Objects::nonNull).collect(Collectors.toList());
            toDeleteGoodsList = oldGoodsList.stream().filter(e -> !goodsIds.contains(e.getId())).collect(Collectors.toList());
        } else {
            toDeleteGoodsList = oldGoodsList;
        }
        // 新增设备领用
        if (!CollectionUtils.isEmpty(toAddGoodsList)) {
            useOrderMapper.useBatch(orderVO.getId(), toAddGoodsList.stream().map(UseOrderGoods::getObjId).collect(Collectors.toList()));
            EntityTool.insertEntity(toAddGoodsList);
            useOrderMapper.logBatch(toAddGoodsList);

            String objNos = productOrderMapper.getObjsByIds(toAddGoodsList.stream().map(UseOrderGoods::getObjId).collect(Collectors.toList())).stream()
                    .map(ProductObject::getObjectNo)
                    .collect(Collectors.joining(","));
            bizLogService.log(MODULE, String.format("设备领用 %s : %s", orderVO.getOrderNo(), objNos));
        }
        // 删除物品领用
        if (!CollectionUtils.isEmpty(toDeleteGoodsList)) {
            toDeleteGoodsList.forEach(e -> e.setDeleteFlag(DeleteFlagEnum.DATA_WARN.getFlag()));
            EntityTool.updateEntity(toDeleteGoodsList);
            useOrderMapper.updateLogBatch(toDeleteGoodsList);
            // 删除处于“领用”状态的记录，自动归还
            toDeleteGoodsList.forEach(e -> {
                if (Objects.isNull(e.getUseReturnTime())) {
                    log.info("删除物品领用: {}", e.getObjectNo());
                    e.setReturnReason("设备领用删除");
                    toReturnGoodsList.add(e);
                }
            });
        }
        // 归还物品
        if (!CollectionUtils.isEmpty(toReturnGoodsList)) {
            // 更新记录
            toReturnGoodsList.forEach(e -> {
                if (!Objects.isNull(e.getUseReturnTime())) {
                    e.setDeleteFlag(DeleteFlagEnum.DATA_OK.getFlag());
                }
            });
            EntityTool.updateEntity(toReturnGoodsList);
            useOrderMapper.updateLogBatch(toReturnGoodsList);
            List<ProductObjVO> objList = toReturnGoodsList.stream().map(e -> {
                if (CommonConstants.RETURN_STATUS_UNQUALIFIED.equals(e.getReturnStatus())) {
                    return new ProductObjVO(e.getObjId(), CommonConstants.OBJECT_STATUS_DEFECTIVE, e.getReturnReason(), e.getReturnPic());
                } else {
                    return new ProductObjVO(e.getObjId(), CommonConstants.OBJECT_STATUS_QUALIFIED, null, null);
                }
            }).collect(Collectors.toList());
            useOrderMapper.returnUse(objList);

            String objNos = productOrderMapper.getObjsByIds(toReturnGoodsList.stream().map(UseOrderGoods::getObjId).collect(Collectors.toList())).stream()
                    .map(ProductObject::getObjectNo)
                    .collect(Collectors.joining(","));
            bizLogService.log(MODULE, String.format("设备归还 %s : %s", orderVO.getOrderNo(), objNos));
        }
        return ApiResponseBody.defaultSuccess();
    }

    @Transactional(rollbackFor = Exception.class)
    public ApiResponseBody deleteByIds(String ids) {
        // 删除订单
        List<Long> orderIds = Arrays.stream(ids.split(",")).map(Long::parseLong).collect(Collectors.toList());
        useOrderMapper.deleteByIds(orderIds);
        // 归还设备
        List<ProductObjVO> oldGoodsList = useOrderMapper.listProductObjByUseOrderIds(orderIds);
        if (!CollectionUtils.isEmpty(oldGoodsList)) {
            List<Long> toDelUseObjIds = oldGoodsList.stream().map(ProductObjVO::getId).collect(Collectors.toList());
            useOrderMapper.useBatch(null, toDelUseObjIds);
        }

        String orderNos = useOrderMapper.selectByIds(orderIds).stream()
                .map(UseOrder::getOrderNo)
                .collect(Collectors.joining(","));

        bizLogService.log(MODULE, String.format("删除订单 %s", orderNos));
        return ApiResponseBody.defaultSuccess();
    }

    public ApiResponseBody<PageInfo<UseOrderVO>> page(UseOrderPageParam param) {
        PageHelper.startPage(param.getPageNum(), param.getPageSize());
        List<UseOrderVO> orderVOList = useOrderMapper.list(param);
        if (!CollectionUtils.isEmpty(orderVOList)) {
            List<UseOrderGoodsVO> goodsList = useOrderMapper.selectUseOrderGoodsByOrderIds(orderVOList.stream().map(UseOrderVO::getId).collect(Collectors.toList()));
            if (!CollectionUtils.isEmpty(goodsList)) {
                Map<Long, List<UseOrderGoodsVO>> orderObjMap = goodsList.stream().collect(Collectors.groupingBy(UseOrderGoods::getOrderId));
                orderVOList.forEach(e -> e.setGoodsList(orderObjMap.get(e.getId())));
            }
        }
        return ApiResponseBody.defaultSuccess(new PageInfo<>(orderVOList));
    }

    public ApiResponseBody<StatUseOrderOverallVO> statOverall() {
        StatUseOrderOverallVO overallVO = new StatUseOrderOverallVO();
        List<UseOrder> orderList = useOrderMapper.listByYear(null);
        if (!CollectionUtils.isEmpty(orderList)) {
            overallVO.setOrderQuantity(orderList.size());
            List<Long> orderIds = orderList.stream().map(UseOrder::getId).collect(Collectors.toList());
            List<ProductObjVO> objVOList = useOrderMapper.listProductObjByUseOrderIds(orderIds);
            if (!CollectionUtils.isEmpty(objVOList)) {
                // 领用总数量
                overallVO.setUseQuantity(objVOList.size());
                // 领用总成本
                Map<Long, List<ProductObjVO>> craftObjMap = objVOList.stream().collect(Collectors.groupingBy(ProductObjVO::getCraftId));
                List<ProductCraftVO> craftVOList = productCraftMapper.selectByIds(new ArrayList<>(craftObjMap.keySet()));
                if (!CollectionUtils.isEmpty(craftVOList)) {
                    BigDecimal useCost = new BigDecimal("0");
                    for (Long craftId : craftObjMap.keySet()) {
                        ProductCraftVO craftVO = craftVOList.stream().filter(e -> e.getId().equals(craftId)).findFirst().get();
                        useCost = useCost.add(craftVO.getGuideCost().multiply(BigDecimal.valueOf(craftObjMap.get(craftId).size())));
                    }
                    overallVO.setUseCost(useCost);
                }
            }
        }
        return ApiResponseBody.defaultSuccess(overallVO);
    }

    /**
     * 逐月统计设备成本、设备数量
     *
     * @param searchYear
     * @return
     */
    public ApiResponseBody<List<StatUseOrderVO>> statUseObj(Year searchYear) {
        List<StatUseOrderVO> statList = new ArrayList<>();
        IntStream.rangeClosed(1, 12).forEach(month -> statList.add(new StatUseOrderVO(month)));
        List<UseOrder> orderList = useOrderMapper.listByYear(searchYear);
        if (!CollectionUtils.isEmpty(orderList)) {
            List<ProductObjVO> objVOList = useOrderMapper.listProductObjByUseOrderIds(orderList.stream().map(UseOrder::getId).collect(Collectors.toList()));
            if (!CollectionUtils.isEmpty(objVOList)) {
                // 查询工艺成本
                List<ProductCraftVO> craftVOList = productCraftMapper.selectByIds(objVOList.stream().map(ProductObjVO::getCraftId).distinct().collect(Collectors.toList()));
                // 按月统计
                Map<Integer, List<ProductObjVO>> monthObjMap = objVOList.stream().collect(Collectors.groupingBy(e -> {
                    Calendar calendar = Calendar.getInstance();
                    calendar.setTime(e.getUseDate());
                    return calendar.get(Calendar.MONTH);
                }));
                for (Integer monthIndex : monthObjMap.keySet()) {
                    StatUseOrderVO stat = statList.get(monthIndex);
                    stat.setUseQuantity(monthObjMap.get(monthIndex).size());
                    BigDecimal useCost = new BigDecimal("0");
                    Map<Long, List<ProductObjVO>> craftObjMap = monthObjMap.get(monthIndex).stream().collect(Collectors.groupingBy(ProductObjVO::getCraftId));
                    for (Long craftId : craftObjMap.keySet()) {
                        ProductCraftVO craftVO = craftVOList.stream().filter(e -> e.getId().equals(craftId)).findFirst().get();
                        useCost = useCost.add(craftVO.getGuideCost().multiply(BigDecimal.valueOf(craftObjMap.get(craftId).size())));
                    }
                    stat.setUseCost(useCost);
                }
            }
        }
        return ApiResponseBody.defaultSuccess(statList);
    }

    private void checkOrder(UseOrder order) {
        if (Strings.isNullOrEmpty(order.getOrderNo())) {
            throw new BizDmException("订单编号为空");
        }
        if (Strings.isNullOrEmpty(order.getOrderName())) {
            throw new BizDmException("订单名称为空");
        }
        if (useOrderMapper.isExistedOrderNo(order)) {
            throw new BizDmException(String.format("订单编号已存在: %S", order.getOrderNo()));
        }
    }

}
