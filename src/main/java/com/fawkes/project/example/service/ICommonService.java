package com.fawkes.project.example.service;


import com.fawkes.core.base.api.ApiResponseBody;
import com.fawkes.project.example.common.param.FormCommitParam;
import com.fawkes.project.example.common.param.FormQueryParam;
import com.fawkes.stream.msg.send.bpm.BpmFormProcessStateMsg;

/**
 * 表单通用服务
 *
 * <AUTHOR>
 * @date 2019-12-2
 */
public interface ICommonService {
    /**
     * 通用表单新增接口
     *
     * <AUTHOR>
     * @date 2019-12-2
     */
    ApiResponseBody saveForm(FormCommitParam formCommitParam) throws Exception;

    /**
     * 通用表单查询接口
     *
     * <AUTHOR>
     * @date 2019-12-2
     */
    ApiResponseBody queryForm(FormQueryParam formQueryParam) throws Exception;

    /**
     * 通用表单更新流程状态字段
     *
     * <AUTHOR>
     * @date 2019-12-2
     */
    void updateFormProcessState(BpmFormProcessStateMsg processState, Object clazz);

}
