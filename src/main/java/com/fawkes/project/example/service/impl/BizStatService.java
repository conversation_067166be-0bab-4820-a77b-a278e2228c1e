package com.fawkes.project.example.service.impl;

import com.fawkes.core.base.api.ApiResponseBody;
import com.fawkes.project.example.common.constants.CommonConstants;
import com.fawkes.project.example.common.enums.ClientTypeEnum;
import com.fawkes.project.example.common.mapper.*;
import com.fawkes.project.example.common.model.ProductOrder;
import com.fawkes.project.example.common.model.ProductOrderLoss;
import com.fawkes.project.example.common.model.UseOrder;
import com.fawkes.project.example.common.utils.OrderTool;
import com.fawkes.project.example.domain.param.SaleOrderPageParam;
import com.fawkes.project.example.domain.vo.*;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.web.bind.annotation.RequestParam;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.time.Year;
import java.util.*;
import java.util.concurrent.atomic.AtomicReference;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 经营统计
 *
 * <AUTHOR>
 */
@Service
@Slf4j
public class BizStatService {
    @Resource
    SaleOrderServiceImpl saleOrderService;
    @Resource
    SaleOrderMapper saleOrderMapper;
    @Resource
    AccessoryMapper accessoryMapper;
    @Resource
    AccessoryOrderMapper accessoryOrderMapper;
    @Resource
    AccessoryOrderGoodsMapper accessoryOrderGoodsMapper;
    @Resource
    ProductOrderMapper productOrderMapper;
    @Resource
    UseOrderMapper useOrderMapper;


    /****************************************** 收款利润统计 **************************************************/
    public ApiResponseBody<BizStatProfitVO> statProfit(Year searchYear) {
        BizStatProfitVO statVO = new BizStatProfitVO();

        SaleOrderPageParam param = new SaleOrderPageParam();
        param.setPageNum(1);
        param.setPageSize(Integer.MAX_VALUE);
        param.setSearchYear(searchYear);
        List<SaleOrderVO> orderVOList = saleOrderService.page(param).getData().getList();
        if (!CollectionUtils.isEmpty(orderVOList)) {
            for (SaleOrderVO orderVO : orderVOList) {
                statVO.setSaleAmount(OrderTool.bdAdd(statVO.getSaleAmount(), orderVO.getSalePrice()));
                statVO.setPaymentAmount(OrderTool.bdAdd(statVO.getPaymentAmount(), orderVO.getPaymentAmount()));
                statVO.setProfit(OrderTool.bdAdd(statVO.getProfit(), orderVO.getActualProfit()));
                statVO.setEarnedProfit(OrderTool.bdAdd(statVO.getEarnedProfit(), orderVO.getEarnedProfit()));
            }
            statVO.setFinishedOrder(orderVOList.stream().filter(e -> CommonConstants.CONTRACT_FINISHED.equals(e.getStatus())).count());
            statVO.setUnfinishedOrder(orderVOList.stream().filter(e -> CommonConstants.CONTRACT_PROGRESS.equals(e.getStatus())).count());
            Map<Long, List<SaleOrderVO>> clientOrderMap = orderVOList.stream().collect(Collectors.groupingBy(SaleOrderVO::getClientId));
            AtomicReference<Long> finishClientQuantity = new AtomicReference<>(0L);
            clientOrderMap.forEach((clientId, list) -> {
                if (list.stream().allMatch(e -> OrderTool.bdIsLessThan(e.getSalePrice(), e.getPaymentAmount()))) {
                    finishClientQuantity.getAndSet(finishClientQuantity.get() + 1);
                }
            });
            statVO.setFinishedClient(finishClientQuantity.get());
            statVO.setUnfinishedClient(clientOrderMap.keySet().size() - finishClientQuantity.get());
        }
        return ApiResponseBody.defaultSuccess(statVO);
    }

    /**
     * 逐月统计收款量
     *
     * @param searchYear
     * @return
     */
    public ApiResponseBody statClientPaymentByYear(Year searchYear) {
        List<LabelValueVO> options = statProfitByClient(searchYear, SaleOrderVO::getPaymentAmount);
        return ApiResponseBody.defaultSuccess(options);
    }

    /**
     * 逐月统计销售额
     *
     * @param searchYear
     * @return
     */
    public ApiResponseBody statClientSaleByYear(Year searchYear) {
        List<LabelValueVO> options = statProfitByClient(searchYear, SaleOrderVO::getSalePrice);
        return ApiResponseBody.defaultSuccess(options);
    }


    /****************************************** 付款统计 **************************************************/
    public ApiResponseBody<BizStatPurchaseVO> statPurchase(Year searchYear) {
        BizStatPurchaseVO statVO = accessoryOrderMapper.statPurchase(searchYear);
        statVO.setSupplierQuantity(accessoryOrderMapper.statPurchaseAccessorySupplier(searchYear));
        return ApiResponseBody.defaultSuccess(statVO);
    }

    public ApiResponseBody<List<BizStatPurchaseMonthVO>> statPurchaseMonth(Year searchYear) {
        List<BizStatPurchaseMonthVO> monthVOList = accessoryOrderMapper.statPurchaseMonth(searchYear);
        monthVOList.forEach(e -> e.setMonth(OrderTool.month2str(e.getMonth())));
        return ApiResponseBody.defaultSuccess(monthVOList);
    }


    /****************************************** 产品库存管理 **************************************************/
    public ApiResponseBody<List<StatProductInVO>> listProductOrderInByProductId(String productId) {
        List<StatProductInVO> productInVOList = new ArrayList<>();
        List<ProductObjVO> objVOList = productOrderMapper.selectObjsByProductId(Long.parseLong(productId));
        if (!CollectionUtils.isEmpty(objVOList)) {
            Map<Long, List<ProductObjVO>> orderObjsMap = objVOList.stream().collect(Collectors.groupingBy(ProductObjVO::getOrderId));
            List<ProductOrder> orderList = productOrderMapper.selectOrderByIds(objVOList.stream().map(ProductObjVO::getOrderId).collect(Collectors.toList()));
            orderList.forEach(order -> {
                StatProductInVO productInVO = new StatProductInVO(order);
                productInVO.setQualifiedNum(orderObjsMap.get(order.getId()).stream().filter(obj -> obj.getStatus() == CommonConstants.OBJECT_STATUS_QUALIFIED).count());
                productInVO.setDefectiveNum(orderObjsMap.get(order.getId()).stream().filter(obj -> obj.getStatus() == CommonConstants.OBJECT_STATUS_DEFECTIVE).count());
                productInVO.setWasteNum(orderObjsMap.get(order.getId()).stream().filter(obj -> obj.getStatus() == CommonConstants.OBJECT_STATUS_WASTE).count());
                productInVOList.add(productInVO);
            });
        }
        return ApiResponseBody.defaultSuccess(productInVOList);
    }

    public ApiResponseBody<List<StatProductOutVO>> listProductOrderOutByProductId(String productId) {
        List<StatProductOutVO> productOutVOList = new ArrayList<>();
        List<ProductObjVO> objVOList = productOrderMapper.selectObjsByProductId(Long.parseLong(productId));
        if (!CollectionUtils.isEmpty(objVOList)) {
            List<ProductObjVO> objInUseOrderList = objVOList.stream().filter(obj -> !Objects.isNull(obj.getUseOrderId())).collect(Collectors.toList());
            if (!CollectionUtils.isEmpty(objInUseOrderList)) {
                Map<Long, List<ProductObjVO>> outObjMap = objInUseOrderList.stream().collect(Collectors.groupingBy(ProductObjVO::getUseOrderId));
                List<UseOrder> useOrderList = useOrderMapper.selectByIds(objInUseOrderList.stream().map(ProductObjVO::getUseOrderId).distinct().collect(Collectors.toList()));
                useOrderList.forEach(useOrder -> {
                    StatProductOutVO outVO = new StatProductOutVO(useOrder);
                    outVO.setPlanNum((long) outObjMap.get(useOrder.getId()).size());
                    productOutVOList.add(outVO);
                });
            }

            List<ProductObjVO> objInSaleOrderList = objVOList.stream().filter(obj -> !Objects.isNull(obj.getSaleOrderId())).collect(Collectors.toList());
            if (!CollectionUtils.isEmpty(objInSaleOrderList)) {
                Map<Long, List<ProductObjVO>> saleObjMap = objInSaleOrderList.stream().collect(Collectors.groupingBy(ProductObjVO::getSaleOrderId));
                List<SaleOrderVO> saleOrderList = saleOrderMapper.selectByIds(objInSaleOrderList.stream().map(ProductObjVO::getSaleOrderId).distinct().collect(Collectors.toList()));
                saleOrderList.forEach(saleOrder -> {
                    StatProductOutVO outVO = new StatProductOutVO(saleOrder);
                    outVO.setRejectNum(saleObjMap.get(saleOrder.getId()).stream().filter(obj -> CommonConstants.RETURN_TYPE_REJECT.equals(obj.getReturnType())).count());
                    outVO.setExchangeNum(saleObjMap.get(saleOrder.getId()).stream().filter(obj -> CommonConstants.RETURN_TYPE_EXCHANGE.equals(obj.getReturnType())).count());
                    productOutVOList.add(outVO);
                });
            }
        }
        productOutVOList.sort((o1, o2) -> o2.getOrderTime().compareTo(o1.getOrderTime()));
        return ApiResponseBody.defaultSuccess(productOutVOList);
    }


    /****************************************** 配件库存管理 **************************************************/
    /**
     * 统计配件入库情况
     *
     * @param accessoryId
     * @return
     */
    public ApiResponseBody<List<AccessoryOrderGoodsVO>> listAccessoryInByAccessoryId(@RequestParam("accessoryId") String accessoryId) {
        return ApiResponseBody.defaultSuccess(accessoryOrderGoodsMapper.selectByAccessoryId(Long.parseLong(accessoryId)));
    }

    /**
     * 统计配件出库情况
     *
     * @param accessoryId
     * @return
     */
    public ApiResponseBody<List<StatAccessoryOutVO>> listAccessoryOutByAccessoryId(@RequestParam("accessoryId") String accessoryId) {
        List<StatAccessoryOutVO> outVOList = accessoryMapper.statAccessoryOutUseByAccessoryId(Long.parseLong(accessoryId));
        if (!CollectionUtils.isEmpty(outVOList)) {
            List<ProductOrderLoss> lossList = productOrderMapper.selectProductOrderLossByOrderIds(outVOList.stream().map(StatAccessoryOutVO::getOrderId).collect(Collectors.toList()));
            outVOList.forEach(outVO -> {
                Optional<ProductOrderLoss> loss = lossList.stream().filter(e -> e.getOrderId().equals(outVO.getOrderId())).findFirst();
                if (loss.isPresent()) {
                    outVO.setLossAccessoryQuantity(loss.get().getLossQuantity());
                } else {
                    outVO.setLossAccessoryQuantity(0);
                }
            });
        }
        return ApiResponseBody.defaultSuccess(outVOList);
    }

    /**
     * 逐月统计，按客户类型分类
     *
     * @param searchYear
     * @param mapper
     * @return
     */
    private List<LabelValueVO> statProfitByClient(Year searchYear, Function<? super SaleOrderVO, BigDecimal> mapper) {
        List<SaleOrderVO> orderVOList = saleOrderMapper.listByYear(searchYear);
        Map<Integer, List<SaleOrderVO>> monthOrderMap = CollectionUtils.isEmpty(orderVOList) ? new HashMap<>(0) : orderVOList.stream().collect(Collectors.groupingBy(e -> e.getSignDate().getMonthValue()));
        List<LabelValueVO> options = new ArrayList<>();
        Map<String, BigDecimal> sumMap = new HashMap<>(ClientTypeEnum.flags().size());
        ClientTypeEnum.flags().forEach(flag -> sumMap.put(flag, new BigDecimal("0")));
        for (int monthIndex = 1; monthIndex <= CommonConstants.MONTH_COUNT; monthIndex++) {
            List<LabelValueVO> clients = new ArrayList<>();
            List<SaleOrderVO> monthOrderList = monthOrderMap.get(monthIndex);
            if (CollectionUtils.isEmpty(monthOrderList)) {
                ClientTypeEnum.flags().forEach(flag -> clients.add(new LabelValueVO(flag, 0)));
            } else {
                Map<String, List<SaleOrderVO>> typeOrderMap = monthOrderList.stream().collect(Collectors.groupingBy(SaleOrderVO::getClientType));
                ClientTypeEnum.flags().forEach(flag -> {
                    BigDecimal value = CollectionUtils.isEmpty(typeOrderMap.get(flag)) ? new BigDecimal("0") : typeOrderMap.get(flag).stream().map(mapper).reduce(BigDecimal::add).get();
                    clients.add(new LabelValueVO(flag, value));
                    sumMap.put(flag, OrderTool.bdAdd(sumMap.get(flag), value));
                });
            }
            options.add(new LabelValueVO(OrderTool.month2str(monthIndex), clients));
        }
        LabelValueVO sumVO = new LabelValueVO("总计", sumMap);
        options.add(sumVO);
        return options;
    }
}
