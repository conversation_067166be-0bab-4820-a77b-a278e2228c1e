package com.fawkes.project.example.service.impl;

import com.fawkes.core.base.api.ApiResponseBody;
import com.fawkes.core.enums.DeleteFlagEnum;
import com.fawkes.core.utils.EntityTool;
import com.fawkes.project.example.common.exception.BizDmException;
import com.fawkes.project.example.common.mapper.*;
import com.fawkes.project.example.common.model.AccessoryOrder;
import com.fawkes.project.example.common.model.AccessoryOrderGoods;
import com.fawkes.project.example.common.model.AccessoryOrderPayLog;
import com.fawkes.project.example.common.model.ProductOrderLoss;
import com.fawkes.project.example.common.utils.MyEntityTool;
import com.fawkes.project.example.common.utils.OrderTool;
import com.fawkes.project.example.domain.dto.AccessoryPriceDTO;
import com.fawkes.project.example.domain.param.AccessoryOrderQueryParam;
import com.fawkes.project.example.domain.param.AccessoryOutOrderQueryParam;
import com.fawkes.project.example.domain.vo.AccessoryOrderGoodsVO;
import com.fawkes.project.example.domain.vo.AccessoryOrderStatPaidVO;
import com.fawkes.project.example.domain.vo.AccessoryOrderVO;
import com.fawkes.project.example.domain.vo.AccessoryOutVO;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.google.common.base.Strings;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Service
@Slf4j
public class AccessoryOrderService {
    @Resource
    AccessoryOrderMapper accessoryOrderMapper;
    @Resource
    AccessoryOrderPayLogMapper accessoryOrderPayLogMapper;
    @Resource
    AccessoryOrderGoodsMapper accessoryOrderGoodsMapper;
    @Resource
    AccessoryMapper accessoryMapper;
    @Resource
    ProductOrderMapper productOrderMapper;

    public ApiResponseBody getLatestOrderNo(LocalDate orderDate) {
        return ApiResponseBody.defaultSuccess(OrderTool.genAccessoryOrderNoFromPrev(orderDate, accessoryOrderMapper.getMaxOrderNoAtTheDate(orderDate)));
    }

    @Transactional(rollbackFor = Exception.class)
    public ApiResponseBody add(AccessoryOrderVO orderVO) {
        checkAccessoryOrder(orderVO);
        if (Strings.isNullOrEmpty(orderVO.getOrderNo())) {
            orderVO.setOrderNo(OrderTool.genAccessoryOrderNoFromPrev(orderVO.getOrderDate(), accessoryOrderMapper.getMaxOrderNoAtTheDate(orderVO.getOrderDate())));
        }
        /* 订单信息 */
        EntityTool.insertEntity(orderVO);
        accessoryOrderMapper.insert(orderVO);
        /* 支付记录 */
        if (!Objects.isNull(orderVO.getCurPayAmount()) && !OrderTool.bdIsZERO(orderVO.getCurPayAmount())) {
            AccessoryOrderPayLog payLog = new AccessoryOrderPayLog(orderVO.getId(), orderVO.getCurPayAmount());
            EntityTool.insertEntity(payLog);
            accessoryOrderPayLogMapper.insert(payLog);
        }
        /* 订单明细 */
        BigDecimal totalPurchasePrice = BigDecimal.ZERO;
        if (!CollectionUtils.isEmpty(orderVO.getGoodsList())) {
            for (AccessoryOrderGoodsVO e : orderVO.getGoodsList()) {
                e.setOrderId(orderVO.getId());
                totalPurchasePrice = OrderTool.bdAdd(totalPurchasePrice, e.getPurchasePrice());
            }
            EntityTool.insertEntity(orderVO.getGoodsList());
            accessoryOrderGoodsMapper.insertBatch(orderVO.getGoodsList());
            List<Long> accessoryIds = orderVO.getGoodsList().stream().map(AccessoryOrderGoods::getAccessoryId).collect(Collectors.toList());
            updateAccessoryCurPriceByIds(accessoryIds);
        }
        /* 累计支付金额 不超过 订单金额*/
        if (!Objects.isNull(orderVO.getCurPayAmount()) && orderVO.getCurPayAmount().compareTo(totalPurchasePrice) > 0) {
            throw new BizDmException("支付金额不能超过订单金额");
        }
        return ApiResponseBody.defaultSuccess();
    }

    public ApiResponseBody<AccessoryOrderVO> query(String id) {
        AccessoryOrderVO accessoryOrderVO = accessoryOrderMapper.selectById(Long.parseLong(id));
        if (!Objects.isNull(accessoryOrderVO)) {
            List<AccessoryOrderGoodsVO> goodsVOList = accessoryOrderGoodsMapper.selectAccessoryOrderGoodsByOrderId(accessoryOrderVO.getId());
            if (!CollectionUtils.isEmpty(goodsVOList)) {
                goodsVOList.forEach(e -> e.setArrivalDate(e.getArrivalQuantity() > 0 ? e.getUpdateDate() : null));
            }
            accessoryOrderVO.setGoodsList(goodsVOList);
        }
        return ApiResponseBody.defaultSuccess(accessoryOrderVO);
    }

    @Transactional(rollbackFor = Exception.class)
    public ApiResponseBody delete(String id) {
        accessoryOrderMapper.deleteById(Long.parseLong(id), MyEntityTool.getUpdateInfo());
        accessoryOrderGoodsMapper.deleteByOrderId(Long.parseLong(id), MyEntityTool.getUpdateInfo());
        updateAccessoryCurPriceByAll();
        return ApiResponseBody.defaultSuccess();
    }

    @Transactional(rollbackFor = Exception.class)
    public ApiResponseBody update(AccessoryOrderVO orderVO) {
        checkAccessoryOrder(orderVO);
        /* 订单信息 */
        EntityTool.updateEntity(orderVO);
        accessoryOrderMapper.updateByPrimaryKey(orderVO);
        /* 支付记录 */
        if (!Objects.isNull(orderVO.getCurPayAmount()) && !OrderTool.bdIsZERO(orderVO.getCurPayAmount())) {
            AccessoryOrderPayLog payLog = new AccessoryOrderPayLog(orderVO.getId(), orderVO.getCurPayAmount());
            EntityTool.insertEntity(payLog);
            accessoryOrderPayLogMapper.insert(payLog);
        }
        /* 订单明细 */
        BigDecimal totalPurchasePrice = BigDecimal.ZERO;
        List<AccessoryOrderGoods> toAddGoodsList = new ArrayList<>();
        List<AccessoryOrderGoods> oldGoodsList = accessoryOrderGoodsMapper.selectByOrderId(orderVO.getId());
        if (CollectionUtils.isEmpty(oldGoodsList)) {
            oldGoodsList = new ArrayList<>();
        }
        oldGoodsList.forEach(goods -> goods.setDeleteFlag(DeleteFlagEnum.DATA_WARN.getFlag()));
        if (!CollectionUtils.isEmpty(orderVO.getGoodsList())) {
            for (AccessoryOrderGoodsVO goods : orderVO.getGoodsList()) {
                totalPurchasePrice = OrderTool.bdAdd(totalPurchasePrice, goods.getPurchasePrice());
                if (Objects.isNull(goods.getOrderId())) {
                    EntityTool.insertEntity(goods);
                    goods.setOrderId(orderVO.getId());
                    toAddGoodsList.add(goods);
                } else {
                    Optional<AccessoryOrderGoods> optional = oldGoodsList.stream().filter(e -> e.getId().equals(goods.getId())).findFirst();
                    if (optional.isPresent()) {
                        AccessoryOrderGoods updateOrderGoods = optional.get();
                        updateOrderGoods.setDeleteFlag(DeleteFlagEnum.DATA_OK.getFlag());
                        updateOrderGoods.setAccessoryId(goods.getAccessoryId());
                        updateOrderGoods.setPurchaseQuantity(goods.getPurchaseQuantity());
                        updateOrderGoods.setPurchasePrice(goods.getPurchasePrice());
                        updateOrderGoods.setArrivalQuantity(goods.getArrivalQuantity());
                    }
                }
            }
        }
        if (!CollectionUtils.isEmpty(toAddGoodsList)) {
            EntityTool.insertEntity(toAddGoodsList);
            accessoryOrderGoodsMapper.insertBatch(toAddGoodsList);
        }
        if (!CollectionUtils.isEmpty(oldGoodsList)) {
            EntityTool.updateEntity(oldGoodsList);
            accessoryOrderGoodsMapper.updateBatch(oldGoodsList);
        }
        updateAccessoryCurPriceByAll();

        /* 累计支付金额 不超过 订单金额*/
        AccessoryOrderVO record = accessoryOrderMapper.selectById(orderVO.getId());
        if (record.getOrderTotalPaid().compareTo(totalPurchasePrice) > 0) {
            throw new BizDmException("支付金额不能超过订单金额");
        }
        return ApiResponseBody.defaultSuccess();
    }

    public ApiResponseBody<PageInfo<AccessoryOrderVO>> page(AccessoryOrderQueryParam param) {
        PageHelper.startPage(param.getPageNum(), param.getPageSize());
        List<AccessoryOrderVO> accessoryOrderVOList = accessoryOrderMapper.selectAccessoryOrderList(param);
        PageInfo<AccessoryOrderVO> pageInfo = new PageInfo<>(accessoryOrderVOList);
        return ApiResponseBody.defaultSuccess(pageInfo);
    }

    public ApiResponseBody outPage(AccessoryOutOrderQueryParam param) {
        PageHelper.startPage(param.getPageNum(), param.getPageSize());
        List<AccessoryOutVO> outVOList = accessoryOrderMapper.selectOutList(param);
        if (!CollectionUtils.isEmpty(outVOList)) {
            Map<Long, List<ProductOrderLoss>> orderLossMap = productOrderMapper
                    .selectProductOrderLossByOrderIds(outVOList.stream().map(AccessoryOutVO::getId).distinct().collect(Collectors.toList()))
                    .stream().collect(Collectors.groupingBy(ProductOrderLoss::getOrderId));
            outVOList.forEach(e -> {
                int lossNum = Objects.isNull(orderLossMap.get(e.getId())) ? 0 : orderLossMap.get(e.getId()).stream().map(ProductOrderLoss::getLossQuantity).reduce(0, Integer::sum);
                e.setAccessoryQuantityWithLoss(e.getAccessoryQuantity() + lossNum);
            });
        }

        PageInfo<AccessoryOutVO> pageInfo = new PageInfo<>(outVOList);
        return ApiResponseBody.defaultSuccess(pageInfo);
    }

    public ApiResponseBody statPaid() {
        AccessoryOrderStatPaidVO accessoryOrderStatPaidVO = accessoryOrderMapper.selectStatPaid();
        return ApiResponseBody.defaultSuccess(accessoryOrderStatPaidVO);
    }

    /**
     * 校验配件入库订单
     *
     * @param order
     */
    private void checkAccessoryOrder(AccessoryOrder order) {
        if (Strings.isNullOrEmpty(order.getOrderNo())) {
            throw new BizDmException("订单编号为空");
        }
        if (Strings.isNullOrEmpty(order.getOrderName())) {
            throw new BizDmException("订单名称为空");
        }
        if (Objects.isNull(order.getOrderDate())) {
            throw new BizDmException("订单日期为空");
        }
        if (accessoryOrderMapper.isExistedOrderNo(order)) {
            throw new BizDmException("订单编号已存在");
        }
    }

    /**
     * 全量更新配件单价
     */
    private void updateAccessoryCurPriceByAll() {
        updateAccessoryCurPriceByIds(null);
    }

    /**
     * 更新配件单价
     */
    private void updateAccessoryCurPriceByIds(List<Long> accessoryIds) {
        List<AccessoryPriceDTO> accessoryPriceDTOList = accessoryOrderGoodsMapper.selectAccessoryPrice(accessoryIds);
        if (!CollectionUtils.isEmpty(accessoryPriceDTOList)) {
            accessoryPriceDTOList.forEach(e -> e.setCurPrice(OrderTool.bdDivide(e.getTotalPrice(), new BigDecimal(e.getTotalQuantity()))));
            accessoryMapper.batchUpdateCurPrice(accessoryPriceDTOList);
        }
    }
}
