package com.fawkes.project.example.service.impl;

import com.fawkes.core.base.api.ApiResponseBody;
import com.fawkes.project.example.common.mapper.BizLogMapper;
import com.fawkes.project.example.common.model.BizLog;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Collections;

/**
 * <AUTHOR>
 */
@Service
public class BizLogService {
    @Resource
    BizLogMapper bizLogMapper;

    public ApiResponseBody<PageInfo<BizLog>> page(Integer pageNum, Integer pageSize) {
        PageHelper.startPage(pageNum, pageSize);
        return ApiResponseBody.defaultSuccess(new PageInfo<>(bizLogMapper.list()));
    }

    public void save(BizLog bizLog) {
        bizLogMapper.insertBatch(Collections.singletonList(bizLog));
    }

    public void log(String module, String content) {
        save(new BizLog(module, content));
    }

}
