package com.fawkes.project.example.service.impl;

import com.fawkes.core.base.api.ApiResponseBody;
import com.fawkes.core.base.model.CurrentUser;
import com.fawkes.core.utils.EntityTool;
import com.fawkes.core.utils.http.HttpHeaderTool;
import com.fawkes.project.example.client.ISysUserClient;
import com.fawkes.project.example.common.constants.CommonConstants;
import com.fawkes.project.example.common.mapper.BizMsgMapper;
import com.fawkes.project.example.common.mapper.ProductOrderMapper;
import com.fawkes.project.example.common.model.BizMsg;
import com.fawkes.project.example.common.model.ProductObject;
import com.fawkes.project.example.common.model.ProductOrder;
import com.fawkes.project.example.domain.dto.SysUserDTO;
import com.fawkes.project.example.domain.param.CheckQo;
import com.fawkes.stream.msg.send.socket.SocketMsg;
import com.fawkes.stream.msg.send.socket.SocketTool;
import com.fawkes.stream.msg.send.socket.SocketTypeEnum;
import lombok.extern.slf4j.Slf4j;
import org.springframework.amqp.core.AmqpTemplate;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 消息通知
 *
 * <AUTHOR>
 */
@Service
@Slf4j
public class BizMsgService {
    @Resource
    ProductOrderMapper productOrderMapper;
    @Resource
    BizMsgMapper bizMsgMapper;
    @Resource
    ISysUserClient sysUserClient;
    @Resource
    AmqpTemplate fawkesAmqpTemplate;

    /**
     * 查询消息通知
     *
     * @return
     */
    public ApiResponseBody<List<BizMsg>> list() {
        CurrentUser currentUser = HttpHeaderTool.getCurrentUser();
        return ApiResponseBody.defaultSuccess(bizMsgMapper.selectByToUserId(Long.parseLong(currentUser.getId())));
    }

    public ApiResponseBody readAll() {
        CurrentUser currentUser = HttpHeaderTool.getCurrentUser();
        bizMsgMapper.updateStatusByToUserId(Long.parseLong(currentUser.getId()), CommonConstants.BIZ_MSG_STATUS_READ);
        return ApiResponseBody.defaultSuccess();
    }

    /**
     * 消息通知: 将检验信息推送给生产责任人
     *
     * @param qo
     * @deprecated
     */
//    @Async("taskExecutor")
    @Transactional(rollbackFor = Exception.class)
    public void asyncSendProductCheckMsg(CheckQo qo) {
        List<BizMsg> msgList = new ArrayList<>();
        // 查询产品对象编号
        List<ProductObject> objectList = bizMsgMapper.getObjsByIds(Arrays.asList(qo.getCheckIds()));
        Map<Long, List<ProductObject>> orderObjMap = objectList.stream().collect(Collectors.groupingBy(ProductObject::getOrderId));
        // 查询订单编号
        List<ProductOrder> orderList = productOrderMapper.selectOrderByIds(objectList.stream().map(ProductObject::getOrderId).distinct().collect(Collectors.toList()));
        // 检验状态
        String checkStatusLabel = getProjectObjStatusLabel(qo.getStatus());
        for (Long orderId : orderObjMap.keySet()) {
            Optional<ProductOrder> orderOptional = orderList.stream().filter(o -> o.getId().equals(orderId)).findFirst();
            orderOptional.ifPresent(order -> orderObjMap.get(orderId).forEach(obj -> {
                BizMsg msg = new BizMsg(order.getProduceUserId(), order.getOrderNo(), String.format("%s被标记为%s", obj.getObjectNo(), checkStatusLabel));
                msgList.add(msg);
            }));
        }
        if (!CollectionUtils.isEmpty(msgList)) {
            EntityTool.insertEntity(msgList);
            bizMsgMapper.insertBatch(msgList);
            List<Long> toUserIds = orderList.stream().map(ProductOrder::getProduceUserId).filter(Objects::nonNull).distinct().collect(Collectors.toList());
            notifyRefreshMsg(toUserIds);
        }
    }

    /**
     * 将生产信息推送给检验责任人
     *
     * @param order
     */
    @Transactional(rollbackFor = Exception.class)

    public void sendProductCreateMsg(ProductOrder order, List<ProductObject> objList) {
        if (Objects.isNull(order.getCheckUserId())) {
            return;
        }
        BizMsg msg = new BizMsg(order.getCheckUserId(), order.getOrderNo(), "生产订单创建成功，请检验产品：" + objList.stream().map(ProductObject::getObjectNo).collect(Collectors.joining(",")));
        EntityTool.insertEntity(msg);
        bizMsgMapper.insertBatch(Collections.singletonList(msg));
        notifyRefreshMsg(Collections.singletonList(order.getCheckUserId()));
    }

    /**
     * 销售订单变动通知责任人
     *
     * @param operation
     * @param orderNo
     * @param userId
     */
    @Transactional(rollbackFor = Exception.class)
    public void sendSaleOrderMsg(String operation, String orderNo, Long userId) {
        log.info("销售订单变动通知责任人：%s, %s, %s", operation, orderNo, userId);
        BizMsg msg = new BizMsg(userId, operation, orderNo);
        EntityTool.insertEntity(msg);
        bizMsgMapper.insertBatch(Collections.singletonList(msg));
        notifyRefreshMsg(Collections.singletonList(userId));
    }

    /**
     * @deprecated
     */
    public void notifyAllRefreshMsg() {
        SocketMsg socketMsg = new SocketMsg();
        socketMsg.setSocketType(SocketTypeEnum.TOPIC);
        socketMsg.setDestination("/topic/message");
        socketMsg.setToClient("fawkes");
        socketMsg.setMsg("refresh msg");
        SocketTool.ceneralSend(socketMsg, fawkesAmqpTemplate);
    }

    /**
     * 通知前端刷新消息
     *
     * @param userIds
     */
    public void notifyRefreshMsg(List<Long> userIds) {
        if (CollectionUtils.isEmpty(userIds)) {
            return;
        }
        List<String> userNameList = sysUserClient.getUserList().getData().stream().filter(u -> userIds.contains(u.getId())).map(SysUserDTO::getUserName).collect(Collectors.toList());
        if (!CollectionUtils.isEmpty(userNameList)) {
            SocketMsg socketMsg = new SocketMsg();
            socketMsg.setSocketType(SocketTypeEnum.USER);
            socketMsg.setDestination("/topic/message");
            socketMsg.setToClient("fawkes");
            socketMsg.setToUsers(userNameList);
            socketMsg.setMsg("refresh msg");
            SocketTool.ceneralSend(socketMsg, fawkesAmqpTemplate);
            log.info("[通知]: " + String.join(",", userNameList));
        }
    }

    private String getProjectObjStatusLabel(Integer status) {
        switch (status) {
            case CommonConstants.OBJECT_STATUS_QUALIFIED:
                return "合格";
            case CommonConstants.OBJECT_STATUS_DEFECTIVE:
                return "次品";
            case CommonConstants.OBJECT_STATUS_WASTE:
                return "废品";
            default:
                return "--";
        }
    }
}
