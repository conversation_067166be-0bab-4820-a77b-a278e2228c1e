package com.fawkes.project.example.service.impl;

import com.fawkes.core.base.api.ApiResponseBody;
import com.fawkes.core.utils.EntityTool;
import com.fawkes.project.example.common.constants.CommonConstants;
import com.fawkes.project.example.common.exception.BizDmException;
import com.fawkes.project.example.common.mapper.AccessoryMapper;
import com.fawkes.project.example.common.mapper.ProductOrderMapper;
import com.fawkes.project.example.common.mapper.SaleOrderGoodsMapper;
import com.fawkes.project.example.common.mapper.SaleOrderMapper;
import com.fawkes.project.example.common.model.*;
import com.fawkes.project.example.common.utils.*;
import com.fawkes.project.example.domain.dto.ProductSaleQuantityDTO;
import com.fawkes.project.example.domain.param.*;
import com.fawkes.project.example.domain.vo.*;
import com.fawkes.project.example.runner.Cache;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.google.common.base.Strings;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.util.ObjectUtils;

import javax.annotation.Resource;
import javax.servlet.ServletOutputStream;
import javax.servlet.http.HttpServletResponse;
import java.io.File;
import java.io.FileInputStream;
import java.io.IOException;
import java.util.*;
import java.util.concurrent.atomic.AtomicLong;
import java.util.stream.Collectors;

@Slf4j
@Service
public class ProductOrderService {

    @Resource
    AccessoryMapper accessoryMapper;
    @Resource
    BizLogService bizLogService;
    @Resource
    ProductOrderMapper productOrderMapper;
    @Resource
    SaleOrderMapper saleOrderMapper;
    @Resource
    SaleOrderGoodsMapper saleOrderGoodsMapper;
    @Resource
    private BizMsgService bizMsgService;

    /**
     * 产品订单列表
     */
    public List<ProductOrder> list(ProductOrderQo qo) {
        List<ProductOrder> orders = productOrderMapper.list(qo);

        /* 获取该订单下所有产品 */
        Map<Long, List<ProductObject>> orderObjs = null;
        if (!ObjectUtils.isEmpty(orders)) {
            List<ProductObject> objects = productOrderMapper.getObjsByOrder(orders.stream().map(ProductOrder::getId).collect(Collectors.toList()), null);
            orderObjs = objects.stream().collect(Collectors.groupingBy(ProductObject::getOrderId));
        }

        for (ProductOrder order : orders) {
            List<ProductObject> objs = orderObjs.get(order.getId());
            if (ObjectUtils.isEmpty(objs)) {
                continue;
            }

            long nonNum = objs.stream().filter(e -> e.getStatus() == 0).count();
            long qualifiedNum = objs.stream().filter(e -> e.getStatus() == 1).count();
            long defectiveNum = objs.stream().filter(e -> e.getStatus() == 2).count();
            long wasteNum = objs.stream().filter(e -> e.getStatus() == 3).count();
            order.setQualifiedNum(qualifiedNum);
            order.setDefectiveNum(defectiveNum);
            order.setWasteNum(wasteNum);

            order.setCheckProgress(Math.round((objs.size() - nonNum) * 100.0 / objs.size()) + "%");
            order.setTotalProductQuantity(objs.size());
            order.setActualCheckFinishTime(getActualCheckFinishTime(objs));
        }
        return orders;
    }

    private Date getActualCheckFinishTime(List<ProductObject> objs) {
        long nonCheckedNum = objs.stream().filter(e -> e.getStatus() == 0).count();
        if (nonCheckedNum > 0) {
            return null;
        }

        return objs.stream().map(ProductObject::getActualFinishTime).max(Date::compareTo).get();
    }

    /**
     * 获取某年最大批次
     */
    public Integer getMaxBatch(int year) {
        Integer maxBatch = productOrderMapper.getMaxBatch(year);
        return maxBatch == null ? 0 : maxBatch;
    }

    /**
     * 由工艺及数量获取配件使用情况
     */
    public List<AccessoryUseInfoVo> accessoryUseInfo(List<ProductCraftQo> qos) {
        if (ObjectUtils.isEmpty(qos)) {
            return Collections.emptyList();
        }

        Map<Long, Integer> accessoryInventoryMap = accessoryMapper.selectAccessoryInventoryList(null, null, null, null).stream().collect(Collectors.toMap(AccessoryInventoryVO::getId, AccessoryInventoryVO::getTotalArrivalQuantity, (key1, key2) -> key1));
        Map<Long, Integer> accessoryUseMap = accessoryMapper.getAccessoryUseList().stream().collect(Collectors.toMap(AccessoryInventoryVO::getAccessoryId, AccessoryInventoryVO::getTotalUseQuantity, (key1, key2) -> key1));
        Map<Long, Integer> accessoryLossMap = accessoryMapper.getAccessoryLossList().stream().collect(Collectors.toMap(AccessoryInventoryVO::getAccessoryId, AccessoryInventoryVO::getTotalLossQuantity, (key1, key2) -> key1));
        List<Accessory> accessories = productOrderMapper.accessoryUseInfo(qos);
        if (ObjectUtils.isEmpty(accessories)) {
            return Collections.emptyList();
        }

        Map<Long, Integer> craftIdNumMap = qos.stream().collect(Collectors.toMap(ProductCraftQo::getCraftId, ProductCraftQo::getNum, (key1, key2) -> key1));

        List<AccessoryUseInfoVo> vos = new ArrayList<>();
        Map<Long, List<Accessory>> map = accessories.stream().collect(Collectors.groupingBy(e -> e.getAccessoryId()));
        map.forEach((k, v) -> {
            int total = 0;
            for (Accessory accessory : v) {
                total += accessory.getQuantity() * craftIdNumMap.get(accessory.getCraftId());
            }
            Accessory first = v.get(0);
            int totalArrayQuantity = accessoryInventoryMap.get(k) == null ? 0 : accessoryInventoryMap.get(k);
            int totalUseQuantity = accessoryUseMap.get(k) == null ? 0 : accessoryUseMap.get(k);
            int totalLossQuantity = accessoryLossMap.get(k) == null ? 0 : accessoryLossMap.get(k);
            int inventoryQuantity = Math.max((totalArrayQuantity - totalUseQuantity - totalLossQuantity), 0);
            vos.add(new AccessoryUseInfoVo(first.getAccessoryId(), first.getAccessoryNo(), first.getAccessoryName(), first.getAccessorySpec(), first.getAccessoryType(), total, inventoryQuantity));
        });
        return vos;
    }

    /**
     * 新增产品订单
     */
    @Transactional(rollbackFor = Exception.class)
    public void add(ProductOrder order) throws Exception {
        checkProductOrder(order);
        EntityTool.insertEntity(order);
        order.setOrderTime(order.getCreateDate());

        /* 校验配件库存 */
        List<AccessoryUseInfoVo> accessoryUseInfoVos = accessoryUseInfo(order.getCrafts());
        accessoryUseInfoVos.forEach(vo -> {
            int lossNum = 0;
            if (!CollectionUtils.isEmpty(order.getLosses())) {
                Optional<ProductOrderLoss> lossOptional = order.getLosses().stream().filter(loss -> loss.getAccessoryId().equals(vo.getAccessoryId())).findFirst();
                if (lossOptional.isPresent()) {
                    lossNum = lossOptional.get().getLossQuantity();
                }
            }
            if (CommonConstants.ACCESSORY_TYPE_RAW_MATERIAL.equals(vo.getAccessoryType()) && vo.getInventoryQuantity() < (lossNum + vo.getQuantity())) {
                throw new BizDmException("配件库存不足，请补充配件：" + vo.getAccessoryName() + " , 当前库存量: " + vo.getInventoryQuantity());
            }
        });

        /* 生成具体产品 */
        List<ProductObject> msgProductObjects = new ArrayList<>();
        for (ProductCraftQo craft : order.getCrafts()) {
            List<ProductObject> productObjects = new ArrayList<>();
            String objNoPrefix = OrderTool.genProductObjPrefix(productOrderMapper.getCategoryCodeByCraft(craft.getCraftId()), order.getOrderYear(), order.getOrderBatch());
            String maxObjNo = productOrderMapper.getObjMaxNoByPrefix(objNoPrefix);
            Integer baseNum = Strings.isNullOrEmpty(maxObjNo) ? 0 : Integer.parseInt(maxObjNo.substring(OrderTool.getProductObjPrefixLength()));
            for (Integer i = 1; i <= craft.getNum(); i++) {
                ProductObject productObj = new ProductObject(order.getId(), craft.getCraftId(), objNoPrefix + String.format("%04d", baseNum + i));
                EntityTool.insertEntity(productObj);
                productObjects.add(productObj);
            }
            // 保存，刷新产品编号
            productOrderMapper.addProductObjects(productObjects);
            msgProductObjects.addAll(productObjects);
        }

        /* 添加配件损耗 */
        if (!ObjectUtils.isEmpty(order.getLosses())) {
            EntityTool.insertEntity(order.getLosses());
            productOrderMapper.addAccessoryLoss(order.getId(), order.getLosses());
        }

        bizLogService.log("产品生产管理", "产品生产登记: " + order.getOrderNo());
        bizMsgService.sendProductCreateMsg(order, msgProductObjects);
        productOrderMapper.add(order);
    }

    /**
     * 获取订单已使用的产品
     *
     * @param orderId
     * @return
     */
    public ApiResponseBody<List<ProductObjUsedVO>> getUsedProductInOder(Long orderId) {
        return ApiResponseBody.defaultSuccess(productOrderMapper.getUsedProductInOder(orderId));
    }

    /**
     * 修改产品订单
     */
    @Transactional(rollbackFor = Exception.class)
    public void update(ProductOrder order) throws Exception {
        ProductOrder record = productOrderMapper.selectOrderById(order.getId());
        /* 删除订单 */
        del(new Long[]{order.getId()});

        /* 重新下单订单 */

        /* 校验批次 */
        checkProductOrder(order);
        EntityTool.insertEntity(order);
        order.setOrderTime(Objects.isNull(record) ? order.getCreateDate() : record.getOrderTime());

        /* 校验配件库存 */
        List<AccessoryUseInfoVo> accessoryUseInfoVos = accessoryUseInfo(order.getCrafts());
        accessoryUseInfoVos.forEach(vo -> {
            int lossNum = 0;
            if (!CollectionUtils.isEmpty(order.getLosses())) {
                Optional<ProductOrderLoss> lossOptional = order.getLosses().stream().filter(loss -> loss.getAccessoryId().equals(vo.getAccessoryId())).findFirst();
                if (lossOptional.isPresent()) {
                    lossNum = lossOptional.get().getLossQuantity();
                }
            }
            if (CommonConstants.ACCESSORY_TYPE_RAW_MATERIAL.equals(vo.getAccessoryType()) && vo.getInventoryQuantity() < (lossNum + vo.getQuantity())) {
                throw new BizDmException("配件库存不足，请补充配件：" + vo.getAccessoryName() + " , 当前库存量: " + vo.getInventoryQuantity());
            }
        });

        /* 生成具体产品 */
        List<ProductObject> msgProductObjects = new ArrayList<>();
        for (ProductCraftQo craft : order.getCrafts()) {
            List<ProductObject> productObjects = new ArrayList<>();
            String objNoPrefix = OrderTool.genProductObjPrefix(productOrderMapper.getCategoryCodeByCraft(craft.getCraftId()), order.getOrderYear(), order.getOrderBatch());
            String maxObjNo = productOrderMapper.getObjMaxNoByPrefix(objNoPrefix);
            Integer baseNum = Strings.isNullOrEmpty(maxObjNo) ? 0 : Integer.parseInt(maxObjNo.substring(OrderTool.getProductObjPrefixLength()));
            for (Integer i = 1; i <= craft.getNum(); i++) {
                ProductObject productObj = new ProductObject(order.getId(), craft.getCraftId(), objNoPrefix + String.format("%04d", baseNum + i));
                EntityTool.insertEntity(productObj);
                productObjects.add(productObj);
            }
            productOrderMapper.addProductObjects(productObjects);
            msgProductObjects.addAll(productObjects);
        }

        /* 添加配件损耗 */
        if (!ObjectUtils.isEmpty(order.getLosses())) {
            EntityTool.insertEntity(order.getLosses());
            productOrderMapper.addAccessoryLoss(order.getId(), order.getLosses());
        }

        bizLogService.log("产品生产管理", "产品生产登记: " + order.getOrderNo());
        bizMsgService.sendProductCreateMsg(order, msgProductObjects);
        productOrderMapper.add(order);
    }

    /**
     * 编辑时，获取产品生产订单信息
     *
     * @param orderId
     * @return
     */
    public ApiResponseBody<ProductOrder> productOrderInfo(Long orderId) {
        ProductOrder order = productOrderMapper.getProductOrderById(orderId);
        /* 产品明细 */
        order.setCrafts(productOrderMapper.getProductCraftByOrderId(orderId));
        /* TODO 配件损耗情况 */
        order.setLosses(productOrderMapper.getAccessoryLossInfoByOrderId(orderId));
        return ApiResponseBody.defaultSuccess(order);
    }

    /**
     * 产品订单详情
     */
    public ProductOrderDetailVo detail(Long orderId) {
        ProductOrderDetailVo vo = productOrderMapper.getById(orderId);
        if (vo == null) {
            return new ProductOrderDetailVo();
        }

        /* 产品明细 */
        vo.setProducts(productOrderMapper.getObjsByOrder(Arrays.asList(orderId), null));

        /* 配件使用情况 */
        List<AccessoryUseInfoVo> accessoryUseInfo = productOrderMapper.getAccessoryUseInfoByOrderId(orderId);
        Map<Long, List<AccessoryUseInfoVo>> map = accessoryUseInfo.stream().collect(Collectors.groupingBy(AccessoryUseInfoVo::getAccessoryId));
        List<AccessoryUseInfoVo> res = new ArrayList<>();
        map.forEach((k, v) -> {
            AccessoryUseInfoVo first = v.get(0);
            res.add(new AccessoryUseInfoVo(first.getAccessoryId(), first.getAccessoryNo(), first.getAccessoryName(), first.getAccessorySpec(), first.getAccessoryType(), v.size()));
        });
        vo.setAccessoryUseInfoVos(res);

        /* 配件损耗情况 */
        vo.setLosses(productOrderMapper.getAccessoryLossByOrderId(orderId));
        return vo;
    }

    /**
     * 删除产品订单
     */
    @Transactional
    public void del(Long[] ids) {
        /* 删除该订单下所有具体产品 */
        productOrderMapper.delObjsByOrderIds(ids);

        productOrderMapper.del(ids);
    }

    /**
     * 检验明细
     */
    public List<CheckDetailVo> checkDetail(CheckDetailQo qo) {
        List<CheckDetailVo> vos = productOrderMapper.getCheckDetail(qo.getOrderId(), null);

        /* 条件筛选 */
        vos = filterByCondition(qo, vos);

        /* 每个产品应该有的率定参数 */
        List<CheckCalib> productCalibs = productOrderMapper.getCalibByProducts(vos);
        Map<Long, List<CheckCalib>> productCalibMap = productCalibs.stream().collect(Collectors.groupingBy(CheckCalib::getProductId));

        /* 获取具体产品率定参数值 */
        List<CheckCalib> calibValues = productOrderMapper.getCalibValByObjs(vos);
        Map<String, List<CheckCalib>> objectCalibMap = calibValues.stream().collect(Collectors.groupingBy(e -> e.getObjectId() + "-" + e.getCalibId()));
        for (CheckDetailVo vo : vos) {
            List<CheckCalib> productCalib = productCalibMap.get(vo.getProductId());

            List<CheckCalib> copyProductCalib = Utils.deepCopyListBean(productCalib);
            if (!CollectionUtils.isEmpty(copyProductCalib)) {
                // 封装值
                for (CheckCalib calib : copyProductCalib) {
                    List<CheckCalib> checkCalibs = objectCalibMap.get(vo.getObjectId() + "-" + calib.getId());
                    if (!ObjectUtils.isEmpty(checkCalibs)) {
                        calib.setCalibValue(checkCalibs.get(0).getCalibValue());
                    }
                }
                vo.setCheckCalibs(copyProductCalib);
            }
        }
        return vos;
    }

    /**
     * 检验明细
     */
    public void defectiveCheck(PageInfo<CheckDetailVo> pageInfo, Integer pageNum, Integer pageSize) {
        List<CheckDetailVo> vos = productOrderMapper.getCheckDetail(null, null);
        vos = vos.stream().filter(e -> e.getStatus() == 2).collect(Collectors.toList());

        pageInfo.setTotal(vos.size());
        List<CheckDetailVo> list = vos.stream().skip((pageNum - 1) * pageSize).limit(pageSize).collect(Collectors.toList());
        pageInfo.setList(list);
    }

    /**
     * 条件筛选
     */
    private List<CheckDetailVo> filterByCondition(CheckDetailQo qo, List<CheckDetailVo> vos) {
        vos = vos.stream().filter(e -> {
            if (!ObjectUtils.isEmpty(qo.getProductNo())) {
                return e.getObjectNo().contains(qo.getProductNo());
            } else {
                return true;
            }
        }).filter(e -> {
            if (!ObjectUtils.isEmpty(qo.getProductStatus())) {
                return e.getStatus().equals(qo.getProductStatus());
            } else {
                return true;
            }
        }).filter(e -> {
            if (!ObjectUtils.isEmpty(qo.getProductModel())) {
                return e.getProductModel().contains(qo.getProductModel());
            } else {
                return true;
            }
        }).filter(e -> {
            if (!ObjectUtils.isEmpty(qo.getProductName())) {
                return e.getProductName().contains(qo.getProductName());
            } else {
                return true;
            }
        }).collect(Collectors.toList());
        return vos;
    }

    /**
     * 产品检验
     */
    public void productCheck(CheckQo qo) {
        for (Long checkId : qo.getCheckIds()) {
            productOrderMapper.productCheck(checkId, qo);
        }
        String operation = "";
        switch (qo.getStatus()) {
            case CommonConstants.OBJECT_STATUS_QUALIFIED:
                operation = "合格";
                break;
            case CommonConstants.OBJECT_STATUS_DEFECTIVE:
                operation = "次品";
                break;
            case CommonConstants.OBJECT_STATUS_WASTE:
                operation = "废品";
                break;
            default:
                break;
        }
        String operateObjs = productOrderMapper.getObjsByIds(Arrays.asList(qo.getCheckIds()))
                .stream()
                .map(ProductObject::getObjectNo)
                .collect(Collectors.joining(","));
        bizLogService.log("产品检验管理", operation + " : " + operateObjs);
        bizMsgService.asyncSendProductCheckMsg(qo);
    }

    /**
     * 查询所有的检验责任人
     */
    public List<String> getAllProductCheckUser() {
        return productOrderMapper.getAllProductCheckUser();
    }

    /**
     * 设置率定参数
     */
    public void setCalib(List<CheckCalib> checkCalibs) {
        for (CheckCalib calib : checkCalibs) {
            productOrderMapper.delCalib(calib);
            EntityTool.insertEntity(calib);
        }

        productOrderMapper.addCalib(checkCalibs);
    }

    /**
     * 率定参数模板下载
     */
    public void calibTemplateDown(HttpServletResponse response, CheckDetailQo qo) throws IOException {
        List<CheckDetailVo> vos = this.checkDetail(qo);

        String sheetName = "率定参数模板";
        String[] keys = new String[]{"productNo", "productModel", "productName", "calibCode", "calibValue"};
        String[] titles = new String[]{"产品编号", "产品型号", "产品名称", "率定参数名", "率定参数值"};

        Map<String, List<Map<String, Object>>> map = new HashMap<>();
        List<Map<String, Object>> list = new ArrayList<>();
        map.put("率定参数", list);

        for (CheckDetailVo vo : vos) {
            if (!CollectionUtils.isEmpty(vo.getCheckCalibs())) {
                for (CheckCalib calib : vo.getCheckCalibs()) {
                    Map<String, Object> one = new HashMap<>();
                    one.put("productNo", vo.getObjectNo());
                    one.put("productModel", vo.getProductModel());
                    one.put("productName", vo.getProductName());
                    one.put("calibCode", calib.getCalibCode());
                    one.put("calibValue", calib.getCalibValue());
                    list.add(one);
                }
            }
        }
        ExcelUtil.exportNormalExcel(sheetName, titles, keys, map, response, false, "yyyy-MM-dd", false);
    }

    /**
     * 率定参数批量导入
     */
    public String calibImport(String filepath, Long orderId) throws Exception {
        String[][] headers = {{"productNo", "产品编号"}, {"productModel", "产品型号"}, {"productName", "产品名称"}, {"calibCode", "率定参数名"}, {"calibValue", "率定参数值"}};

        List<CheckCalib> calibs;
        try {
            calibs = ExcelUtils.readExcel(filepath, CheckCalib.class, headers);
            calibs.forEach(System.out::println);
        } catch (Exception e) {
            throw new Exception("模板非法，或者类型错误");
        }

        /* 获取所有率定参数 */
        Map<String, Long> calibCodeIdMap = productOrderMapper.getCalibByProducts(null).stream().collect(Collectors.toMap(CheckCalib::getCalibCode, CheckCalib::getId, (key1, key2) -> key1));
        Map<String, Long> objNoIdMap = productOrderMapper.getObjsByOrder(Arrays.asList(orderId), null).stream().collect(Collectors.toMap(ProductObject::getObjectNo, ProductObject::getObjectId, (key1, key2) -> key1));


        /* 设置产品编号和率定参数ID */
        StringBuilder errStr = new StringBuilder();
        for (CheckCalib calib : calibs) {
            Long calibId = calibCodeIdMap.get(calib.getCalibCode());
            if (calibId == null) {
                errStr.append(calib.getCalibCode() + "不存在");
                errStr.append("\r\n");
            } else {
                calib.setCalibId(calibId);
            }

            Long objectId = objNoIdMap.get(calib.getProductNo());
            if (objectId == null) {
                errStr.append(calib.getProductNo() + "不存在");
                errStr.append("\r\n");
            } else {
                calib.setObjectId(objectId);
            }
        }

        if (!ObjectUtils.isEmpty(errStr)) {
            return errStr.toString();
        }
        this.setCalib(calibs);
        return null;
    }

    /**
     * 生成合格证
     */
    public void genCertificate(HttpServletResponse response, List<Long> ids) throws Exception {
        /* 创建目标文件夹 */
        String dir = Utils.isLinux() ? "/home/" + System.currentTimeMillis() : "D:/" + System.currentTimeMillis();
        new File(dir).mkdirs();

        /* 获取产品信息 */
        List<ProductObject> objs = productOrderMapper.getObjsByIds(ids);

        /* 生成合格证并压缩 */
        List<String> wordPaths = new ArrayList<>(objs.size());
        for (ProductObject obj : objs) {
            wordPaths.add(getWordPath(obj, Utils.isLinux() ? Cache.ftlPath + "template.ftl" : "D:/ftl生成word/template.ftl", dir));
        }
        FileCompressUtil.zipDir(dir, null);

        responseFile(response, dir);
        return;
    }

    /**
     * 响应文件
     */
    private void responseFile(HttpServletResponse response, String dir) throws IOException {
        response.setHeader("Content-disposition", "attachment;filename" + dir.substring(dir.lastIndexOf("\\") + 1));
        FileInputStream in = new FileInputStream(dir + ".zip");
        int len;
        byte[] buffer = new byte[1024];
        ServletOutputStream out = response.getOutputStream();
        while ((len = in.read(buffer)) > 0) {
            out.write(buffer, 0, len);
        }
        in.close();
        out.close();
    }

    /**
     * 生成Word并返回路径
     */
    private String getWordPath(ProductObject obj, String ftlPath, String dir) {
        Map<String, Object> dataMap = new HashMap<>(6);
        if ("传感器".equals(obj.getCertType())) {
            dataMap.put("flag1", "◎");
            dataMap.put("flag2", "○");
        } else {
            dataMap.put("flag1", "○");
            dataMap.put("flag2", "◎");
        }

        dataMap.put("A", obj.getProductModel());
        dataMap.put("B", obj.getObjectNo());
        dataMap.put("C", obj.getProduceUserName());
        dataMap.put("D", obj.getCheckFinishTime() == null ? "未检验" : TimeUtil.format2Day(obj.getCheckFinishTime()));

        // 创建临时文件
        String outPath = dir + "/" + obj.getObjectNo() + ".doc";
        new WordUtils().createDocFile(ftlPath, dataMap, outPath, 1);
        return outPath;
    }

    /**
     * 产品订单数量统计
     */
    public ProductOrderStatVo statNum() {
        ProductOrderStatVo vo = new ProductOrderStatVo();
        vo.setTotalBatchNum(productOrderMapper.getBatchNum(false));
        vo.setCurYearBatchNum(productOrderMapper.getBatchNum(true));

        vo.setTotalProductNum(productOrderMapper.getProductNum(false));
        vo.setCurYearProductNum(productOrderMapper.getProductNum(true));

        System.out.println(vo);
        return vo;
    }

    /**
     * 产品累计生产量
     */
    public List<ProductOrderProductStatVo> statProductNum(Integer year) {
        List<ProductOrderProductStatVo> vos = new ArrayList<>();
        Map<String, List<CheckDetailVo>> productNameMap = productOrderMapper.getCheckDetail(null, year).stream().filter(e -> !ObjectUtils.isEmpty(e.getProductName()))
                .collect(Collectors.groupingBy(CheckDetailVo::getProductName));

        productNameMap.forEach((k, v) -> {
            vos.add(new ProductOrderProductStatVo(k, v.size()));
        });
        return vos;
    }

    /**
     * 产品检验数量统计
     */
    public ProductCheckStatVo checkStatNum() {
        ProductCheckStatVo vo = new ProductCheckStatVo();

        List<ProductObject> objs = productOrderMapper.getObjsByOrder(null, null);
        if (ObjectUtils.isEmpty(objs)) {
            return vo;
        }

        // 产品状态：0 - 未检验；1 - 合格品 ；2 - 次品；3 - 废品
        vo.setNonCheckedObjNum(objs.stream().filter(e -> e.getStatus() == 0).count());
        vo.setCheckedObjNum(objs.size() - vo.getNonCheckedObjNum());

        AtomicLong checkedOrderNum = new AtomicLong();
        AtomicLong checkingOrderNum = new AtomicLong();
        AtomicLong nonCheckedOrderNum = new AtomicLong();
        Map<Long, List<ProductObject>> orderObjsMap = objs.stream().collect(Collectors.groupingBy(ProductObject::getOrderId));
        orderObjsMap.forEach((orderId, orderObjs) -> {
            long nonCheckedNum = orderObjs.stream().filter(e -> e.getStatus() == 0).count();
            long checkedNum = orderObjs.stream().filter(e -> e.getStatus() != 0).count();
            if (nonCheckedNum == 0 && checkedNum != 0) {
                checkedOrderNum.getAndIncrement();
            } else if (nonCheckedNum != 0 && checkedNum == 0) {
                nonCheckedOrderNum.getAndIncrement();
            } else if (nonCheckedNum != 0 && checkedNum != 0) {
                checkingOrderNum.getAndIncrement();
            }
        });
        vo.setNonCheckedOrderNum(nonCheckedOrderNum.get());
        vo.setCheckingOrderNum(checkingOrderNum.get());
        vo.setCheckedOrderNum(checkedOrderNum.get());
        return vo;
    }

    /**
     * 产品检验状态统计
     */
    public List<ProductCheckStatusStatVo> checkStatStatus(Integer year) {
        List<ProductCheckStatusStatVo> vos = new ArrayList<>();

        List<ProductObject> objs = productOrderMapper.getObjsByOrder(null, year);
        if (ObjectUtils.isEmpty(objs)) {
            return vos;
        }

        Map<String, List<ProductObject>> nameObjsMap = objs.stream().filter(e -> e.getProductName() != null).collect(Collectors.groupingBy(ProductObject::getProductName));
        nameObjsMap.forEach((productName, nameObjs) -> {
            long count0 = nameObjs.stream().filter(e -> e.getStatus() == 0).count();
            long count1 = nameObjs.stream().filter(e -> e.getStatus() == 1).count();
            long count2 = nameObjs.stream().filter(e -> e.getStatus() == 2).count();
            long count3 = nameObjs.stream().filter(e -> e.getStatus() == 3).count();
            vos.add(new ProductCheckStatusStatVo(productName, count0, count1, count2, count3));
        });
        return vos;
    }

    /**
     * 产品库存统计列表
     * CheckDetailQo.productStatus - 产品应用现状 product.apply_status：0 - 弃用， 1 - 在用
     */
    public void productInventoryList(PageInfo<ProductInventoryVo> pageInfo, CheckDetailQo qo) {

        /**
         * TODO 待出库数量： 销售订单中未出库数量
         */
        List<ProductInventoryVo> vos = new ArrayList<>();
        List<ProductObject> objects = productOrderMapper.productInventory(qo.getProductStatus());
        Map<String, List<ProductObject>> productNameObjs = objects.stream().collect(Collectors.groupingBy(ProductObject::getProductName));
        /* 产品的计划销售数量 */
        if (!CollectionUtils.isEmpty(objects)) {
            List<ProductSaleQuantityDTO> productSaleQuantityList = productOrderMapper.getProductSaleQuantity(null, objects.stream().map(ProductObject::getProductId).distinct().collect(Collectors.toList()));
            productNameObjs.forEach((productName, objs) -> {
                if (!ObjectUtils.isEmpty(qo.getProductName())) {
                    if (!productName.contains(qo.getProductName())) {
                        return;
                    }
                }
                if (!ObjectUtils.isEmpty(qo.getProductModel())) {
                    if (!objs.get(0).getProductModel().contains(qo.getProductModel())) {
                        return;
                    }
                }

                long nonCheckNum = objs.stream().filter(e -> e.getStatus() == 0).count();
                long qualifiedNum = objs.stream().filter(e -> e.getStatus() == 1).count();
                long defectiveNum = objs.stream().filter(e -> e.getStatus() == 2).count();
                long wasteNum = objs.stream().filter(e -> e.getStatus() == 3).count();

                // 已出库数量
                long toOutNum = 0;
                Optional<ProductSaleQuantityDTO> optional = productSaleQuantityList.stream().filter(e -> e.getProductId().equals(objs.get(0).getProductId())).findFirst();
                if (optional.isPresent()) {
                    toOutNum = optional.get().getQuantity() - objs.stream().filter(ProductObject::getOutStatus).count();
                }

                ProductInventoryVo vo = new ProductInventoryVo(productName, objs.get(0).getProductModel(), objs.get(0).getProductId());
                vo.setNonCheckNum(nonCheckNum);
                vo.setDefectiveNum(defectiveNum);
                vo.setWasteNum(wasteNum);
                vo.setAvailNum(qualifiedNum);
                vo.setInNum(qualifiedNum + nonCheckNum);
                vo.setToOutNum(toOutNum);
                vos.add(vo);
            });
        }

        /* 分页 */
        pageInfo.setTotal(vos.size());
        List<ProductInventoryVo> list = vos.stream().skip((long) (qo.getPageNum() - 1) * qo.getPageSize()).limit(qo.getPageSize()).collect(Collectors.toList());
        pageInfo.setList(list);
    }

    /**
     * 产品库存统计数量
     */
    public ProductInventoryVo productInventoryNum(String year) {
        ProductInventoryVo vo = new ProductInventoryVo();
        vo.setNonCheckNum(0L);
        vo.setDefectiveNum(0L);
        vo.setWasteNum(0L);
        vo.setAvailNum(0L);
        vo.setInNum(0L);
        vo.setToOutNum(0L);

        ProductOrderQo queryParams = new ProductOrderQo();
        queryParams.setOrderYear(year);
        List<ProductOrder> orderList = productOrderMapper.list(queryParams);
        List<ProductObject> objs = new ArrayList<>();
        if (!CollectionUtils.isEmpty(orderList)) {
            objs = productOrderMapper.getObjsByOrder(orderList.stream().map(ProductOrder::getId).collect(Collectors.toList()), null);
            vo.setNonCheckNum(objs.stream().filter(e -> e.getStatus() == 0).count());
            vo.setAvailNum(objs.stream().filter(e -> e.getStatus() == 1).count());
            vo.setDefectiveNum(objs.stream().filter(e -> e.getStatus() == 2).count());
            vo.setWasteNum(objs.stream().filter(e -> e.getStatus() == 3).count());
            vo.setInNum(vo.getAvailNum() + vo.getNonCheckNum());
        }

        // 待出库数量
        List<SaleOrder> saleOrderList = saleOrderMapper.selectBySignDate(year);
        if (!CollectionUtils.isEmpty(saleOrderList)) {
            List<Long> orderIds = saleOrderList.stream().map(SaleOrder::getId).collect(Collectors.toList());
            List<SaleOrderGoods> goodsList = saleOrderGoodsMapper.listByOrderIds(orderIds);
            if (!CollectionUtils.isEmpty(goodsList)) {
                Long saleQuantityTotal = goodsList.stream().mapToLong(SaleOrderGoods::getSaleQuantity).sum();
                Long outQuantityTotal = objs.stream().filter(e -> !Objects.isNull(e.getSaleOrderId()) && orderIds.contains(e.getSaleOrderId())).count();
                Long toOutNum = saleQuantityTotal >= outQuantityTotal ? saleQuantityTotal - outQuantityTotal : 0;
                vo.setToOutNum(toOutNum);
            }
        }
        return vo;
    }

    /**
     * 产品状态列表
     *
     * @param param
     * @return
     */
    public ApiResponseBody<PageInfo<ProductStatusVO>> productStatusList(ProductStatusParam param) {
        PageHelper.startPage(param.getPageNum(), param.getPageSize());
        List<ProductStatusVO> productStatusList = productOrderMapper.selectProductStatusList(param);
        PageInfo<ProductStatusVO> pageInfo = new PageInfo<>(productStatusList);
        return ApiResponseBody.defaultSuccess(pageInfo);
    }

    public void checkProductOrder(ProductOrder order) {
        /* 校验批次 */
        if (Strings.isNullOrEmpty(order.getOrderBatch()) || 3 != order.getOrderBatch().length()) {
            throw new BizDmException("请输入生产批次（三位）");
        }
        if (Objects.isNull(order.getOrderYear())) {
            throw new BizDmException("请输入生产年份");
        }
        if (productOrderMapper.isExistedOrderBatch(order)) {
            throw new BizDmException("生产批次已被使用");
        }
    }

}
