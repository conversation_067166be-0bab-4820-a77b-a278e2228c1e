package com.fawkes.project.example.service.impl;

import com.fawkes.core.base.api.ApiResponseBody;
import com.fawkes.core.enums.DeleteFlagEnum;
import com.fawkes.core.utils.EntityTool;
import com.fawkes.project.example.common.exception.BizDmException;
import com.fawkes.project.example.common.mapper.ProductCraftComposeMapper;
import com.fawkes.project.example.common.mapper.ProductCraftMapper;
import com.fawkes.project.example.common.mapper.ProductMapper;
import com.fawkes.project.example.common.model.ProductCraft;
import com.fawkes.project.example.common.model.ProductCraftCompose;
import com.fawkes.project.example.common.utils.MyEntityTool;
import com.fawkes.project.example.common.utils.OrderTool;
import com.fawkes.project.example.domain.dto.ProductDTO;
import com.fawkes.project.example.domain.vo.ProductCraftComposeVO;
import com.fawkes.project.example.domain.vo.ProductCraftVO;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.google.common.base.Strings;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 产品信息维护
 *
 * <AUTHOR>
 */
@Service
@Slf4j
public class ProductService {
    @Resource
    ProductMapper productMapper;
    @Resource
    ProductCraftMapper productCraftMapper;
    @Resource
    ProductCraftComposeMapper productCraftComposeMapper;

    /**
     * 新增工艺
     *
     * @param productCraftVO
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    public ApiResponseBody add(ProductCraftVO productCraftVO) {
        checkCraftCode(productCraftVO);
        // 新增工艺
        productCraftVO.setApplyStatus(true);
        EntityTool.insertEntity(productCraftVO);
        productCraftMapper.insert(productCraftVO);
        // 新增明细
        if (!CollectionUtils.isEmpty(productCraftVO.getComposeList())) {
            productCraftVO.getComposeList().forEach(e -> e.setCraftId(productCraftVO.getId()));
            EntityTool.insertEntity(productCraftVO.getComposeList());
            productCraftComposeMapper.insertBatch(productCraftVO.getComposeList());
        }
        // 更新产品应用状态
        productMapper.updateApplyStatusByProductId(productCraftVO.getProductId(), true, MyEntityTool.getUpdateInfo());
        return ApiResponseBody.defaultSuccess();
    }

    public ApiResponseBody queryCraft(String craftId) {
        ProductCraftVO craftVO = productCraftMapper.selectById(Long.parseLong(craftId));
        if (!Objects.isNull(craftVO)) {
            craftVO.setComposeList(productCraftComposeMapper.selectAllByCraftId(Long.parseLong(craftId)));
        }
        return ApiResponseBody.defaultSuccess(craftVO);
    }

    @Transactional(rollbackFor = Exception.class)
    public ApiResponseBody deleteCraft(String craftId) {
        ProductCraft craft = productCraftMapper.selectById(Long.parseLong(craftId));
        List<ProductCraft> usedCraftList = productCraftMapper.selectUsedCraftByProductIds(Collections.singletonList(craft.getProductId()));
        if (!CollectionUtils.isEmpty(usedCraftList) && usedCraftList.stream().anyMatch(e -> e.getId().equals(Long.parseLong(craftId)))) {
            throw new BizDmException("工艺已被使用");
        }
        productCraftMapper.deleteById(Long.parseLong(craftId), MyEntityTool.getUpdateInfo());
        return ApiResponseBody.defaultSuccess();
    }

    @Transactional(rollbackFor = Exception.class)
    public ApiResponseBody deleteCraftBatchByProductIds(String productIds) {
        List<Long> ids = Arrays.stream(productIds.split(",")).map(Long::parseLong).collect(Collectors.toList());
        List<ProductDTO> productList = productMapper.selectByIds(ids);
        if (!CollectionUtils.isEmpty(productList)) {
            List<ProductCraft> usedCraftList = productCraftMapper.selectUsedCraftByProductIds(ids);
            if (!CollectionUtils.isEmpty(usedCraftList)) {
                throw new BizDmException(String.format("工艺(%s)已被使用", usedCraftList.get(0).getCraftCode()));
            }
            productCraftMapper.deleteByProductIds(ids, MyEntityTool.getUpdateInfo());
        }
        return ApiResponseBody.defaultSuccess();
    }

    @Transactional(rollbackFor = Exception.class)
    public ApiResponseBody update(ProductCraftVO productCraftVO) {
        checkCraftCode(productCraftVO);
        // 修改工艺
        ProductCraft record = productCraftMapper.selectByPrimaryKey(productCraftVO.getId());
        productCraftVO.setApplyStatus(record.getApplyStatus());
        EntityTool.updateEntity(productCraftVO);
        productCraftMapper.updateByPrimaryKey(productCraftVO);
        // 修改工艺明细
        List<ProductCraftComposeVO> oldComposeList = productCraftComposeMapper.selectAllByCraftId(productCraftVO.getId());
        List<ProductCraftCompose> toAddComposeList = new ArrayList<>();
        oldComposeList.forEach(e -> e.setDeleteFlag(DeleteFlagEnum.DATA_WARN.getFlag()));
        if (!CollectionUtils.isEmpty(productCraftVO.getComposeList())) {
            productCraftVO.getComposeList().forEach(compose -> {
                Optional<ProductCraftComposeVO> optional = oldComposeList.stream().filter(e -> (e.getId().equals(compose.getId()) || e.getAccessoryId().equals(compose.getId()))).findFirst();
                if (optional.isPresent()) {
                    optional.get().setDeleteFlag(DeleteFlagEnum.DATA_OK.getFlag());
                    optional.get().setQuantity(compose.getQuantity());
                } else {
                    compose.setCraftId(productCraftVO.getId());
                    toAddComposeList.add(compose);
                }
            });
        }
        if (!CollectionUtils.isEmpty(toAddComposeList)) {
            EntityTool.insertEntity(toAddComposeList);
            productCraftComposeMapper.insertBatch(toAddComposeList);
        }
        if (!CollectionUtils.isEmpty(oldComposeList)) {
            EntityTool.updateEntity(oldComposeList);
            productCraftComposeMapper.updateBatch(oldComposeList);
        }
        return ApiResponseBody.defaultSuccess();
    }

    @Transactional(rollbackFor = Exception.class)
    public ApiResponseBody changeCraftApplyStatus(String craftId, Boolean applyStatus) {
        if (!applyStatus) {
            if (productCraftMapper.judgeUsedCraftById(Long.parseLong(craftId))) {
                throw new BizDmException("工艺已被使用");
            }
        }
        ProductCraft craft = productCraftMapper.selectByPrimaryKey(Long.parseLong(craftId));
        craft.setApplyStatus(applyStatus);
        EntityTool.updateEntity(craft);
        productCraftMapper.updateByPrimaryKey(craft);
        // 启用工艺时，更新产品的启用状态
        if (applyStatus) {
            productMapper.updateApplyStatusByProductId(craft.getProductId(), true, MyEntityTool.getUpdateInfo());
        }

        HashMap<String, Object> changeTips = new HashMap<>(2);
        changeTips.put("productId", String.valueOf(craft.getProductId()));
        changeTips.put("hasActiveCraft", productCraftMapper.hasActiveCraftByProductId(craft.getProductId()));
        return ApiResponseBody.defaultSuccess(changeTips);
    }

    @Transactional(rollbackFor = Exception.class)
    public ApiResponseBody changeProductApplyStatus(String productIds, Boolean applyStatus) {
        List<Long> ids = Arrays.stream(productIds.split(",")).map(Long::parseLong).collect(Collectors.toList());
        List<ProductDTO> productList = productMapper.selectByIds(ids);
        // 直接启用
        if (applyStatus) {
            productList.forEach(product -> product.setApplyStatus(true));
            EntityTool.updateEntity(productList);
            productMapper.updateBatch(productList);
            return ApiResponseBody.defaultSuccess();
        }
        // 弃用
        List<ProductCraftVO> craftList = checkCraftUsedByProductIds(ids);
        if (!CollectionUtils.isEmpty(craftList)) {
            craftList.forEach(e -> e.setApplyStatus(false));
            EntityTool.updateEntity(craftList);
            productCraftMapper.updateBatch(craftList);
        }
        if (!CollectionUtils.isEmpty(productList)) {
            productList.forEach(e -> e.setApplyStatus(false));
            EntityTool.updateEntity(productList);
            productMapper.updateBatch(productList);
        }
        return ApiResponseBody.defaultSuccess();
    }

    public ApiResponseBody page(Integer pageNum, Integer pageSize, String model, String name, Boolean applyStatus) {
        PageHelper.startPage(pageNum, pageSize);
        List<ProductDTO> productDTOList = productMapper.selectProductListByModelAndNameAndApplyStatus(model, name, applyStatus);
        if (!CollectionUtils.isEmpty(productDTOList)) {
            List<Long> productIds = productDTOList.stream().map(ProductDTO::getId).collect(Collectors.toList());
            List<ProductCraftVO> craftList = productCraftMapper.selectByProductIds(productIds, applyStatus);
            if (!CollectionUtils.isEmpty(craftList)) {
                Map<Long, List<ProductCraftVO>> craftMap = craftList.stream().collect(Collectors.groupingBy(ProductCraftVO::getProductId));
                productDTOList.forEach(e -> e.setCraftList(craftMap.get(e.getId())));
            }
        }
        PageInfo<ProductDTO> pageInfo = new PageInfo<>(productDTOList);
        return ApiResponseBody.defaultSuccess(pageInfo);
    }

    public ApiResponseBody genLatestCraftCode(String productId) {
        List<String> craftCodeList = productCraftMapper.selectAllCraftCodeByProductId(Long.parseLong(productId));
        String craftCode = OrderTool.genCraftCodeFromCodeList(craftCodeList);
        return ApiResponseBody.defaultSuccess(craftCode);
    }

    private void checkCraftCode(ProductCraftVO productCraftVO) {
        if (Objects.isNull(productCraftVO.getProductId())) {
            throw new BizDmException("请选择产品!");
        }
        if (!Strings.isNullOrEmpty(productCraftVO.getCraftCode()) && !OrderTool.isValidCraftCode(productCraftVO.getCraftCode())) {
            throw new BizDmException("版本号以v开头，且使用2号段，比如v0.1、v1.0、v1.1");
        }
        if (productCraftMapper.isExistedCraftCode(productCraftVO)) {
            throw new BizDmException("工艺已存在!");
        }
    }

    /**
     * 校验产品下的工艺是否已被使用
     *
     * @param productIds 待检测的产品id数组
     * @return 均被使用时，返回所有工艺
     */
    public List<ProductCraftVO> checkCraftUsedByProductIds(List<Long> productIds) {
        List<ProductCraftVO> craftList = productCraftMapper.selectActiveByProductIds(productIds);
        if (!CollectionUtils.isEmpty(craftList)) {
            List<Long> activeCraftIds = craftList.stream().map(ProductCraft::getId).collect(Collectors.toList());
            List<ProductCraftVO> craftInUsedList = productCraftMapper.judgeUsedCraftByIds(activeCraftIds);
            if (!CollectionUtils.isEmpty(craftInUsedList)) {
                List<String> inUsedCraftCodeList = craftInUsedList.stream().map(e -> String.join(":", e.getProductMode(), e.getCraftCode())).collect(Collectors.toList());
                throw new BizDmException(String.format("工艺已被使用: %s", String.join(",", inUsedCraftCodeList)));
            }
        }
        return craftList;
    }
}
