package com.fawkes.project.example;

import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.builder.SpringApplicationBuilder;
import org.springframework.cloud.client.discovery.EnableDiscoveryClient;

@EnableDiscoveryClient
@SpringBootApplication
public class ProjectExampleApplication {

    public static void main(String[] args) {
        new SpringApplicationBuilder(ProjectExampleApplication.class).allowCircularReferences(true).run(args);
    }

}
