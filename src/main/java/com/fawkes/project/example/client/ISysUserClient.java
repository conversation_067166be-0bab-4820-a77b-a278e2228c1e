package com.fawkes.project.example.client;

import com.fawkes.core.base.api.ApiResponseBody;
import com.fawkes.core.constants.AppConstants;
import com.fawkes.project.example.client.fallback.SysUserClientFallBack;
import com.fawkes.project.example.domain.dto.SysUserDTO;
import io.swagger.annotations.ApiOperation;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;

import java.util.List;

/**
 * 用户接口调用
 *
 * <AUTHOR>
 */
@FeignClient(
        name = AppConstants.SYS_APPLICATION_USER_NAME,
        fallback = SysUserClientFallBack.class
)
public interface ISysUserClient {
    /**
     * 获取用户列表
     *
     * @return
     */
    @ApiOperation("获取用户列表")
    @GetMapping(path = "/users")
    ApiResponseBody<List<SysUserDTO>> getUserList();
}
