package com.fawkes.project.example.client.fallback;

import com.fawkes.core.base.api.ApiResponseBody;
import com.fawkes.core.enums.BizCodeMsgEnum;
import com.fawkes.project.example.client.IBpmClient;
import org.springframework.stereotype.Component;

import java.util.Map;

@Component
public class BpmClientFallBack implements IBpmClient {
    @Override
    public ApiResponseBody startProcess(String modelKey, String formKey, String bizId, Map<String, Object> variable, String comment, String stageFlag, String customProcessFlag, String fileToken, String customId) {
        return ApiResponseBody.error(BizCodeMsgEnum.SYS_ERROR);
    }

    @Override
    public ApiResponseBody completeProcess(String taskId, String comment, String approval, Map<String, Object> variable, String stageFlag) {
        return ApiResponseBody.error(BizCodeMsgEnum.SYS_ERROR);
    }
}
