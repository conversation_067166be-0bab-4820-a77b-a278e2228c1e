<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE generatorConfiguration PUBLIC "-//mybatis.org//DTD MyBatis Generator Configuration 1.0//EN"
        "http://mybatis.org/dtd/mybatis-generator-config_1_0.dtd">
<generatorConfiguration>

    <context id="test" targetRuntime="MyBatis3">
        <plugin type="org.mybatis.generator.plugins.EqualsHashCodePlugin"></plugin>
        <plugin type="org.mybatis.generator.plugins.SerializablePlugin"></plugin>
        <plugin type="org.mybatis.generator.plugins.ToStringPlugin"></plugin>
        <!--本插件提供实体类的lombok注解，代码简洁，但是mybatis-generator的包需要从内网的Nexus下载-->
        <!-- <plugin type="org.mybatis.generator.plugins.LombokPlugin">
             <property name="hasLombok" value="true"/>
         </plugin>-->
        <!-- 使用自定义的插件 -->
        <commentGenerator type="com.fawkes.project.example.common.config.MyCommentGenerator">
            <!-- 这个元素用来去除指定生成的注释中是否包含生成的日期 false:表示保护 -->
            <!-- 如果生成日期，会造成即使修改一个字段，整个实体类所有属性都会发生变化，不利于版本控制，所以设置为true -->
            <property name="suppressDate" value="true"/>
            <!-- 是否去除自动生成的注释 true：是 ： false:否 -->
            <property name="suppressAllComments" value="false"/>
        </commentGenerator>
        <!--数据库链接URL，用户名、密码 -->
        <jdbcConnection driverClass="com.mysql.cj.jdbc.Driver"
                        connectionURL="***************************************************************************************************************************************************"
                        userId="root" password="7xYgBGquHh7X6@!x">
        </jdbcConnection>

        <!-- 指定时间类型为LocalDate-->
        <javaTypeResolver>
            <!-- This property is used to specify whether MyBatis Generator should
                force the use of java.math.BigDecimal for DECIMAL and NUMERIC fields, -->
            <property name="forceBigDecimals" value="false"/>
            <property name="useJSR310Types" value="true"/>
        </javaTypeResolver>
        <!-- 生成模型的包名和位置 -->
        <javaModelGenerator targetPackage="com.fawkes.project.example.common.model"
                            targetProject="src/main/java">
            <property name="enableSubPackages" value="true"/>
            <property name="trimStrings" value="true"/>
            <property name="rootClass" value="com.fawkes.core.base.model.BaseEntity"/>
        </javaModelGenerator>
        <!-- 生成映射文件的包名和位置 -->
        <sqlMapGenerator targetPackage="com.fawkes.project.example.common.mapper"
                         targetProject="src/main/java">
            <property name="enableSubPackages" value="true"/>
        </sqlMapGenerator>
        <!-- 生成DAO的包名和位置 -->
        <javaClientGenerator type="XMLMAPPER"
                             targetPackage="com.fawkes.project.example.common.mapper"
                             targetProject="src/main/java">
            <property name="enableSubPackages" value="true"/>
        </javaClientGenerator>
        <!--        <table tableName="accessory_dict" domainObjectName="AccessoryDict"-->
        <!--               enableCountByExample="true" enableUpdateByExample="true"-->
        <!--               enableDeleteByExample="true" enableSelectByExample="true"-->
        <!--               enableSelectByPrimaryKey="true">-->
        <!--            <columnOverride column="create_date" javaType="java.sql.Timestamp"/>-->
        <!--            <columnOverride column="update_date" javaType="java.sql.Timestamp"/>-->
        <!--        </table>-->
        <!--        <table tableName="accessory" domainObjectName="Accessory"-->
        <!--               enableCountByExample="true" enableUpdateByExample="true"-->
        <!--               enableDeleteByExample="true" enableSelectByExample="true"-->
        <!--               enableSelectByPrimaryKey="true">-->
        <!--            <columnOverride column="create_date" javaType="java.sql.Timestamp"/>-->
        <!--            <columnOverride column="update_date" javaType="java.sql.Timestamp"/>-->
        <!--        </table>-->
        <!--        <table tableName="accessory_order" domainObjectName="AccessoryOrder"-->
        <!--               enableCountByExample="true" enableUpdateByExample="true"-->
        <!--               enableDeleteByExample="true" enableSelectByExample="true"-->
        <!--               enableSelectByPrimaryKey="true">-->
        <!--            <columnOverride column="create_date" javaType="java.sql.Timestamp"/>-->
        <!--            <columnOverride column="update_date" javaType="java.sql.Timestamp"/>-->
        <!--        </table>-->
        <!--        <table tableName="accessory_order_goods" domainObjectName="AccessoryOrderGoods"-->
        <!--               enableCountByExample="true" enableUpdateByExample="true"-->
        <!--               enableDeleteByExample="true" enableSelectByExample="true"-->
        <!--               enableSelectByPrimaryKey="true">-->
        <!--            <columnOverride column="create_date" javaType="java.sql.Timestamp"/>-->
        <!--            <columnOverride column="update_date" javaType="java.sql.Timestamp"/>-->
        <!--        </table>-->
        <!--        <table tableName="accessory_order_goods_log" domainObjectName="AccessoryOrderGoodsLog"-->
        <!--               enableCountByExample="true" enableUpdateByExample="true"-->
        <!--               enableDeleteByExample="true" enableSelectByExample="true"-->
        <!--               enableSelectByPrimaryKey="true">-->
        <!--            <columnOverride column="create_date" javaType="java.sql.Timestamp"/>-->
        <!--            <columnOverride column="update_date" javaType="java.sql.Timestamp"/>-->
        <!--        </table>-->
        <!--        <table tableName="accessory_order_paid_log" domainObjectName="AccessoryOrderPaidLog"-->
        <!--               enableCountByExample="true" enableUpdateByExample="true"-->
        <!--               enableDeleteByExample="true" enableSelectByExample="true"-->
        <!--               enableSelectByPrimaryKey="true">-->
        <!--            <columnOverride column="create_date" javaType="java.sql.Timestamp"/>-->
        <!--            <columnOverride column="update_date" javaType="java.sql.Timestamp"/>-->
        <!--        </table>-->
<!--        <table tableName="product" domainObjectName="Product"-->
<!--               enableCountByExample="true" enableUpdateByExample="true"-->
<!--               enableDeleteByExample="true" enableSelectByExample="true"-->
<!--               enableSelectByPrimaryKey="true">-->
<!--            <columnOverride column="create_date" javaType="java.sql.Timestamp"/>-->
<!--            <columnOverride column="update_date" javaType="java.sql.Timestamp"/>-->
<!--        </table>-->
<!--        <table tableName="product_calib" domainObjectName="ProductCalib"-->
<!--               enableCountByExample="true" enableUpdateByExample="true"-->
<!--               enableDeleteByExample="true" enableSelectByExample="true"-->
<!--               enableSelectByPrimaryKey="true">-->
<!--            <columnOverride column="create_date" javaType="java.sql.Timestamp"/>-->
<!--            <columnOverride column="update_date" javaType="java.sql.Timestamp"/>-->
<!--        </table>-->
<!--        <table tableName="product_param" domainObjectName="ProductParam"-->
<!--               enableCountByExample="true" enableUpdateByExample="true"-->
<!--               enableDeleteByExample="true" enableSelectByExample="true"-->
<!--               enableSelectByPrimaryKey="true">-->
<!--            <columnOverride column="create_date" javaType="java.sql.Timestamp"/>-->
<!--            <columnOverride column="update_date" javaType="java.sql.Timestamp"/>-->
<!--        </table>-->
<!--        <table tableName="product_craft" domainObjectName="ProductCraft"-->
<!--               enableCountByExample="true" enableUpdateByExample="true"-->
<!--               enableDeleteByExample="true" enableSelectByExample="true"-->
<!--               enableSelectByPrimaryKey="true">-->
<!--            <columnOverride column="create_date" javaType="java.sql.Timestamp"/>-->
<!--            <columnOverride column="update_date" javaType="java.sql.Timestamp"/>-->
<!--        </table>-->
<!--        <table tableName="product_craft_compose" domainObjectName="ProductCraftCompose"-->
<!--               enableCountByExample="true" enableUpdateByExample="true"-->
<!--               enableDeleteByExample="true" enableSelectByExample="true"-->
<!--               enableSelectByPrimaryKey="true">-->
<!--            <columnOverride column="create_date" javaType="java.sql.Timestamp"/>-->
<!--            <columnOverride column="update_date" javaType="java.sql.Timestamp"/>-->
<!--        </table>-->
<!--        <table tableName="client" domainObjectName="Client"-->
<!--               enableCountByExample="true" enableUpdateByExample="true"-->
<!--               enableDeleteByExample="true" enableSelectByExample="true"-->
<!--               enableSelectByPrimaryKey="true">-->
<!--            <columnOverride column="create_date" javaType="java.sql.Timestamp"/>-->
<!--            <columnOverride column="update_date" javaType="java.sql.Timestamp"/>-->
<!--        </table>-->
<!--        <table tableName="client_contacts" domainObjectName="ClientContacts"-->
<!--               enableCountByExample="true" enableUpdateByExample="true"-->
<!--               enableDeleteByExample="true" enableSelectByExample="true"-->
<!--               enableSelectByPrimaryKey="true">-->
<!--            <columnOverride column="create_date" javaType="java.sql.Timestamp"/>-->
<!--            <columnOverride column="update_date" javaType="java.sql.Timestamp"/>-->
<!--        </table>-->
<!--        <table tableName="sale_order" domainObjectName="SaleOrder"-->
<!--               enableCountByExample="true" enableUpdateByExample="true"-->
<!--               enableDeleteByExample="true" enableSelectByExample="true"-->
<!--               enableSelectByPrimaryKey="true">-->
<!--            <columnOverride column="create_date" javaType="java.sql.Timestamp"/>-->
<!--            <columnOverride column="update_date" javaType="java.sql.Timestamp"/>-->
<!--        </table>-->
<!--        <table tableName="sale_order_goods" domainObjectName="SaleOrderGoods"-->
<!--               enableCountByExample="true" enableUpdateByExample="true"-->
<!--               enableDeleteByExample="true" enableSelectByExample="true"-->
<!--               enableSelectByPrimaryKey="true">-->
<!--            <columnOverride column="create_date" javaType="java.sql.Timestamp"/>-->
<!--            <columnOverride column="update_date" javaType="java.sql.Timestamp"/>-->
<!--        </table>-->
<!--        <table tableName="sale_goods_alter" domainObjectName="SaleGoodsAlter"-->
<!--               enableCountByExample="true" enableUpdateByExample="true"-->
<!--               enableDeleteByExample="true" enableSelectByExample="true"-->
<!--               enableSelectByPrimaryKey="true">-->
<!--            <columnOverride column="create_date" javaType="java.sql.Timestamp"/>-->
<!--            <columnOverride column="update_date" javaType="java.sql.Timestamp"/>-->
<!--        </table>-->
<!--        <table tableName="sale_goods_alter_log" domainObjectName="SaleGoodsAlterLog"-->
<!--               enableCountByExample="true" enableUpdateByExample="true"-->
<!--               enableDeleteByExample="true" enableSelectByExample="true"-->
<!--               enableSelectByPrimaryKey="true">-->
<!--            <columnOverride column="create_date" javaType="java.sql.Timestamp"/>-->
<!--            <columnOverride column="update_date" javaType="java.sql.Timestamp"/>-->
<!--        </table>-->
<!--        <table tableName="sale_order_invoice" domainObjectName="SaleOrderInvoice"-->
<!--               enableCountByExample="true" enableUpdateByExample="true"-->
<!--               enableDeleteByExample="true" enableSelectByExample="true"-->
<!--               enableSelectByPrimaryKey="true">-->
<!--            <columnOverride column="create_date" javaType="java.sql.Timestamp"/>-->
<!--            <columnOverride column="update_date" javaType="java.sql.Timestamp"/>-->
<!--        </table>-->
    </context>
</generatorConfiguration>