spring:
  application:
    name: biz-dm
  cloud:
    nacos:
      discovery:
        server-addr: sys-nacos:8848
        username: nacos
        password: PnRX<2brZ*w)
        namespace: dm
#        namespace: dm-dev
      config:
        server-addr: sys-nacos:8848
        username: nacos
        password: PnRX<2brZ*w)
        namespace: dm
        file-extension: yml
        group: FAWKES_SYS_GROUP
        prefix: biz-dm
        extension-configs:
          - data-id: common-core.yml
            group: FAWKES_SYS_GROUP
            refresh: true
          - data-id: common-jasypt.yml
            group: FAWKES_SYS_GROUP
            refresh: true
          - data-id: common-log.yml
            group: FAWKES_SYS_GROUP
            refresh: true
          - data-id: common-datasource.yml
            group: FAWKES_SYS_GROUP
            refresh: true
          - data-id: common-redis.yml
            group: FAWKES_SYS_GROUP
            refresh: true
          - data-id: common-rabbitmq.yml
            group: FAWKES_SYS_GROUP
            refresh: true