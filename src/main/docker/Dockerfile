FROM adoptopenjdk/openjdk8-openj9:ubuntu

# 环境变量
ENV WORK_PATH /home/<USER>/fawkes
ENV APP_NAME biz_dm-encrypted.jar
#ENV APP_NAME biz_dm.jar
ENV APP_VERSION @project.version@

EXPOSE 11410
#USER
#USER user:group
#VOLUME
VOLUME ["/home/<USER>", "/tmp/data"]
#ADD
#COPY
COPY $APP_NAME $WORK_PATH/
#LABEL
#STOPSIGNAL
#ARG
#ONBUILD
# WORKDIR
WORKDIR $WORK_PATH
RUN ln -sf /usr/share/zoneinfo/Asia/Shanghai /etc/localtime
RUN echo 'Asia/Shanghai' >/etc/timezone

# ENTRYPOINT
ENTRYPOINT ["java","-Djava.security.egd=file:/dev/./urandom"]

#CMD ["-jar","biz_dm.jar"]
CMD ["-javaagent:biz_dm-encrypted.jar='-pwd #'","-jar","biz_dm-encrypted.jar"]