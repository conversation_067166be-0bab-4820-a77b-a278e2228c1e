# DM 订单管理系统后台

## 介绍
技术框架：[凤翎 2.1.0](https://fawkes.cybereng.com/help/doc/ham9jlceap76/han00t46klbg?activeTab=1&version=2.1.0)





## 设置本地host
C:\Windows\System32\drivers\etc



## 部署

### Build Steps
```shell
mvn clean package -e -Dmaven.test.skip=true -Pdev
cp -r ./src/main/docker/ ./target
mv ./target/*.jar ./target/docker/
cd ./target/docker/
```

### Send build artifacts over SSH (Exec command)
```shell
cd  /data/dm/mnt/docker_end
docker build -t xinpu/back-biz-dm:latest /data/dm/mnt/docker_end/
docker kill back-biz-dm
docker rm back-biz-dm
docker-compose -f /data/dm/mnt/back_biz_dm.yml up -d
```

## 备注
- [如何解决Nacos服务下线报错问题](https://www.jb51.net/program/325094n3p.htm)